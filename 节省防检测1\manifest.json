{"manifest_version": 3, "name": "New Relic Bypass", "version": "1.0", "description": "Bypass New Relic monitoring script with network request blocking", "permissions": ["activeTab", "scripting", "declarativeNetRequest", "declarativeNetRequestFeedback"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}}