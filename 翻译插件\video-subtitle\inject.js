(()=>{var u={BUILD_TIME:"2025-04-29T07:31:40.000Z",VERSION:"1.16.8",PROD:"1",REDIRECT_URL:"https://dash.immersivetranslate.com/auth-done/",PROD_API:"1",BETA:"0",userscript_domains:'["google.com","translate.googleapis.com","api-edge.cognitive.microsofttranslator.com","edge.microsoft.com","transmart.qq.com","translate.yandex.net","tmt.tencentcloudapi.com","www2.deepl.com","w.deepl.com","immersive-translate.owenyoung.com","generativelanguage.googleapis.com","chat.openai.com","bing.com","www.bing.com","open.volcengineapi.com","fanyi.baidu.com","api.fanyi.baidu.com","api.interpreter.caiyunai.com","api-free.deepl.com","api.deepl.com","api.openl.club","openapi.youdao.com","translate.volcengine.com","api.niutrans.com","immersivetranslate.com","test-api2.immersivetranslate.com","api2.immersivetranslate.com","config.immersivetranslate.com","app.immersivetranslate.com","dash.immersivetranslate.com","api.immersivetranslate.com","immersive-translate.deno.dev","www.googleapis.com","www.google-analytics.com","translate-pa.googleapis.com","api.cognitive.microsofttranslator.com","api.groq.com","api.x.ai","api.papago-chrome.com","api.openai.com","api.interpreter.caiyunai.com","api.cognitive.microsofttranslator.com","aidemo.youdao.com","dict.youdao.com","openai.azure.com","mt.aliyuncs.com","subhub.weixin.so","api.anthropic.com","localhost","127.0.0.1","ai.immersivetranslate.com","test-ai.immersivetranslate.com","openrouter.ai","dashscope.aliyuncs.com","api.deepseek.com","aip.baidubce.com","ark.cn-beijing.volces.com","hunyuan.tencentcloudapi.com","public-beta-api.siliconflow.cn","api.siliconflow.cn","open.bigmodel.cn","store.immersivetranslate.com"]',MOCK:"0",DEBUG:"0",INSTALL_FROM:"chrome_zip"};var y=class{from;to;constructor(e){this.from=e.from,this.to=e.to}sendMessages(e){let n={type:e.type,data:e.data,id:e.id||this.getRandomId(),isAsync:!1};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(n)}))}getRandomId(){return Math.random()*1e17+new Date().getTime()}sendAsyncMessages({type:e,data:n}){return new Promise(t=>{let a=this.handleMessages(T=>{T.id===l&&(t(T.data),a())}),l=this.getRandomId(),g={type:e,data:n,id:l,isAsync:!0};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(g)}))})}handleMessageOnce(e){return new Promise(n=>{let t=this.handleMessage(e,a=>{n(a.data),t()})})}handleMessage(e,n){return this.handleMessages(t=>{t.type===e&&n(t)})}handleMessages(e){let n=t=>{let l=JSON.parse(t.detail||"{}");l&&typeof l=="object"&&e(l)};return globalThis.document.addEventListener(this.from,n),()=>{globalThis.document.removeEventListener(this.from,n)}}},W={get(r,e,n){return e in r?(...t)=>{let a=r[e];return typeof a=="function"?a.apply(r,t):Reflect.get(r,e,n)}:t=>r.sendAsyncMessages({type:e,data:t})}};var V="imt-subtitle-event-to-content-script",J="imt-subtitle-event-to-inject",ae=new y({from:V,to:J}),b=new y({from:J,to:V}),P=new Proxy(b,W),Re=new Proxy(ae,W);function R(r){if(!r)return null;try{let e=r;return r.startsWith("//")?e=globalThis.location.protocol+r:r.startsWith("/")?e=`${globalThis.location.protocol}//${globalThis.location.host}${r}`:r.startsWith("http")||(e=`${globalThis.location.protocol}//${r}`),new URL(e).toString()}catch(e){return console.error(e),r}}var i=class{content=P;config;constructor(e){this.config=e,b.handleMessages(async({type:n,id:t,data:a})=>{let l=this[n];if(!l)return;let g=l.apply(this,[a]);g instanceof Promise&&(g=await g),b.sendMessages({id:t,data:g})})}triggerSubtitle(e){}async translateSubtitle(e){let n=await this.content.requestSubtitle({url:R(e._url)});if(n){if(this.config.responseType=="document"){let a=new DOMParser().parseFromString(n,"text/xml");Object.defineProperty(e,"responseXML",{value:a,writable:!1}),Object.defineProperty(e,"response",{value:a,writable:!1});return}let t=n;(e.responseType=="arraybuffer"||this.config.responseType=="arraybuffer")&&typeof n=="string"&&(t=new TextEncoder().encode(n).buffer),Object.defineProperty(e,"responseText",{value:t,writable:!1}),Object.defineProperty(e,"response",{value:t,writable:!1})}}async translateSubtitleWithResponse(e,n){return await this.content.requestSubtitle({url:R(e),responseText:n})}startRequestSubtitle(e){this.content.startRequestSubtitle({url:R(e)})}subtitleRequestError(e){this.content.subtitleRequestError({...e,url:R(e.url)})}async isOnlyResponse(){return this.config.hookType.includes("xhr_response")}async translateSubtitleWithFetch(e,n){let t={...n},a;return typeof e=="string"?a={url:e,method:"GET",headers:{}}:a=await ie(e),t?.body&&(t.body=z(t.body)),this.content.requestSubtitle({fetchInfo:JSON.stringify({input:a,options:t})})}async getVideoMeta(e){}isSubtitleRequest(e){return!this.config||!this.config.subtitleUrlRegExp||!e?!1:new RegExp(this.config.subtitleUrlRegExp).test(e||"")}};function ie(r){if(r instanceof URL)return{url:r.href,method:"GET",headers:{}};let e=r.clone(),n={url:r.url,method:r.method,headers:Object.fromEntries(r.headers.entries())};if(e.body){let t=z(e.body);if(e.body!==t)return e.text().then(a=>(n.body=a,n));n.body=t}return Promise.resolve(n)}function z(r){if(!r)return r;if(r instanceof FormData||r instanceof URLSearchParams){let e={};for(let[n,t]of r.entries())e[n]=t;return e._formatBodyType="FormData",e}return r}var C=class extends i{timer=null;triggerSubtitle({force:e}){setTimeout(()=>{if(this.config?.subtitleButtonSelector){let n=document.querySelector(this.config.subtitleButtonSelector);if(n){let t=n.getAttribute("aria-pressed")==="true";t&&e?(n.click(),setTimeout(()=>{n.click()},100)):t||n.click();return}}if(this.config?.videoPlayerSelector){let n=document.querySelector(this.config.videoPlayerSelector);n?.toggleSubtitles(),setTimeout(()=>{n?.toggleSubtitles()},100)}},1e3)}async getVideoMeta(){if(!this.config.videoPlayerSelector)return null;try{return await this.sleep(100),document.querySelector(this.config.videoPlayerSelector)?.getPlayerResponse()}catch{return null}}async isOnlyResponse(){let e=await super.isOnlyResponse();return!e||(await this.getVideoMeta())?.videoDetails?.isLive?!1:e}getCurrentTime(){try{return this.config.videoPlayerSelector?document.querySelector(this.config.videoPlayerSelector)?.getCurrentTime():null}catch{return null}}sleep(e){return new Promise(n=>{setTimeout(()=>{n(null)},e)})}};var v=class extends i{timer=null;videoMeta={};lastVideoMeta=null;constructor(e){super(e),this.hookJSON()}hookJSON(){let e=JSON.parse;JSON.parse=n=>{let t=e(n);try{t&&t.result&&t.result.timedtexttracks&&t.result.movieId&&(this.videoMeta[t.result.movieId]=t.result,this.lastVideoMeta=t.result)}catch(a){console.log(a)}return t}}getVideoMeta(e){return this.lastVideoMeta}};var I=class extends i{timer=null;videoMeta={};constructor(e){super(e),this.hookJSON()}hookJSON(){let e=JSON.parse;JSON.parse=n=>{let t=e(n);try{t?.asset?.captions?.length?this.videoMeta[t.id]=t?.asset:t?.previews&&t?.course&&t?.previews?.forEach(a=>{this.videoMeta[a.id]=a})}catch(a){console.error(a)}return t}}getVideoMeta(e){return this.videoMeta[e]}};var L=class extends i{timer=null;videoMeta={};constructor(e){super(e),this.hookJSON()}hookJSON(){let e=JSON.parse;JSON.parse=n=>{let t=e(n);try{if(t?.stream?.sources?.length&&t?.stream?.sources[0]?.complete?.url){let a=window.location.pathname.split("/");a.length>2&&a[a.length-2]==="video"&&(this.videoMeta[a[a.length-1]]=t.stream.sources[0].complete.url)}}catch(a){console.error(a)}return t}}getVideoMeta(e){return this.videoMeta[e]}};var A=class extends i{constructor(e){super(e)}async translateSubtitleWithFetch(e,n){this.main(e,n)}async main(e,n){let t=globalThis.__originalFetch;if(!t)return;let a=e;e instanceof Request&&(a=e.clone());let l=await t(a,n);if(!l.ok)return;let g=await l.json();g.transcripts_urls&&this.requestSubtitle(g.transcripts_urls)}async requestSubtitle(e){await m(),await this.content.requestSubtitle(e)}};var k=class extends i{constructor(e){super(e)}lang="";async translateSubtitleWithFetch(e,n){this.main(e,n)}async main(e,n){let t=globalThis.__originalFetch;if(!t)return;let a=this.getUrl(e);return/textstream_/.test(a)?this.parseLang(a):this.parseAllSubs(e,n,t)}getUrl(e){return e.toString()}async parseLang(e){let t=e.match(/textstream_(\w+)=/)?.[1];return!t||t==this.lang||(this.lang=t,await m(),this.content.changeLang(t)),null}async parseAllSubs(e,n,t){if(!t)return;let a=e;e instanceof Request&&(a=e.clone());let l=await t(a,n);if(!l.ok)return;let g=await l.json();g.text_track_urls&&this.requestSubtitle(g.text_track_urls)}async requestSubtitle(e){await m(),await this.content.requestSubtitle(e)}};var{Deno:Y}=globalThis,se=typeof Y?.noColor=="boolean"?Y.noColor:!0,le=!se;function O(r,e){return{open:`\x1B[${r.join(";")}m`,close:`\x1B[${e}m`,regexp:new RegExp(`\\x1b\\[${e}m`,"g")}}function w(r,e){return le?`${e.open}${r.replace(e.regexp,e.open)}${e.close}`:r}function U(r){return w(r,O([2],22))}function D(r){return w(r,O([31],39))}function B(r){return w(r,O([32],39))}function $(r){return w(r,O([33],39))}var Xe=new RegExp(["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|"),"g");function _(){return typeof process>"u"&&typeof Deno<"u"?Deno.env.toObject():u}var H=_();function M(){return H.PROD==="1"}function F(){return H.PROD_API==="1"}function X(){if(H.IMMERSIVE_TRANSLATE_SAFARI==="1")return!0;if(typeof globalThis.immersiveTranslateBrowserAPI<"u"&&globalThis.immersiveTranslateBrowserAPI.runtime&&globalThis.immersiveTranslateBrowserAPI.runtime.getManifest){let e=globalThis.immersiveTranslateBrowserAPI.runtime.getManifest();return!!(e&&e._isSafari)}else return!1}var tt=_().PROD==="1",nt=_().PROD!=="1";var o="immersiveTranslate",d="Immersive Translate",s="immersive-translate",Q="imt",ue="immersivetranslate";var p="immersivetranslate.com",ce=`https://config.${p}/`,st=`https://app.${p}/`,c=M()||F()?`https://${p}/`:`https://test.${p}/`,N=`https://dash.${p}/`,lt=M()||F()?`https://api2.${p}/`:`https://test-api2.${p}/`,gt=M()||F()?`https://ai.${p}/`:`https://test-ai.${p}/`,ut=`https://assets.${ue}.cn/`,Z=c+"accounts/login?from=plugin",ee=c+"profile/",f=c+"auth/pricing/",x=c+"pricing/";X()&&(f=c+"accounts/safari-iap/",x=c+"accounts/safari-iap/");var te=`https://github.com/${s}/${s}/`,ct=`https://s.${p}/`,pt=`https://onboarding.${p}/`;var mt=o+"DeeplGlobalState",dt=o+"BingGlobalState",bt=o+"YandexGlobalState",ft=o+"BaiduQianfanGlobalConfigStorageKey",xt=o+"SiliconCloudGlobalConfigStorageKey",ht=o+"ZhipuGlobalConfigStorageKey";var Tt=o+"GoogleAccessToken",St=o+"AuthFlow",yt=s+"-config-latest.json",Rt=o+"AuthState",_t=o+"IframeMessage",Mt=o+"WaitForRateLimit",Et=o+"DocumentMessageAsk",Pt=o+"DocumentMessageTellThirdParty",Ct=o+"showError",vt=o+"showModal",It=o+"showToast",Lt=o+"DocumentMessageThirdPartyTell",At=o+"DocumentMessageEventUpload",kt=o+"DocumentMessageTypeStopJsSDK",Ot=o+"DocumentMessageHandler",wt=o+"DocumentSetFloatBallActive",Ut=`${o}Share`,Dt=`${o}ShowFloatBallGuide`,Bt=o+"DocumentMessageTempEnableSubtitleChanged",Ft=o+"DocumentMessageUpdateQuickButtonAiSubtitle",Nt=`${o}ToggleMouseHoverTranslateDirectly`,Gt=`${o}ReqDraft`,qt=`${o}ResDraft`,Wt=`${o}Container`,$t=`${o}SpecifiedContainer`;var Ht=`${o}PageTranslatedStatus`,Kt=`${o}MangaTranslatedStatus`,jt=`${o}PageUrlChanged`,Vt=`${o}ReceiveCommand`,Jt=o+"LastUseMouseHoverTime",zt=o+"LastUseInputTime",Yt=o+"LastUseManualTranslatePageTime",Xt=`${o}PopupReceiveMessage`,Qt=o+"DocumentMessageEventTogglePopup",Zt=`${ce}default_config.json`,en=`${o}Mark`,tn=`${o}Root`,nn=`${o}Walked`,rn=`data-${s}-walked`,on=`${o}Paragraph`,an=`data-${s}-paragraph`,sn=`data-${s}-translation-element-mark`,ln=`${o}TranslationElementMark`,gn=`${o}TranslatedMark`,un=`${s}-input-injected-css`,cn=`${o}LoadingId`,pn=`data-${s}-loading-id`,mn=`${o}ErrorId`,dn=`data-${s}-error-id`,bn=`${o}AtomicBlockMark`,fn=`${o}ExcludeMark`,xn=`data-${s}-exclude-mark`,hn=`${o}StayOriginalMark`,Tn=`${o}PreWhitespaceMark`,Sn=`${o}InlineMark`,yn=`${o}BlockMark`,Rn=`${o}Left`,_n=`${o}Right`,Mn=`${o}Width`,En=`${o}Height`,Pn=`${o}Top`,Cn=`${o}FontSize`;var vn=`${o}GlobalStyleMark`;var In=`${s}-target-wrapper`,Ln=`${s}-pdf-target-container`,An=`${s}-target-inner`,kn=`${s}-source-wrapper`,On=`${s}-target-translation-block-wrapper`,wn=`${s}-root-translation-theme`,Un=`${o}RootTranslationTheme`,Dn=`${s}-target-translation-vertical-block-wrapper`,Bn=`${s}-target-translation-pdf-block-wrapper`,Fn=`${s}-target-translation-pre-whitespace`,Nn=`${s}-target-translation-inline-wrapper`;var Gn=["https://immersive-translate.owenyoung.com/options/","https://immersive-translate.owenyoung.com/auth-done/",N,N+"auth-done/","http://localhost:8000/dist/userscript/options/","http://localhost:8000/auth-done/","http://************:8000/dist/userscript/options/","http://*************:8000/dist/userscript/options/","http://************:8000/dist/userscript/options/","https://www.deepl.com/translator","translate.google.com","http://localhost:8000/options/","http://************:8000/options/","http://*************:8000/options/","http://************:8000/options/"];var qn=c+"docs/communities/",Wn=te+"issues/1809",$n=te+"issues/1179",Hn={type:o+"ChildFrameToRootFrameIdentifier"};var Kn=M()?N+"#general":"http://localhost:8000/dist/userscript/options/#general";var pe=N+"#general",jn=c+"accounts/login?from=plugin&return_url="+encodeURIComponent(pe),Vn=Z+"&utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Jn=Z+"&utm_source=extension&utm_medium=extension&utm_campaign=manga_guide",me=c+"download/",de=c+"topup?type=open_ai&",be=c+"topup?type=deepl&",zn=c+"topup?type=comics&",Yn=x+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_more",Xn=x+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_guide",Qn=me+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",Zn=x+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_footer",er=x+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",tr=x+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",nr=ee+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",rr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=subtitle_download",or=de+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",ar=be+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",ir=c+"topup?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",sr=x+"?utm_source=extension&utm_medium=extension&utm_campaign=option_sync_config",lr=ee+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal&upgradeFromTrial=true",gr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_intro",ur=f+"?utm_source=extension&utm_medium=extension&utm_campaign=image_intro",cr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=image_client",pr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=yt_ai_asr",mr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=",dr=c+"docs/usage/",br=c+"docs/communities/",E=_().TRANSLATE_FILE_URL,fr=E+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",xr=E+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",hr=`${E}download-subtitle/`,Tr=`${E}pdf-pro/`,Sr=`${E}text/`;var yr=c+"docs/usage/";var Rr=`https://analytics.${p}/collect`,_r=`https://analytics.${p}/internal`,Mr=`${c}activities/components/image-pro`;var Er=50*1e4,Pr=`[${Q}-ctx-divider]`,Cr=`${Q}_context_preview`;var h=console,K=class{#e=performance.now();reset(){this.#e=performance.now()}stop(e){let n=performance.now(),t=Math.round(n-this.#e),a=B;t>1e4?a=D:t>1e3&&(a=$),h.debug(U(d+" TIMING:"),e,"in",a(t+"ms")),this.#e=n}},j=class{#e=1;get level(){return this.#e}setLevel(e){switch(e){case"debug":this.#e=0;break;case"info":this.#e=1;break;case"warn":this.#e=2;break;case"error":this.#e=3;break;case"fatal":this.#e=4;break}}debug(...e){this.#e<=0&&h.log(U(d+" DEBUG:"),...e)}v(...e){this.#e<=0&&console.log(U(d+" VERBOSE:"),...e)}info(...e){this.#e<=1&&h.log(B(d+" INFO:"),...e)}l(...e){this.#e<=1&&console.log(B(d+" TEMP INFO:"),...e)}warn(...e){this.#e<=2&&h.warn($(d+" WARN:"),...e)}error(...e){this.#e<=3&&h.error(D(d+" ERROR:"),...e)}fatal(...e){this.#e<=4&&h.error(D(d+" FATAL:"),...e)}timing(){return this.level===0?new K:{reset:()=>{},stop:()=>{}}}},ne=new j;var re={hookRequest:()=>{}};async function fe(){let r=await b.sendAsyncMessages({type:"getConfig"});if(!r)return;let n={youtube:C,netflix:v,webvtt:i,khanacademy:i,udemy:I,general:i,ebutt:i,hulu:A,mubi:k,disneyplus:L,"fmp4.xml":i,multi_attach_vtt:i,twitter:i,subsrt:i,xml:i,text_track_dynamic:i,av:i}[r.type||""];if(!n)return;let t=new n(r);re.hookRequest(t,r)}re.hookRequest=(r,e)=>{if(e.hookType.includes("xhr")){let n=XMLHttpRequest.prototype.open,t=XMLHttpRequest.prototype.send,a=function(){return this._url=arguments[1],n.apply(this,arguments)},l=async function(){let g=this._url,T=r.isSubtitleRequest(g);return!g||!T?t.apply(this,arguments):(await r.isOnlyResponse()?(r.startRequestSubtitle(g),this.onreadystatechange=async()=>{let S=XMLHttpRequest.DONE;typeof S>"u"&&(S=4),this.readyState===S&&this.status===429&&r.subtitleRequestError({url:g,responseStatus:this.status}),this.readyState===S&&this.status===200&&await m()&&r.translateSubtitleWithResponse(g,this.responseText)}):await m()&&await r.translateSubtitle(this),t.apply(this,arguments))};Object.defineProperty(XMLHttpRequest.prototype,"open",{value:a,writable:!0}),Object.defineProperty(XMLHttpRequest.prototype,"send",{value:l,writable:!0})}if(e.hookType.includes("fetch")){let n=globalThis.fetch;globalThis.__originalFetch=n,globalThis.fetch=async function(t,a){let l=typeof t=="string"?t:t.url||t.href;if(!r.isSubtitleRequest(l))return n(t,a);if(await m()){let q=await r.translateSubtitleWithFetch(t,a);return q?new Response(q):n(t,a)}return n(t,a)}}};var G=!1;function m(){if(!G){let r=b.handleMessageOnce("contentReady").then(()=>(G=!0,!0));return P.isContentReady(),Promise.race([r,new Promise(e=>{setTimeout(()=>{G||ne.warn("waitPluginDone timeout"),e(!1)},5e3)})])}return Promise.resolve(G)}m();fe();})();
