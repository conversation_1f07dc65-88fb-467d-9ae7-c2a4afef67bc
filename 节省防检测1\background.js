chrome.runtime.onInstalled.addListener(() => {
applyRules();
});

function applyRules() {
const newRule = {
id: 2,
priority: 1,
action: { type: "block" },
condition: {
urlFilter: "*://*.example.net/*",
resourceTypes: [
"main_frame", 
"sub_frame", 
"stylesheet", 
"script", 
"image", 
"font", 
"object", 
"xmlhttprequest", 
"ping", 
"csp_report", 
"media", 
"websocket", 
"webtransport", 
"webbundle", 
"other"
]
}
};

// 先移除所有现有的动态规则
chrome.declarativeNetRequest.getDynamicRules((existingRules) => {
const existingRuleIds = existingRules.map(rule => rule.id);

chrome.declarativeNetRequest.updateDynamicRules({
removeRuleIds: existingRuleIds,
addRules: [newRule]
}, () => {
if (chrome.runtime.lastError) {
console.error("规则更新失败:", chrome.runtime.lastError);
} else {
console.log("规则更新成功");
}
});
});

// 验证规则集是否启用
chrome.declarativeNetRequest.getEnabledRulesets((rulesets) => {
console.log("已启用的规则集:", rulesets);
});

// 添加规则更新状态监听
chrome.declarativeNetRequest.onRuleMatchedDebug?.addListener((info) => {
console.log("规则匹配:", info);
});
}