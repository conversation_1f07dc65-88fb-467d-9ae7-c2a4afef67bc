import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

base_url = "https://start.lifepointspanel.com/aff_c?aff_id={aff_id}&offer_id=1778&aff_unique1=xxxxx&aff_sub2=xxxxx&lang=xx&country=xx"

headers = {
    "Host": "start.lifepointspanel.com",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
    "Connection": "keep-alive",
}

start_affid = 1000
end_affid = 2000
max_workers = 20  # 可根据实际情况调整

def test_aff_id(aff_id):
    url = base_url.format(aff_id=aff_id)
    try:
        response = requests.get(url, headers=headers, allow_redirects=False, timeout=8)
        if 'Location' in response.headers:
            return f"aff_id: {aff_id}, Location: {response.headers['Location']}\n"
    except Exception as e:
        pass
    return None

with ThreadPoolExecutor(max_workers=max_workers) as executor, open('offer_id=1778.txt', 'w', encoding='utf-8') as f:
    futures = [executor.submit(test_aff_id, aff_id) for aff_id in range(start_affid, end_affid + 1)]
    for future in as_completed(futures):
        result = future.result()
        if result:
            f.write(result)
            print(result.strip())