import { formatTime } from './utils.js';

// 创建面板元素
function createPanel() {
  const panel = document.createElement('div');
  panel.id = "secureAutoAnswerPanel";
  panel.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  panel.style.cssText = `
    position: fixed;
    top: 100px;
    right: 0;
    background: white;
    padding: 8px;
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 2px 10px rgba(0,0,0,0.1);
    z-index: 99999;
    display: flex;
    flex-direction: column;
    gap: 6px;
    transition: height 0.3s ease;
    width: 120px;
  `;
  return panel;
}

// 创建折叠按钮
function createCollapseButton() {
  const collapseBtn = document.createElement('button');
  collapseBtn.textContent = "▼";
  collapseBtn.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  collapseBtn.style.cssText = `
    padding: 2px 6px;
    background: #e0e0e0;
    color: #333;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
    margin-bottom: 4px;
    align-self: flex-end;
  `;
  return collapseBtn;
}

// 创建计时器显示
function createTimerDisplay() {
  const timerDisplay = document.createElement('div');
  timerDisplay.id = "timerDisplay";
  timerDisplay.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  timerDisplay.textContent = "未检测\n00:00";
  timerDisplay.style.cssText = `
    font-size: 12px;
    color: #333;
    text-align: center;
    margin-bottom: 4px;
    white-space: pre-wrap;
  `;
  return timerDisplay;
}

// 创建自动提交开关
function createAutoSubmitSwitch() {
  const container = document.createElement('label');
  container.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  container.style.cssText = `
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #333;
    margin-bottom: 4px;
    cursor: pointer;
  `;

  const switchInput = document.createElement('input');
  switchInput.type = "checkbox";
  switchInput.id = "autoSubmitToggle";
  switchInput.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  switchInput.style.cssText = `
    display: none;
  `;

  const switchSlider = document.createElement('span');
  switchSlider.className = "switch-slider";
  // ... 续接上面的部分

  switchSlider.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  switchSlider.style.cssText = `
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    background-color: #ccc;
    border-radius: 10px;
    transition: background-color 0.3s;
    cursor: pointer;
  `;

  const switchKnob = document.createElement('span');
  switchKnob.className = "switch-knob";
  switchKnob.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  switchKnob.style.cssText = `
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  `;

  switchSlider.appendChild(switchKnob);
  container.appendChild(switchInput);
  container.appendChild(switchSlider);
  container.appendChild(document.createTextNode(' 自动提交'));

  return {
    container,
    input: switchInput,
    slider: switchSlider,
    knob: switchKnob
  };
}

// 创建模拟选择按钮
function createSelectOnlyButton() {
  const button = document.createElement('button');
  button.textContent = "🔒 模拟";
  button.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  button.style.cssText = `
    padding: 4px 8px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: 0.2s;
    width: 100%;
  `;
  return button;
}

// 创建快速提交按钮
function createQuickSubmitButton() {
  const button = document.createElement('button');
  button.textContent = "⚡ 快速";
  button.setAttribute('data-auto-answer-ui', 'true'); // 添加UI标记
  button.style.cssText = `
    padding: 4px 8px;
    background: #FF9800;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: 0.2s;
    width: 100%;
  `;
  return button;
}

export {
  createPanel,
  createCollapseButton,
  createTimerDisplay,
  createAutoSubmitSwitch,
  createSelectOnlyButton,
  createQuickSubmitButton
};