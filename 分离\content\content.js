import { handleUniversalQuestions, handleAutoSubmit } from './quiz-handler.js';
import { 
  createPanel, 
  createCollapseButton, 
  createTimerDisplay, 
  createAutoSubmitSwitch,
  createSelectOnlyButton,
  createQuickSubmitButton
} from './ui-components.js';
import { startTimerUpdates, setupMessageListener } from './timer-display.js';

// 主程序入口
function initializeExtension() {
  // 检查是否为iframe内容
  if (window.top !== window.self) {
    return;
  }
  
  // 防止重复初始化
  if (window.secureAutoAnswerInjected) {
    return;
  }
  window.secureAutoAnswerInjected = true;
  
  // 创建UI元素
  const panel = createPanel();
  const collapseBtn = createCollapseButton();
  const timerDisplay = createTimerDisplay();
  const autoSubmitSwitch = createAutoSubmitSwitch();
  const selectOnlyBtn = createSelectOnlyButton();
  const quickSubmitBtn = createQuickSubmitButton();
  
  // 添加元素到面板
  panel.appendChild(collapseBtn);
  panel.appendChild(timerDisplay);
  panel.appendChild(autoSubmitSwitch.container);
  panel.appendChild(selectOnlyBtn);
  panel.appendChild(quickSubmitBtn);
  document.body.appendChild(panel);
  
  // 折叠功能
  let isCollapsed = false;
  const originalHeight = panel.offsetHeight;
  collapseBtn.addEventListener('click', () => {
    if (isCollapsed) {
      panel.style.height = `${originalHeight}px`;
      collapseBtn.textContent = "▼";
      timerDisplay.style.display = 'block';
      autoSubmitSwitch.container.style.display = 'flex';
      selectOnlyBtn.style.display = 'block';
      quickSubmitBtn.style.display = 'block';
    } else {
      panel.style.height = `${collapseBtn.offsetHeight + 16}px`;
      collapseBtn.textContent = "▲";
      timerDisplay.style.display = 'none';
      autoSubmitSwitch.container.style.display = 'none';
      selectOnlyBtn.style.display = 'none';
      quickSubmitBtn.style.display = 'none';
    }
    isCollapsed = !isCollapsed;
  });
  
  // 加载自动提交开关状态
  chrome.storage.local.get(['autoSubmitEnabled'], (data) => {
    const isEnabled = data.autoSubmitEnabled || false;
    autoSubmitSwitch.input.checked = isEnabled;
    if (isEnabled) {
      autoSubmitSwitch.slider.style.backgroundColor = '#2196F3';
      autoSubmitSwitch.knob.style.transform = 'translateX(20px)';
    } else {
      autoSubmitSwitch.slider.style.backgroundColor = '#ccc';
      autoSubmitSwitch.knob.style.transform = 'translateX(0)';
    }
  });
  
  // 监听开关变化并存储
  autoSubmitSwitch.input.addEventListener('change', (event) => {
    const isEnabled = event.target.checked;
    chrome.storage.local.set({ autoSubmitEnabled: isEnabled });
    
    if (isEnabled) {
      autoSubmitSwitch.slider.style.backgroundColor = '#2196F3';
      autoSubmitSwitch.knob.style.transform = 'translateX(20px)';
    } else {
      autoSubmitSwitch.slider.style.backgroundColor = '#ccc';
      autoSubmitSwitch.knob.style.transform = 'translateX(0)';
    }
  });
  
  // "模拟选择"按钮功能
  selectOnlyBtn.addEventListener('click', async () => {
    try {
      await handleUniversalQuestions(false);
      chrome.storage.local.get(['autoSubmitEnabled'], (data) => {
        if (data.autoSubmitEnabled) {
          handleAutoSubmit(false);
        }
      });
    } catch (error) {
      console.log('模拟选择过程中出错:', error);
    }
  });
  
  // "快速模式"按钮功能
  quickSubmitBtn.addEventListener('click', async () => {
    try {
      await handleUniversalQuestions(true);
      chrome.storage.local.get(['autoSubmitEnabled'], (data) => {
        if (data.autoSubmitEnabled) {
          handleAutoSubmit(true);
        }
      });
    } catch (error) {
      console.log('快速提交过程中出错:', error);
    }
  });
  
  // 在全局添加点击事件监听，捕获 token
  document.addEventListener('click', function(event) {
    const link = event.target.closest('a[href*="token="]');
    if (link) {
      const url = new URL(link.href);
      const token = url.searchParams.get('token');
      if (token) {
        chrome.storage.local.set({ capturedToken: token }, () => {
          console.log('已捕获链接中的token:', token);
        });
      }
    }
  }, true);
  
  // 启动计时器更新
  const timerId = startTimerUpdates(timerDisplay);
  
  // 设置消息监听器
  setupMessageListener(timerDisplay);
  
  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    clearInterval(timerId);
  });
}

// 初始化插件
document.addEventListener('DOMContentLoaded', initializeExtension);

// 如果DOM已经加载完成，直接初始化
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  initializeExtension();
}