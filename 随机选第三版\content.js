(function() {
    'use strict';

    // ===== 配置和常量 =====
    const DOMAINS = {
        KTRMR: 'ktrmr.com',
        NFIELD: 'nfieldmr.com',
        QUALTRICS: 'qualtrics.com',
        JIBUNU: 'jibunu.com',
        CMIX: 'cmix.com',
        CONFIRMIT_EU: 'confirmit.eu',
        CONFIRMIT_COM: 'confirmit.com',
        IPSOS: 'ipsosinteractive.com',
        TELECOM: 'mytelecomsurvey.com',
        LIFEPOINTS: 'lifepointspanel.com',
        INSIGHT: 'insightexpress.com',
        HARRIS: 'harrisinsights.com',
        DECIPHER: 'decipherinc.com',
        BMS: 'bms-research.de',
        VIPVOICE: 'vipvoice.com'
    };

    const VALUE_BLACKLIST = ["NA", "REF", "DK", "REFUSED", "DONTKNOW", "Other", "Other1", "Other2", "Other3", ""];
    
    const hostname = window.location.hostname;

    // ===== UI组件 =====
    const UI = {
        init() {
            this.createPanel();
            this.createTopBanner();
            this.initializeEventListeners();
            document.body.appendChild(this.panel);
            document.body.appendChild(this.topBanner);
        },

        createPanel() {
            this.panel = document.createElement('div');
            this.panel.id = "secureAutoAnswerPanel";
            this.panel.setAttribute('data-auto-answer-ui', 'true');
            
            // 创建Shadow DOM
            this.shadowRoot = this.panel.attachShadow({ mode: 'closed' });
            this.shadowHost = document.createElement('div');
            this.shadowRoot.appendChild(this.shadowHost);
            
            this.shadowHost.style.cssText = `
                position: fixed;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                padding: 8px;
                border-radius: 8px 0 0 8px;
                box-shadow: none;
                z-index: 99999;
                display: flex;
                flex-direction: column;
                gap: 6px;
                transition: height 0.3s ease;
                width: auto;
                background: transparent;
            `;

            // 创建切换开关容器
            this.switchContainer = document.createElement('label');
            this.switchContainer.setAttribute('data-auto-answer-ui', 'true');
            this.switchContainer.style.cssText = `
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #333;
                margin-bottom: 4px;
                cursor: pointer;
            `;

            // 创建切换开关
            this.autoAnswerSwitch = document.createElement('input');
            this.autoAnswerSwitch.type = "checkbox";
            this.autoAnswerSwitch.id = "autoAnswerToggle";
            this.autoAnswerSwitch.setAttribute('data-auto-answer-ui', 'true');
            this.autoAnswerSwitch.style.cssText = `
                display: none;
            `;

            // 创建切换开关滑块
            this.switchSlider = document.createElement('span');
            this.switchSlider.className = "switch-slider";
            this.switchSlider.setAttribute('data-auto-answer-ui', 'true');
            this.switchSlider.style.cssText = `
                position: relative;
                display: inline-block;
                width: 40px;
                height: 20px;
                background-color: #ccc;
                border-radius: 10px;
                transition: background-color 0.3s;
                cursor: pointer;
            `;

            // 创建切换开关旋钮
            this.switchKnob = document.createElement('span');
            this.switchKnob.className = "switch-knob";
            this.switchKnob.setAttribute('data-auto-answer-ui', 'true');
            this.switchKnob.style.cssText = `
                position: absolute;
                top: 2px;
                left: 2px;
                width: 16px;
                height: 16px;
                background-color: white;
                border-radius: 50%;
                transition: transform 0.3s;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            `;

            // 组装切换开关组件
            this.switchSlider.appendChild(this.switchKnob);
            this.switchContainer.appendChild(this.autoAnswerSwitch);
            this.switchContainer.appendChild(this.switchSlider);
            this.shadowHost.appendChild(this.switchContainer);

            // 创建快速提交按钮
            this.quickSubmitBtn = document.createElement('button');
            this.quickSubmitBtn.setAttribute('data-auto-answer-ui', 'true');
            
            // 按钮的SVG图标
            const svgIcon = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-bolt">
                    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
                </svg>
            `;
            
            this.quickSubmitBtn.innerHTML = svgIcon;
            this.quickSubmitBtn.style.cssText = `
                padding: 0;
                background: rgb(0, 255, 0);
                color: white;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                font-size: 0;
                transition: 0.2s;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            this.shadowHost.appendChild(this.quickSubmitBtn);
        },

        createTopBanner() {
            this.topBanner = document.createElement('div');
            this.topBanner.id = "topBanner";
            this.topBanner.style.cssText = `
                position: fixed;
                bottom: 8px;
                right: 8px;
                background: transparent;
                padding: 8px 16px;
                border-radius: 8px;
                box-shadow: none;
                z-index: 99999;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: 16px;
                font-size: 14px;
                color: #333;
            `;

            this.timerDisplay = document.createElement('div');
            this.timerDisplay.id = "timerDisplay";
            this.timerDisplay.textContent = "00:00";
            this.timerDisplay.style.cssText = `
                font-weight: bold;
            `;

            this.surveyIdDisplay = document.createElement('div');
            this.surveyIdDisplay.id = "surveyIdDisplay";
            this.surveyIdDisplay.textContent = "";
            this.surveyIdDisplay.style.cssText = `
                font-weight: bold;
            `;

            this.topBanner.appendChild(this.timerDisplay);
            this.topBanner.appendChild(this.surveyIdDisplay);
        },

        initializeEventListeners() {
            this.quickSubmitBtn.addEventListener('click', async () => {
                try {
                    await SurveyAutomation.handleUniversalQuestions(true);
                } catch (error) {
                    Logger.error('快速提交过程中出错:', error);
                }
            });
        },

        updateToggleState(isEnabled) {
            this.autoAnswerSwitch.checked = isEnabled;
            if (isEnabled) {
                this.switchSlider.style.backgroundColor = '#4CAF50';
                this.switchKnob.style.transform = 'translateX(20px)';
            } else {
                this.switchSlider.style.backgroundColor = '#ccc';
                this.switchKnob.style.transform = 'translateX(0)';
            }
        },

        showCountdown(seconds, callback) {
            const countdownOverlay = document.createElement('div');
            countdownOverlay.id = "countdownOverlay";
            countdownOverlay.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px;
                border-radius: 8px;
                font-size: 48px;
                text-align: center;
                z-index: 100000;
            `;
            document.body.appendChild(countdownOverlay);

            let countdown = seconds;
            const countdownInterval = setInterval(() => {
                if (countdown > 0) {
                    countdownOverlay.textContent = countdown;
                    countdown--;
                } else {
                    clearInterval(countdownInterval);
                    document.body.removeChild(countdownOverlay);
                    if (callback) callback();
                }
            }, 1000);
        },

        updateTimer(surveyId, time) {
            if (this.timerDisplay) {
                this.timerDisplay.textContent = `${surveyId || '空闲'}\n${Utils.formatTime(time)}`;
            }
        }
    };

    // ===== 工具函数 =====
    const Utils = {
        formatTime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
        },

        decodeJWT(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                return JSON.parse(jsonPayload);
            } catch (e) {
                Logger.error('JWT解码错误:', e);
                return null;
            }
        },

        getSurveyIdFromUrl() {
            return new Promise((resolve) => {
                chrome.storage.local.get(['capturedToken'], (data) => {
                    if (data.capturedToken) {
                        const decoded = this.decodeJWT(data.capturedToken);
                        let surveyId = decoded?.survey_id || decoded?.surveyId || null;
                        if (surveyId) {
                            const matches = surveyId.match(/\d+/);
                            surveyId = matches ? matches[0] : surveyId;
                        }
                        resolve(typeof surveyId === 'string' ? surveyId : null);
                        chrome.storage.local.remove('capturedToken');
                    } else {
                        resolve(null);
                    }
                });
            });
        },

        isCintReturnPage() {
            if (window.location.href.includes('s.cint.com/survey/return')) {
                return true;
            }
            if (window.location.href.includes('https://app.lifepointspanel.com/')) {
                return true;
            }
            
            // 检测进度文本元素
            const progressElements = document.querySelectorAll('[class*="progress"], [id*="progress"], [data-progress]');
            for (const el of progressElements) {
                const text = el.textContent || el.innerText;
                const match = text.match(/(\d{1,3})%/);
                if (match && parseInt(match[1]) >= 90) {
                    return true;
                }
            }
            
            return false;
        }
    };

    // ===== 日志记录 =====
    const Logger = {
        log(...args) {
            console.log('[AutoSurvey]', ...args);
        },
        
        error(...args) {
            console.error('[AutoSurvey Error]', ...args);
        },
        
        warn(...args) {
            console.warn('[AutoSurvey Warning]', ...args);
        }
    };

    // ===== 问卷自动化引擎 =====
    const SurveyAutomation = {
        // 根据域名获取交互元素
        getInteractiveElements() {
            let selector;
            
            if (hostname.includes(DOMAINS.KTRMR) || 
                hostname.includes(DOMAINS.NFIELD) || 
                hostname.includes(DOMAINS.LIFEPOINTS)) {
                selector = 'input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), input[type="number"]:not([disabled]), select:not([disabled])';
                Logger.log('获取元素模式一');
            } else if (hostname.includes(DOMAINS.CONFIRMIT_EU) || hostname.includes(DOMAINS.CONFIRMIT_COM)) {
                selector = `input[type="radio"]:not([disabled]),
                    input[type="checkbox"]:not([disabled]),
                    input[type="number"]:not([disabled]),
                    [role="radio"]:not([disabled]),
                    [role="checkbox"]:not([disabled]),
                    select:not([disabled])`;
                Logger.log('获取元素模式二');
            } else {
                selector = 'input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), input[type="number"]:not([disabled]), select:not([disabled])';
            }
            
            const allElements = document.querySelectorAll(selector);
            const elements = Array.from(allElements).filter(el => !el.closest('[data-auto-answer-ui]'));
            Logger.log('元素数量:', elements.length);
            return elements;
        },

        // 获取特定域名的分组属性
        getDomainGroupingAttribute() {
            if (hostname.includes(DOMAINS.KTRMR) || hostname.includes(DOMAINS.NFIELD)) 
                return 'questionname';
                
            if (hostname.includes(DOMAINS.IPSOS) || 
                hostname.includes(DOMAINS.CONFIRMIT_EU) || 
                hostname.includes(DOMAINS.CONFIRMIT_COM)) 
                return 'id';
                
            return 'name'; // Default to name grouping
        },

        // 根据域名获取可点击元素
        getClickableElement(element) {
            if (hostname.includes(DOMAINS.TELECOM) ||
                hostname.includes(DOMAINS.INSIGHT) ||
                hostname.includes(DOMAINS.CMIX) ||
                hostname.includes(DOMAINS.CONFIRMIT_EU)) {
                const label = element.closest('label');
                if (label) {
                    Logger.log(`找到可点击的标签: ${label.textContent}`);
                    return label;
                }
            }

            if (hostname.includes(DOMAINS.QUALTRICS) ||
                hostname.includes(DOMAINS.JIBUNU)) {
                const parent = element.parentElement;
                if (parent) {
                    Logger.log(`找到可点击的元素: ${parent.tagName || 'unknown'}`);
                    return parent;
                }
            }
            
            Logger.log(`使用默认可点击元素: ${element.tagName || 'unknown'}`);
            return element;
        },

        // 快速点击辅助函数
        async fastClick(element) {
            element.click();
            element.checked = true;
            element.dispatchEvent(new Event('change', { bubbles: true }));
        },

        // 对元素进行分组处理
        groupElements(elements) {
            const tempGroups = new Map();
            const finalGroups = new Map();
            const defaultGroup = [];
            const dropdownGroup = [];
            const numberGroup = [];
            const invalidGroup = [];
            const groupingAttribute = this.getDomainGroupingAttribute();
        
            Logger.log(`当前网站使用 ${groupingAttribute} 属性进行分组`);
        
            elements.forEach(el => {

                if (this.hasTextInput(el)) {
                    invalidGroup.push(el);
                    return;
                }
        
                let groupKey;
        
                if (el.tagName === 'SELECT') {
                    dropdownGroup.push(el);
                    return;
                }
                if (el.type === 'number') {
                    numberGroup.push(el);
                    return;
                }
        
                // 特使域名分组逻辑
                if (hostname.includes(DOMAINS.CONFIRMIT_EU) || hostname.includes(DOMAINS.CONFIRMIT_COM)) {
                    const id = el.getAttribute('id');
                    if (id) {
                        const segments = id.split('_');
                        const numberIndex = segments.length - 1 - segments.slice().reverse().findIndex(seg => !isNaN(seg));
                        if (numberIndex > 0) {
                            groupKey = segments.slice(0, numberIndex).join('_');
                        } else {
                            groupKey = id || `ungrouped_${Math.random()}`;
                        }
                    }
                } else if (hostname.includes(DOMAINS.IPSOS)) {
                    const id = el.getAttribute('id');
                    if (id && id.startsWith('_')) {
                        const segments = id.split('_');
                        if (segments.length > 1) {
                            groupKey = segments[0] + '_' + segments[1]; 
                        } else {
                            groupKey = id || `ungrouped_${Math.random()}`;
                        }
                    }
                } else if (hostname.includes(DOMAINS.LIFEPOINTS)) {
                    const row = el.closest('tr');
                    const rowHeader = row ? row.querySelector('th[id]') : null;
                    if (rowHeader && rowHeader.id) {
                        groupKey = rowHeader.id;
                    } else {
                        groupKey = el.name || `ungrouped_${Math.random()}`;
                    }
                } else if (hostname.includes(DOMAINS.INSIGHT)) {
                    const name = el.getAttribute('name');
                    if (name) {
                        const segments = name.split('_');
                        if (segments.length >= 2) {
                            groupKey = `${segments[0]}_${segments[1]}`;
                        } else {
                            groupKey = name;
                        }
                    }
                } else if (hostname.includes(DOMAINS.NFIELD)) {
                    const questionname = el.getAttribute('questionname');
                    if (questionname) {
                        const segments = questionname.split('-');
                        if (segments.length >= 2) {
                            groupKey = `${segments[0]}-${segments[1]}`;
                        } else {
                            groupKey = questionname;
                        }
                    }
                } else if (el.hasAttribute(groupingAttribute)) {
                    groupKey = el.getAttribute(groupingAttribute);
                } else if (el.name) {
                    groupKey = el.name;
                } else {
                    groupKey = `ungrouped_${Math.random()}`;
                }
        
                if (!tempGroups.has(groupKey)) {
                    tempGroups.set(groupKey, []);
                }
                tempGroups.get(groupKey).push(el);
            });
        
            // 整理分组
            for (const [key, group] of tempGroups) {
                if (group.length === 1) {
                    defaultGroup.push(...group);
                } else {
                    finalGroups.set(key, group);
                }
            }
        
            if (defaultGroup.length > 0) {
                finalGroups.set('default', defaultGroup);
            }
            if (dropdownGroup.length > 0) {
                finalGroups.set('dropdown_group', dropdownGroup);
            }
            if (numberGroup.length > 0) {
                finalGroups.set('number_group', numberGroup);
            }
        
            return finalGroups;
        },

        // 检查分组方式
        hasSingleSelect(group) {
            return group.every(el => el.type === 'radio' || el.getAttribute('role') === 'radio');
        },

        hasMultiSelect(group) {
            return group.every(el => el.type === 'checkbox' || el.getAttribute('role') === 'checkbox');
        },

        hasMixedSelect(group) {
            const hasRadio = group.some(el => el.type === 'radio' || el.getAttribute('role') === 'radio');
            const hasCheckbox = group.some(el => el.type === 'checkbox' || el.getAttribute('role') === 'checkbox');
            return hasRadio && hasCheckbox;
        },

        hasDropdownSelect(group) {
            return group.every(el => el.tagName === 'SELECT');
        },

        hasNumberInput(group) {
            return group.every(el => el.type === 'number');
        },

        hasTextInput(element) {
            if (hostname.includes(DOMAINS.LIFEPOINTS)) {
                const closestTr = element.closest('tr');
                if (!closestTr) return false;
                const textInputs = closestTr.querySelectorAll('input[type="text"], textarea');
                return textInputs.length > 0;
            }
            if (hostname.includes(DOMAINS.CONFIRMIT_COM) || hostname.includes(DOMAINS.CONFIRMIT_EU)) {
                const parentElement = element.parentElement;
                if (!parentElement) return false;
                const textInputs = parentElement.querySelectorAll('input[type="text"], textarea');
                return textInputs.length > 0;
            }
            return false;
        },

        // 处理自动提交
        async handleAutoSubmit() {
            let submitBtn = null;
            
            // Domain-specific submit button selectors
            if (hostname.includes(DOMAINS.VIPVOICE)) {
                submitBtn = document.querySelector("#add_to_basket");
            } else if (hostname.includes(DOMAINS.CMIX)) {
                submitBtn = document.querySelector("#cm-NextButton");
            } else if (hostname.includes(DOMAINS.JIBUNU)) {
                submitBtn = document.querySelector("#BS_CONTINUE_BUTTON");
            } else if (hostname.includes(DOMAINS.LIFEPOINTS)) {
                submitBtn = document.querySelector("#btn_continue");
            } else if (hostname.includes(DOMAINS.CONFIRMIT_EU)) {
                submitBtn = document.querySelector("body > div.cf-page__main > div.cf-page__navigation.cf-navigation > button.cf-navigation__button.cf-navigation-next");
            } else if (hostname.includes(DOMAINS.INSIGHT)) {
                submitBtn = document.querySelector("#submit-button");
            } else if (hostname.includes(DOMAINS.HARRIS)) {
                submitBtn = document.querySelector("#btn_continue");  
            } else if (hostname.includes(DOMAINS.QUALTRICS)) {
                submitBtn = document.querySelector("#NextButton");
            } else if (hostname.includes(DOMAINS.DECIPHER)) {
                submitBtn = document.querySelector("#btn_continue");
            } else if (hostname.includes(DOMAINS.TELECOM)) {
                submitBtn = document.querySelector("#os");
            } else if (hostname.includes(DOMAINS.IPSOS)) {
                submitBtn = document.querySelector("#nav-controls > input"); 
            } else if (hostname.includes(DOMAINS.BMS)) {
                submitBtn = document.querySelector("#next_button"); 
            } else if (hostname.includes(DOMAINS.CONFIRMIT_COM)) {
                submitBtn = document.querySelector("body > div.cf-page__main > div.cf-page__navigation.cf-navigation > button")
            } else {
                // Generic submit button selectors
                submitBtn = document.querySelector('button[type="submit"], input[type="submit"], ' +
                                               '[id*="submit" i], [class*="submit" i], ' +
                                               '[id*="next" i], [class*="next" i], ' +
                                               '[id*="continue" i], [class*="continue" i]');
            }

            if (!submitBtn) {
                Logger.warn('未找到提交按钮');
                return;
            }

            // Clear disabled state if present
            if (submitBtn.hasAttribute('disabled')) {
                submitBtn.removeAttribute('disabled');
                Logger.log('已清除提交按钮的禁用状态');
            }

            // Click submit button with delay
            setTimeout(() => {
                Logger.log('点击提交按钮...');
                submitBtn.click();
                submitBtn.dispatchEvent(new Event('change', { bubbles: true }));
            }, 1000);
        },

        // 处理KTRMR问卷
        async handleKtrmrSurvey() {
            return new Promise(async (resolve) => {
                try {
                    const interactiveElements = this.getInteractiveElements();
                    const groups = this.groupElements(interactiveElements);
                    Logger.log('交互元素分组数量:', groups.size);
                    
                    const selectedValues = new Map();
                    
                    for (const [groupName, elements] of groups) {
                        //处理数字题
                        if (this.hasNumberInput(elements)) {
                            for (const input of elements) {
                                input.value = 1;
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        }
                       //处理下拉框
                        else if (this.hasDropdownSelect(elements)) {
                            for (const select of elements) {
                                const validOptions = Array.from(select.options).filter(opt => {
                                    const value = opt.value.toUpperCase();
                                    return !VALUE_BLACKLIST.includes(value) && !opt.disabled;
                                });
                                
                                const optionsToUse = validOptions.length > 0 ? 
                                    validOptions : 
                                    Array.from(select.options).filter(opt => !opt.disabled && opt.value !== '');   
                                if (optionsToUse.length > 0) {                                 
                                    const randomIndex = Math.floor(Math.random() * optionsToUse.length);
                                    const selectedOption = optionsToUse[randomIndex];                                                                 
                                    select.value = selectedOption.value;
                                    Logger.log(`设置下拉菜单 ${groupName} 选中: ${selectedOption.value} (文本: ${selectedOption.text})`);
                                    selectedValues.set(select.name, selectedOption.value);       
                                    select.dispatchEvent(new Event('change', { bubbles: true }));
                                } else {
                                    Logger.log(`下拉菜单 ${groupName} 没有可选择的有效选项`);
                                }
                            }
                        }
                        //处理单选题
                        else if (this.hasSingleSelect(elements) && elements.length > 0) {
                            const validElements = elements.filter(el => {
                                const value = el.value.toUpperCase();
                                return !VALUE_BLACKLIST.includes(value);
                            });
                            
                            const elementsToUse = validElements.length > 0 ? validElements : elements;
                            const randomIndex = Math.floor(Math.random() * elementsToUse.length);
                            const selectedRadio = elementsToUse[randomIndex];
                            selectedRadio.checked = true;
                            Logger.log(`设置单选组 ${groupName} 选中: ${selectedRadio.value}`);
                            selectedValues.set(selectedRadio.name, selectedRadio.value);
                            selectedRadio.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                        //处理多选题
                        else if (this.hasMultiSelect(elements) && elements.length > 0) {
                            const validElements = elements.filter(el => {
                                const value = el.value.toUpperCase();
                                return !VALUE_BLACKLIST.includes(value);
                            });
                            const elementsToUse = validElements.length > 0 ? validElements : elements;
                            const availableElements = elementsToUse;
                            if (availableElements.length > 0) {
                                // 最多选择50%的选项（向上取整），但不超过3个，且至少选择2个
                                const maxSelect = Math.min(
                                    Math.ceil(availableElements.length * 0.5),
                                    3
                                );
                                const numToSelect = Math.min(
                                    maxSelect,
                                    Math.floor(Math.random() * 2) + 2 // 随机选择2-3个
                                );
                                const shuffled = availableElements.sort(() => 0.5 - Math.random());
                                const selectedElements = shuffled.slice(0, numToSelect);
                                
                                selectedElements.forEach(checkbox => {
                                    checkbox.checked = true;
                                    Logger.log(`设置复选框 ${groupName} 选中: ${checkbox.value}`);
                                    selectedValues.set(checkbox.name, checkbox.value);
                                    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                });
                            }
                        }
                        //处理混合题
                        else if (this.hasMixedSelect(elements)) {
                            Logger.log('找到混合选择元素组:', groupName, elements.length);
                            const checkboxElements = elements.filter(el => 
                                el.type === 'checkbox' || el.getAttribute('role') === 'checkbox');
                            
                            if (checkboxElements.length > 0) {
                                const validElements = checkboxElements.filter(el => {
                                    const value = el.value.toUpperCase();
                                    return !VALUE_BLACKLIST.includes(value);
                                });
                                
                                const elementsToUse = validElements.length > 0 ? validElements : checkboxElements;
                                const availableElements = elementsToUse;
                                    
                                if (availableElements.length > 0) {
                                    // 最多选择50%的选项（向上取整），但不超过3个，且至少选择2个
                                    const maxSelect = Math.min(
                                        Math.ceil(availableElements.length * 0.5),
                                        3
                                    );
                                    const numToSelect = Math.min(
                                        maxSelect,
                                        Math.floor(Math.random() * 2) + 2 // 随机选择2-3个
                                    );
                                    const shuffled = availableElements.sort(() => 0.5 - Math.random());
                                    const selectedElements = shuffled.slice(0, numToSelect);
                                    
                                    selectedElements.forEach(checkbox => {
                                        checkbox.checked = true;
                                        Logger.log(`设置混合组复选框 ${groupName} 选中: ${checkbox.value}`);
                                        selectedValues.set(checkbox.name, checkbox.value);
                                        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                                    });
                                }
                            }
                        }
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    const form = document.querySelector('form[method="post"]');
                    if (!form) {
                        Logger.error('未找到表单');
                        resolve();
                        return;
                    }
                    
                    for (const [name, value] of selectedValues.entries()) {
                        const existingField = form.querySelector(`input[name="${name}"]:checked`);
                        if (!existingField) {
                            const hiddenField = document.createElement('input');
                            hiddenField.type = 'hidden';
                            hiddenField.name = name;
                            hiddenField.value = value;
                            form.appendChild(hiddenField);
                            Logger.log(`添加隐藏字段: ${name} = ${value}`);
                        }
                    }
                    
                    const allSubmitButtons = document.querySelectorAll('input[type="submit"]');
                    let submitButton = null;
                    for (const btn of allSubmitButtons) {
                        if (btn.name !== '_NPrev') {
                            submitButton = btn; 
                            break;
                        }
                    }

                    if (submitButton && submitButton.name) {
                        const dynamicField = document.createElement('input');
                        dynamicField.type = 'hidden';
                        dynamicField.name = submitButton.name;
                        dynamicField.value = submitButton.value || '';
                        form.appendChild(dynamicField);
                        Logger.log(`动态添加字段: ${submitButton.name} = ${submitButton.value}`);
                    } 

                    Logger.log('准备提交表单...');

                    setTimeout(() => {
                        try {
                            form.submit();
                            Logger.log('表单已提交');
                        } catch (err) {
                            Logger.error('表单提交出错:', err);
                        }
                    }, 500);
                    
                    await new Promise(resolve => setTimeout(resolve, 500));
                    resolve();
                } catch (error) {
                    Logger.error('提交表单时出错:', error);
                    resolve();
                }
            });
        },

 // 通用处理
        async handleBasicSelection(autoSubmit) {
            const interactiveElements = this.getInteractiveElements();
            const groups = this.groupElements(interactiveElements);
            Logger.log('交互元素分组数量:', groups.size);
            
            for (const [name, elements] of groups) {
                if (elements.length === 0) continue;
                Logger.log('处理交互元素组:', name, elements.length);
                //处理数字题
                 if (this.hasNumberInput(elements)) {
                    Logger.log('处理数字输入组');
                    for (const input of elements) {
                        input.value = 1;
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
                //处理下拉框
                else if (this.hasDropdownSelect(elements)) {
                    Logger.log('处理下拉菜单组');
                    for (const select of elements) {
                        const options = Array.from(select.options).filter(opt =>
                            !opt.disabled && !opt.selected && opt.value !== ''
                        );
                        if (options.length === 0) continue;
                        const targetOption = options[Math.floor(Math.random() * options.length)];
                        select.value = targetOption.value;
                        select.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
                //处理单选题
                else if (this.hasSingleSelect(elements)) {
                    Logger.log('处理单选按钮组');
                    if (elements.length === 1) continue;
                    const clickable = elements
                        .map(el => this.getClickableElement(el))
                        .filter(el => el);
                    if (clickable.length === 0) continue;
                    const target = clickable[Math.floor(Math.random() * clickable.length)];
                    await this.fastClick(target);
                } 
                //处理多选题
                else if (this.hasMultiSelect(elements)) {
                    Logger.log('处理复选框组');
                    if (elements.length === 1) continue;
                    const clickable = elements
                        .map(el => this.getClickableElement(el))
                        .filter(el => el);
                    if (clickable.length === 0) continue;
                    // 最多选择50%的选项（向上取整），但不超过3个，且至少选择2个
                    const maxSelect = Math.min(
                        Math.ceil(clickable.length * 0.5),
                        3
                    );
                    const numToSelect = Math.min(
                        maxSelect,
                        Math.floor(Math.random() * 2) + 2 // 随机选择2-3个
                    );
                    const shuffled = clickable.sort(() => 0.5 - Math.random());
                    const selectedElements = shuffled.slice(0, numToSelect);
                    
                    for (const target of selectedElements) {
                        await this.fastClick(target);
                    }
                }
                //处理混合题
                else if (this.hasMixedSelect(elements)) {
                    Logger.log('处理混合选择元素组');
                    const checkboxElements = elements.filter(el => 
                        el.type === 'checkbox' || el.getAttribute('role') === 'checkbox');
                    if (checkboxElements.length > 0) {
                        const clickableCheckboxes = checkboxElements
                            .map(el => this.getClickableElement(el))
                            .filter(el => el);
                        if (clickableCheckboxes.length > 0) {
                            // 最多选择50%的选项（向上取整），但不超过3个，且至少选择2个
                            const maxSelect = Math.min(
                                Math.ceil(clickableCheckboxes.length * 0.5),
                                3
                            );
                            const numToSelect = Math.min(
                                maxSelect,
                                Math.floor(Math.random() * 2) + 2 // 随机选择2-3个
                            );
                            const shuffled = clickableCheckboxes.sort(() => 0.5 - Math.random());
                            const selectedElements = shuffled.slice(0, numToSelect);
                            
                            for (const target of selectedElements) {
                                await this.fastClick(target);
                            }
                        }
                    }
                }


            }
        
            if (autoSubmit) {
                Logger.log('自动提交表单...');
                await this.handleAutoSubmit();
            }
        },

        // 主处理器
        async handleUniversalQuestions(autoSubmit) {
            if (hostname.includes(DOMAINS.KTRMR) || hostname.includes(DOMAINS.NFIELD)) {
                await this.handleKtrmrSurvey();
                return;
            }

            await this.handleBasicSelection(autoSubmit);
            return;
        }
    };

    // ===== TIMER MANAGEMENT =====
    const TimerManager = {
        startTimer() {
            // Only run in top-level frames
            if (window.top !== window.self) {
                return;
            }

            this.setupMessageListeners();
            this.setupAutoAnswerToggle();
            this.setupLinkInterception();
            this.initializeSurveyInfo();
        },
        
        setupMessageListeners() {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                try {
                    switch(message.action) {
                        case 'toggleAutoAnswer':
                            UI.autoAnswerSwitch.checked = message.value;
                            UI.autoAnswerSwitch.dispatchEvent(new Event('change'));
                            break;
                        case 'quickSelect':
                            SurveyAutomation.handleUniversalQuestions(false);
                            break;
                        case 'quickSubmit':
                            SurveyAutomation.handleUniversalQuestions(true);
                            break;
                        case 'timerUpdate':
                            if (message.surveyId && message.elapsedTime !== undefined) {
                                UI.updateTimer(message.surveyId, message.elapsedTime);
                            }
                            break;
                    }
                } catch (error) {
                    Logger.error('处理消息时出错:', error);
                }
            });
        },
        
        setupAutoAnswerToggle() {
            chrome.storage.local.get(['autoAnswerEnabled'], (data) => {
                const isEnabled = data.autoAnswerEnabled || false;
                if (Utils.isCintReturnPage()) {
                    UI.updateToggleState(false);
                    chrome.storage.local.set({ autoAnswerEnabled: false });
                    return;
                }
                
                UI.updateToggleState(isEnabled);
                
                if (isEnabled) {
                    UI.showCountdown(3, () => {
                        if (Utils.isCintReturnPage()) return;
                        SurveyAutomation.handleUniversalQuestions(true);
                    });
                }
            });
            
            UI.autoAnswerSwitch.addEventListener('change', function() {
                const isEnabled = this.checked;
                if (isEnabled) {
                    if (Utils.isCintReturnPage()) {
                        UI.updateToggleState(false);
                        chrome.storage.local.set({ autoAnswerEnabled: false });
                        return;
                    }
                    
                    UI.updateToggleState(true);
                    chrome.storage.local.set({ autoAnswerEnabled: true });
                    
                    UI.showCountdown(2, () => {
                        SurveyAutomation.handleUniversalQuestions(true);
                    });
                } else {
                    UI.updateToggleState(false);
                    chrome.storage.local.set({ autoAnswerEnabled: false });
                }
            });
        },
        
        setupLinkInterception() {
            document.addEventListener('click', function(event) {
                const link = event.target.closest('a[href*="token="]');
                if (link) {
                    const url = new URL(link.href);
                    const token = url.searchParams.get('token');
                    if (token) {
                        chrome.storage.local.set({ capturedToken: token });
                    }
                }
            }, true);
        },
        
        initializeSurveyInfo() {
            chrome.storage.local.get(['activeSurveyId'], async (data) => {
                const currentSurveyId = (await Utils.getSurveyIdFromUrl()) || '空闲';
                const storedSurveyId = data.activeSurveyId;
                const surveyIdToUse = currentSurveyId !== '空闲' ? currentSurveyId : (storedSurveyId || '空闲');
                
                if (!chrome.runtime?.id) {
                    Logger.error('扩展上下文已失效，无法发送消息');
                    UI.updateTimer(surveyIdToUse, 0);
                    return;
                }
                
                chrome.runtime.sendMessage({ 
                    action: 'updateSurveyId', 
                    surveyId: surveyIdToUse 
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        Logger.error('消息发送失败:', chrome.runtime.lastError.message);
                        UI.updateTimer(surveyIdToUse, 0);
                    } else if (response && response.surveyId) {
                        UI.updateTimer(response.surveyId, response.elapsedTime);
                    }
                });
            });
            
            const updateTimer = setInterval(() => {
                chrome.storage.local.get(['activeSurveyId'], async (data) => {
                    const currentSurveyId = data.activeSurveyId || (await Utils.getSurveyIdFromUrl()) || '空闲';
                    chrome.runtime.sendMessage({ 
                        action: 'getTimer', 
                        surveyId: currentSurveyId 
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            Logger.error('获取计时失败:', chrome.runtime.lastError.message);
                            UI.updateTimer(currentSurveyId, 0);
                        } else if (response && response.elapsedTime !== undefined) {
                            UI.updateTimer(response.surveyId || '空闲', response.elapsedTime);
                        }
                    });
                });
            }, 1000);
            
            window.addEventListener('beforeunload', () => {
                clearInterval(updateTimer);
            });
        }
    };

    // ===== INITIALIZATION =====
    function init() {
        if (!window.secureAutoAnswerInjected) {
            window.secureAutoAnswerInjected = true;
            
            Logger.log('初始化自动答题工具...');
            UI.init();
            TimerManager.startTimer();
        }
    }
    
    init();
})();