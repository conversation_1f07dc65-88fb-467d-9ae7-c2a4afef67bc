import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

base_url = "https://start.lifepointspanel.com/aff_c?aff_id=1351&offer_id="

headers = {
    "Host": "start.lifepointspanel.com",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
    "Connection": "keep-alive",
}

start_id = 0
end_id = 1800
max_workers = 5  # 线程数可根据实际情况调整

def test_offer_id(offer_id):
    url = f"{base_url}{offer_id}"
    try:
        response = requests.get(url, headers=headers, allow_redirects=False, timeout=8)
        if 'Location' in response.headers:
            return f"offer_id: {offer_id}, Location: {response.headers['Location']}\n"
    except Exception as e:
        pass
    return None

with ThreadPoolExecutor(max_workers=max_workers) as executor, open('aff_id=1351.txt', 'w', encoding='utf-8') as f:
    futures = [executor.submit(test_offer_id, offer_id) for offer_id in range(start_id, end_id + 1)]
    for future in as_completed(futures):
        result = future.result()
        if result:
            f.write(result)
            print(result.strip())