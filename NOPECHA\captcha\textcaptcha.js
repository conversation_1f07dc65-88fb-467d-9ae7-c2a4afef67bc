(()=>{var f=chrome;var y="https://api.nopecha.com",i="https://www.nopecha.com",B="https://developers.nopecha.com",ie={doc:{url:B,automation:{url:`${B}/guides/extension_advanced/#automation-build`}},api:{url:y,recognition:{url:`${y}/recognition`},status:{url:`${y}/status`}},www:{url:i,annoucement:{url:`${i}/json/announcement.json`},demo:{url:`${i}/captcha`,recaptcha:{url:`${i}/captcha/recaptcha`},funcaptcha:{url:`${i}/captcha/funcaptcha`},awscaptcha:{url:`${i}/captcha/awscaptcha`},textcaptcha:{url:`${i}/captcha/textcaptcha`},turnstile:{url:`${i}/captcha/turnstile`},perimeterx:{url:`${i}/captcha/perimeterx`},geetest:{url:`${i}/captcha/geetest`},lemincaptcha:{url:`${i}/captcha/lemincaptcha`}},manage:{url:`${i}/manage`},pricing:{url:`${i}/pricing`},setup:{url:`${i}/setup`}},discord:{url:`${i}/discord`},github:{url:`${i}/github`,release:{url:`${i}/github/release`}}};function M(e){let t=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(n=>n.charCodeAt(0));return I(t)}var T=new Uint32Array(256);for(let e=256;e--;){let t=e;for(let n=8;n--;)t=t&1?3988292384^t>>>1:t>>>1;T[e]=t}function I(e){let t=-1;for(let n of e)t=t>>>8^T[t&255^n];return(t^-1)>>>0}async function d(e,t){let n=""+[+new Date,performance.now(),Math.random()],[a,o]=await new Promise(c=>{f.runtime.sendMessage([n,e,...t],c)});if(a===M(n))return o}function $(){let e;return t=>e||(e=t().finally(()=>e=void 0),e)}var G=$(),m;function H(){return G(async()=>(m||(m=await d("settings::get",[])),m))}function E(e){m&&(m={...m,...e},L(m))}function h(){return m}function r(e){return new Promise(t=>setTimeout(t,e))}var P=[];function D(e,t){e.timedout=!1,P.push(e);let n,a=setInterval(async()=>{await R(e,h())||(clearTimeout(n),clearInterval(a))},400);t&&(n=setTimeout(()=>clearInterval(a),t),e.timedout=!0)}async function R(e,t){if(e.timedout)return!1;let n=e.condition(t);if(n===e.running())return!1;if(!n&&e.running())return e.quit(),!1;if(n&&!e.running()){for(;!e.ready();)await r(200);return e.start(),!1}}function L(e){P.forEach(t=>R(t,e))}function A(){f.runtime.connect({name:"stream"}).onMessage.addListener(t=>{t.event==="settingsUpdate"&&E(t.settings)})}function C(e){if(document.readyState!=="loading")setTimeout(e,0);else{let t;t=()=>{removeEventListener("DOMContentLoaded",t),e()},addEventListener("DOMContentLoaded",t)}}function J(e,t){let n=document.createElement("canvas");return n.width=e,n.height=t,n}function q(e){return e.toDataURL("image/jpeg").replace(/data:image\/[a-z]+;base64,/g,"")}function X(e){try{e.getContext("2d").getImageData(0,0,1,1)}catch{return!0}return!1}async function b(e,t,n=1e4){if(!t&&!e.complete&&!await new Promise(s=>{let u=setTimeout(()=>{s(!1)},n);e.addEventListener("load",()=>{clearTimeout(u),s(!0)})}))return;let a=J(e.naturalWidth||t?.clientWidth,e.naturalHeight||t?.clientHeight);return a.getContext("2d").drawImage(e,0,0),!X(a)&&a}async function U(e){let n=getComputedStyle(e).backgroundImage;if(!n||n==="none")if("src"in e&&e.src)n=`url("${e.src}")`;else return;if("computedStyleMap"in e&&!/url\(["']https?:\/\//.test(n)){let l=e.computedStyleMap().get("background-image");if(l instanceof CSSImageValue){let g=await b(l,e);if(g)return g}}let a=/"(.+)"/.exec(n);if(!a)return;n=a[1];let o=document.createElement("a");if(o.href=n,new URL(o.href).origin===document.location.origin){let l=new Image;l.crossOrigin="anonymous",l.src=n;let g=await b(l);if(g)return g}let c=await d("fetch::asData",[n,{}]),s=new Image;s.crossOrigin="anonymous",s.src=c.data;let u=await b(s);if(u)return u}function Y(e,t,n,a){let o=(a*t+n)*4;return[e[o],e[o+1],e[o+2]]}function Z(e,t){return e.every(n=>n<=t)}function ee(e,t){return e.every(n=>n>=t)}function N(e,t=0,n=230,a=.99){let o=e.getContext("2d"),c=o.canvas.width,s=o.canvas.height;if(c===0||s===0)return!0;let u=o.getImageData(0,0,c,s).data,l=0;for(let p=0;p<s;p++)for(let x=0;x<c;x++){let k=Y(u,c,x,p);(Z(k,t)||ee(k,n))&&l++}return l/(c*s)>a}function W(){return[]}function O(e){return new Promise(t=>{e.push(t)})}function v(e){e.forEach(t=>t()),e.splice(0)}async function z(e,t){let n={v:f.runtime.getManifest().version,key:te(e)};return n.url=await d("tab::getURL",[]),n}function te(e){return!e.keys||!e.keys.length?e.key:e.keys[Math.floor(Math.random()*e.keys.length)]}var _=W(),S,w=!1;function j(){let e=h(),t=document.querySelector(e.textcaptcha_image_selector),n=document.querySelector(e.textcaptcha_input_selector);return!t||!n?!1:(d("tab::registerDetectedCaptcha",["textcaptcha"]),!0)}function V(){w=!0,v(_),S=new MutationObserver(()=>{setTimeout(()=>v(_),200)}),S.observe(document.body,{subtree:!0,childList:!0,attributes:!0}),ae()}function K(){S.disconnect(),w=!1,v(_)}function Q(){return w}async function ne(e){if(e instanceof HTMLCanvasElement)return e;if(e instanceof HTMLImageElement&&(e.src||e.srcset)){let t=await b(e);if(t)return t}return await U(e)}var F=!1;async function ae(){if(F)return;F=!0;let e;for(;w;){let t=h(),n=document.querySelector(t.textcaptcha_image_selector),a=document.querySelector(t.textcaptcha_input_selector);if(!n){await r(500);continue}if(!a){await r(500);continue}if(a.click(),a.focus(),await r(200),a.value!==""){await r(500);continue}let o=await ne(n);if(!o){await r(500);continue}if(N(o)){await r(500);continue}let c=q(o);if(e===c){await r(500);continue}e=c;let s=new Date().valueOf(),u=await d("api::recognition",[{type:"textcaptcha",image_data:[c],...await z(t)}]);if(!u||"error"in u){console.warn("[@nope/textcaptcha] api error",u),await r(2e3);continue}let l=new Date().valueOf();if(t.textcaptcha_solve_delay){let g=t.textcaptcha_solve_delay_time-l+s;g>0&&await r(g)}if(u.data&&u.data.length>0&&!a.value){a.click(),a.focus(),await r(200),Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set.call(a,u.data[0]);for(let p of u.data[0])a.dispatchEvent(new KeyboardEvent("keydown",{key:p,bubbles:!0})),await r(15),a.dispatchEvent(new KeyboardEvent("keypress",{key:p,bubbles:!0})),await r(10),a.dispatchEvent(new KeyboardEvent("keyup",{key:p,bubbles:!0})),await r(15);a.dispatchEvent(new Event("input",{bubbles:!0})),a.dispatchEvent(new Event("change",{bubbles:!0}))}await O(_)}}async function oe(){A(),await H(),await d("tab::registerDetectedCaptcha",["textcaptcha"]);let e=location.hostname;D({name:"textcaptcha/auto-solve",condition:t=>t.enabled&&t.textcaptcha_auto_solve&&!t.disabled_hosts.includes(e)&&!!t.textcaptcha_image_selector&&!!t.textcaptcha_input_selector,ready:j,start:V,quit:K,running:Q})}C(oe);})();
