(()=>{"use strict";var e,t={3332:(e,t,i)=>{var o=i(7416),n=i(1128),a=i(2024),s=i(822),l=i(4526);const p={class:"section"},c={class:"option-wrap"},r={class:"option select"},d={key:0,class:"option text-field"},u={key:1,class:"service-guide",target:"_blank",rel:"noreferrer",href:"https://github.com/dessant/buster/wiki/Configuring-Google-Cloud-Speech-to-Text"},h={key:2,class:"option text-field"},m={key:3,class:"option text-field"},g={key:4,class:"service-guide",target:"_blank",rel:"noreferrer",href:"https://github.com/dessant/buster/wiki/Configuring-IBM-Watson-Speech-to-Text"},b={key:5,class:"option select"},f={key:6,class:"option text-field"},v={key:7,class:"service-guide",target:"_blank",rel:"noreferrer",href:"https://github.com/dessant/buster/wiki/Configuring-Microsoft-Azure-Speech-to-Text"},w={key:9,class:"wit-add-api"},A={key:10,class:"service-guide",target:"_blank",rel:"noreferrer",href:"https://github.com/dessant/buster/wiki/Configuring-Wit.ai"},S={class:"section section-client"},y={class:"option-wrap"},T={key:0,class:"option"},k={key:1,class:"option"},x={key:2,class:"option"},L={key:3,class:"client-download"},_=["innerHTML"],C={key:0,class:"download-error"},V=["href"],W={class:"section"},I={class:"option-wrap"},U={class:"option select"},E={class:"option"},O={key:0,class:"option"},M={key:1,class:"option"},K={key:2,class:"option button"};var $=i(4390),F=i(4910),q=i(786),D=i(8618),X=i(4729),Q=i(3655),P=i(7272);const j={components:{[F.qw.name]:F.qw,[F.$n.name]:F.$n,[F.In.name]:F.In,[F.l6.name]:F.l6,[F.dO.name]:F.dO,[F.A_.name]:F.A_},data:function(){return{dataLoaded:!1,listItems:{...(0,n.L1)({speechService:["witSpeechApiDemo","googleSpeechApi","witSpeechApi","ibmSpeechApi","microsoftSpeechApi"]},{scope:"optionValue_speechService"}),...(0,n.L1)({microsoftSpeechApiLoc:Q.sn},{scope:"optionValue_microsoftSpeechApiLoc"}),...(0,n.L1)({witSpeechApiLang:[...new Set(Object.values(Q.NP).filter(Boolean))].sort()},{scope:"optionValue_witSpeechApiLang"}),...(0,n.L1)({appTheme:["auto","light","dark"]},{scope:"optionValue_appTheme"})},enableContributions:X.ZY,witSpeechApiLang:null,witSpeechApis:[],clientAppVerified:!1,clientAppInstalled:!1,clientAppDownloadUrl:"",installGuideUrl:"",options:{speechService:"",googleSpeechApiKey:"",ibmSpeechApiUrl:"",ibmSpeechApiKey:"",microsoftSpeechApiLoc:"",microsoftSpeechApiKey:"",witSpeechApiKeys:{},loadEnglishChallenge:!1,tryEnglishSpeechModel:!1,simulateUserInput:!1,autoUpdateClientApp:!1,navigateWithKeyboard:!1,appTheme:"",showContribPage:!1}}},methods:{getText:D.q4,setup:async function(){P.runtime.onMessage.addListener(this.onMessage);const e=await q.Ay.get(Q.M$);for(const t of Object.keys(this.options))this.options[t]=e[t],this.$watch(`options.${t}`,(async function(e){await q.Ay.set({[t]:(0,$.ux)(e)}),await P.runtime.sendMessage({id:"optionChange"})}),{deep:!0});this.witSpeechApis=Object.keys(e.witSpeechApiKeys),this.setWitSpeechApiLangOptions(),document.title=(0,D.q4)("pageTitle",[(0,D.q4)("pageTitle_options"),(0,D.q4)("extensionName")]),this.verifyClientApp(),this.dataLoaded=!0},onMessage:function(e,t){"reloadOptionsPage"===e.id&&self.location.reload()},verifyClientApp:async function(){try{await(0,n.FM)(),this.clientAppInstalled=!0}catch(e){if(!this.installGuideUrl){this.installGuideUrl="https://github.com/dessant/buster/wiki/Installing-the-client-app";const{os:e,arch:t}=this.$env;Q.T6.includes(`${e}/${t}`)&&(this.installGuideUrl+=`#${e}`,this.clientAppDownloadUrl=`https://github.com/dessant/buster-client/releases/download/v${X.Pc}/buster-client-setup-v${X.Pc}-${e}-${t}`,"windows"===e&&(this.clientAppDownloadUrl+=".exe"))}this.clientAppInstalled=!1}this.clientAppVerified=!0},setWitSpeechApiLangOptions:function(){this.listItems.witSpeechApiLang=this.listItems.witSpeechApiLang.filter((e=>!this.witSpeechApis.includes(e.value)))},addWitSpeechApi:function(){this.witSpeechApis.push(this.witSpeechApiLang),this.witSpeechApiLang=null,this.setWitSpeechApiLangOptions()},saveWitSpeechApiKey:function(e,t){const i=this.options.witSpeechApiKeys;e?this.options.witSpeechApiKeys=Object.assign({},i,{[t]:e}):i[t]&&(delete i[t],this.options.witSpeechApiKeys=Object.assign({},i))},showContribute:async function(){await(0,n.kB)()}},created:function(){document.title=(0,D.q4)("pageTitle",[(0,D.q4)("pageTitle_options"),(0,D.q4)("extensionName")]),this.setup()}},G=(0,i(1535).A)(j,[["render",function(e,t,i,o,n,a){const $=(0,s.g2)("vn-select"),F=(0,s.g2)("vn-text-field"),q=(0,s.g2)("vn-button"),D=(0,s.g2)("vn-switch"),X=(0,s.g2)("vn-icon"),Q=(0,s.g2)("vn-app");return e.dataLoaded?((0,s.uX)(),(0,s.Wv)(Q,{key:0},{default:(0,s.k6)((()=>[(0,s.Lk)("div",p,[t[0]||((0,s.Vq)(-1),t[0]=(0,s.Lk)("div",{class:"section-title"},[(0,s.eW)((0,l.v_)(a.getText("optionSectionTitle_services")),1)]),(0,s.Vq)(1),t[0]),(0,s.eW)(),(0,s.Lk)("div",c,[(0,s.Lk)("div",r,[(0,s.bF)($,{label:a.getText("optionTitle_speechService"),items:e.listItems.speechService,modelValue:e.options.speechService,"onUpdate:modelValue":t[1]||(t[1]=t=>e.options.speechService=t),transition:"scale-transition"},null,8,["label","items","modelValue"])]),(0,s.eW)(),"googleSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("div",d,[(0,s.bF)(F,{label:a.getText("inputLabel_apiKey"),modelValue:e.options.googleSpeechApiKey,"onUpdate:modelValue":t[2]||(t[2]=t=>e.options.googleSpeechApiKey=t),modelModifiers:{trim:!0}},null,8,["label","modelValue"])])):(0,s.Q3)("",!0),(0,s.eW)(),"googleSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("a",u,(0,l.v_)(a.getText("linkText_apiGuide")),1)):(0,s.Q3)("",!0),(0,s.eW)(),"ibmSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("div",h,[(0,s.bF)(F,{modelValue:e.options.ibmSpeechApiUrl,"onUpdate:modelValue":t[3]||(t[3]=t=>e.options.ibmSpeechApiUrl=t),modelModifiers:{trim:!0},label:a.getText("inputLabel_apiUrl")},null,8,["modelValue","label"])])):(0,s.Q3)("",!0),(0,s.eW)(),"ibmSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("div",m,[(0,s.bF)(F,{modelValue:e.options.ibmSpeechApiKey,"onUpdate:modelValue":t[4]||(t[4]=t=>e.options.ibmSpeechApiKey=t),modelModifiers:{trim:!0},label:a.getText("inputLabel_apiKey")},null,8,["modelValue","label"])])):(0,s.Q3)("",!0),(0,s.eW)(),"ibmSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("a",g,(0,l.v_)(a.getText("linkText_apiGuide")),1)):(0,s.Q3)("",!0),(0,s.eW)(),"microsoftSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("div",b,[(0,s.bF)($,{label:a.getText("optionTitle_microsoftSpeechApiLoc"),items:e.listItems.microsoftSpeechApiLoc,modelValue:e.options.microsoftSpeechApiLoc,"onUpdate:modelValue":t[5]||(t[5]=t=>e.options.microsoftSpeechApiLoc=t),transition:"scale-transition"},null,8,["label","items","modelValue"])])):(0,s.Q3)("",!0),(0,s.eW)(),"microsoftSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("div",f,[(0,s.bF)(F,{modelValue:e.options.microsoftSpeechApiKey,"onUpdate:modelValue":t[6]||(t[6]=t=>e.options.microsoftSpeechApiKey=t),modelModifiers:{trim:!0},label:a.getText("inputLabel_apiKey")},null,8,["modelValue","label"])])):(0,s.Q3)("",!0),(0,s.eW)(),"microsoftSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("a",v,(0,l.v_)(a.getText("linkText_apiGuide")),1)):(0,s.Q3)("",!0),(0,s.eW)(),"witSpeechApi"===e.options.speechService?((0,s.uX)(!0),(0,s.CE)(s.FK,{key:8},(0,s.pI)(e.witSpeechApis,(t=>((0,s.uX)(),(0,s.Wv)(F,{class:"text-field",key:t.id,"model-value":e.options.witSpeechApiKeys[t]||"",label:a.getText("inputLabel_apiKeyType",[a.getText(`optionValue_witSpeechApiLang_${t}`)]),"onUpdate:modelValue":e=>a.saveWitSpeechApiKey(e.trim(),t)},null,8,["model-value","label","onUpdate:modelValue"])))),128)):(0,s.Q3)("",!0),(0,s.eW)(),"witSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("div",w,[(0,s.bF)($,{label:a.getText("optionTitle_witSpeechApiLang"),items:e.listItems.witSpeechApiLang,modelValue:e.witSpeechApiLang,"onUpdate:modelValue":t[7]||(t[7]=t=>e.witSpeechApiLang=t),transition:"scale-transition"},null,8,["label","items","modelValue"]),(0,s.eW)(),(0,s.bF)(q,{disabled:!e.witSpeechApiLang,onClick:a.addWitSpeechApi},{default:(0,s.k6)((()=>[(0,s.eW)((0,l.v_)(a.getText("buttonLabel_addApi")),1)])),_:1},8,["disabled","onClick"])])):(0,s.Q3)("",!0),(0,s.eW)(),"witSpeechApi"===e.options.speechService?((0,s.uX)(),(0,s.CE)("a",A,(0,l.v_)(a.getText("linkText_apiGuide")),1)):(0,s.Q3)("",!0)])]),(0,s.eW)(),(0,s.Lk)("div",S,[t[8]||((0,s.Vq)(-1),t[8]=(0,s.Lk)("div",{class:"section-title"},[(0,s.eW)((0,l.v_)(a.getText("optionSectionTitle_client")),1)]),(0,s.Vq)(1),t[8]),(0,s.eW)(),t[9]||((0,s.Vq)(-1),t[9]=(0,s.Lk)("div",{class:"section-desc"},[(0,s.eW)((0,l.v_)(a.getText("optionSectionDescription_client")),1)]),(0,s.Vq)(1),t[9]),(0,s.eW)(),(0,s.Lk)("div",y,[e.clientAppInstalled||e.clientAppVerified&&e.options.simulateUserInput?((0,s.uX)(),(0,s.CE)("div",T,[(0,s.bF)(D,{label:a.getText("optionTitle_simulateUserInput"),modelValue:e.options.simulateUserInput,"onUpdate:modelValue":t[10]||(t[10]=t=>e.options.simulateUserInput=t)},null,8,["label","modelValue"])])):(0,s.Q3)("",!0),(0,s.eW)(),e.clientAppVerified&&e.options.simulateUserInput?((0,s.uX)(),(0,s.CE)("div",k,[(0,s.bF)(D,{label:a.getText("optionTitle_navigateWithKeyboard"),modelValue:e.options.navigateWithKeyboard,"onUpdate:modelValue":t[11]||(t[11]=t=>e.options.navigateWithKeyboard=t)},null,8,["label","modelValue"])])):(0,s.Q3)("",!0),(0,s.eW)(),e.clientAppInstalled?((0,s.uX)(),(0,s.CE)("div",x,[(0,s.bF)(D,{label:a.getText("optionTitle_autoUpdateClientApp"),modelValue:e.options.autoUpdateClientApp,"onUpdate:modelValue":t[12]||(t[12]=t=>e.options.autoUpdateClientApp=t)},null,8,["label","modelValue"])])):(0,s.Q3)("",!0),(0,s.eW)(),e.clientAppVerified&&!e.clientAppInstalled?((0,s.uX)(),(0,s.CE)("div",L,[(0,s.Lk)("div",{class:"download-desc",innerHTML:a.getText("pageContent_optionClientAppDownloadDesc",[`<a target='_blank' rel='noreferrer' href='${e.installGuideUrl}'>${a.getText("linkText_installGuide")}</a>`])},null,8,_),(0,s.eW)(),e.clientAppDownloadUrl?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.CE)("div",C,(0,l.v_)(a.getText("pageContent_optionClientAppOSError")),1)),(0,s.eW)(),(0,s.bF)(q,{class:"download-button",disabled:!e.clientAppDownloadUrl,onClick:t[13]||(t[13]=t=>e.$refs.dlLink.click()),variant:"elevated"},{default:(0,s.k6)((()=>[(0,s.eW)((0,l.v_)(a.getText("buttonLabel_downloadApp")),1)])),_:1},8,["disabled"]),(0,s.eW)(),(0,s.Lk)("a",{ref:"dlLink",class:"download-link",target:"_blank",rel:"noreferrer",href:e.clientAppDownloadUrl},null,8,V)])):(0,s.Q3)("",!0)])]),(0,s.eW)(),(0,s.Lk)("div",W,[t[14]||((0,s.Vq)(-1),t[14]=(0,s.Lk)("div",{class:"section-title"},[(0,s.eW)((0,l.v_)(a.getText("optionSectionTitle_misc")),1)]),(0,s.Vq)(1),t[14]),(0,s.eW)(),(0,s.Lk)("div",I,[(0,s.Lk)("div",U,[(0,s.bF)($,{label:a.getText("optionTitle_appTheme"),items:e.listItems.appTheme,modelValue:e.options.appTheme,"onUpdate:modelValue":t[15]||(t[15]=t=>e.options.appTheme=t),transition:"scale-transition"},null,8,["label","items","modelValue"])]),(0,s.eW)(),(0,s.Lk)("div",E,[(0,s.bF)(D,{label:a.getText("optionTitle_loadEnglishChallenge"),modelValue:e.options.loadEnglishChallenge,"onUpdate:modelValue":t[16]||(t[16]=t=>e.options.loadEnglishChallenge=t)},null,8,["label","modelValue"])]),(0,s.eW)(),e.options.loadEnglishChallenge?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.CE)("div",O,[(0,s.bF)(D,{label:a.getText("optionTitle_tryEnglishSpeechModel"),modelValue:e.options.tryEnglishSpeechModel,"onUpdate:modelValue":t[17]||(t[17]=t=>e.options.tryEnglishSpeechModel=t)},null,8,["label","modelValue"])])),(0,s.eW)(),e.enableContributions?((0,s.uX)(),(0,s.CE)("div",M,[(0,s.bF)(D,{label:a.getText("optionTitle_showContribPage"),modelValue:e.options.showContribPage,"onUpdate:modelValue":t[18]||(t[18]=t=>e.options.showContribPage=t)},null,8,["label","modelValue"])])):(0,s.Q3)("",!0),(0,s.eW)(),e.enableContributions?((0,s.uX)(),(0,s.CE)("div",K,[(0,s.bF)(q,{class:"contribute-button vn-icon--start",onClick:a.showContribute},{default:(0,s.k6)((()=>[(0,s.bF)(X,{src:"/src/assets/icons/misc/favorite-filled.svg"}),(0,s.eW)(" "+(0,l.v_)(a.getText("buttonLabel_contribute")),1)])),_:1},8,["onClick"])])):(0,s.Q3)("",!0)])])])),_:1})):(0,s.Q3)("",!0)}]]);!async function(){await(0,n.rn)(["400 14px Roboto","500 14px Roboto"]);const e=(0,o.Ef)(G);await(0,n.K7)(e),await(0,a.H)(e),e.mount("body")}()},1128:(e,t,i)=>{i.d(t,{FM:()=>u,K7:()=>p,L1:()=>s,kB:()=>d,rL:()=>r,rn:()=>l,xB:()=>c});var o=i(786),n=i(8618),a=(i(3655),i(4729),i(7272));function s(e,{scope:t="",shortScope:i=""}={}){const o={};for(const[a,s]of Object.entries(e))o[a]=[],s.forEach((function(e){void 0===e.value&&(e={value:e}),e.title=(0,n.q4)(`${t?t+"_":""}${e.value}`),i&&(e.shortTitle=(0,n.q4)(`${i}_${e.value}`)),o[a].push(e)}));return o}async function l(e){await Promise.allSettled(e.map((e=>document.fonts.load(e))))}async function p(e){const t=await(0,n.uo)(),i=[t.targetEnv,t.os];document.documentElement.classList.add(...i),e&&(e.config.globalProperties.$env=t)}async function c(e){return e||({appTheme:e}=await o.Ay.get("appTheme")),"auto"===e&&(e=(0,n.T6)().matches?"dark":"light"),e}function r(e){!function(e){(0,n.T6)().addEventListener("change",(function(){e()}))}(e),function(e){a.storage.onChanged.addListener((function(t,i){"local"===i&&t.appTheme&&e()}))}(e)}async function d({action:e="",updateStats:t=!0,getTab:i=!1,activeTab:s=null}={}){t&&await o.Ay.set({contribPageLastOpen:(0,n.I4)()});let l=a.runtime.getURL("/src/contribute/index.html");return e&&(l=`${l}?action=${e}`),async function({url:e="",setOpenerTab:t=!0,getTab:i=!1,activeTab:o=null}={}){o||(o=await(0,n.e6)());const s={url:e,index:o.index+1,active:!0,getTab:i};return t&&(s.openerTabId=await async function({tab:e,tabId:t=null}={}){return e||null===t||(e=await a.tabs.get(t).catch((e=>null))),await(0,n.kL)({tab:e})&&!(await(0,n.uo)()).isMobile?e.id:null}({tab:o})),(0,n.aI)(s)}({url:l,getTab:i,activeTab:s})}async function u({start:e=!0,stop:t=!0,checkResponse:i=!0}={}){e&&await a.runtime.sendMessage({id:"startClientApp"});const o=await a.runtime.sendMessage({id:"messageClientApp",message:{command:"ping"}});if(t&&await a.runtime.sendMessage({id:"stopClientApp"}),i&&(!o.success||"pong"!==o.data))throw new Error(`Client app response: ${o.data}`);return o}},8618:(e,t,i)=>{i.d(t,{I4:()=>h,T6:()=>u,aI:()=>l,e6:()=>p,kL:()=>c,q4:()=>s,uo:()=>d}),i(9362),i(9033),i(4665);var o=i(786),n=i(4729),a=i(7272);function s(e,t){return a.i18n.getMessage(e,t)}async function l({url:e="",index:t=null,active:i=!0,openerTabId:o=null,getTab:s=!1}={}){const l={url:e,active:i};null!==t&&(l.index=t),null!==o&&(l.openerTabId=o);let p=await a.tabs.create(l);if(s){if("samsung"===n.Dm){let t=1;for(;t<=500&&(!p||p.url!==e);)[p]=await a.tabs.query({lastFocusedWindow:!0,url:e}),await m(20),t+=1}return p}}async function p(){const[e]=await a.tabs.query({lastFocusedWindow:!0,active:!0});return e}async function c({tab:e,tabId:t=null}={}){if(e||null===t||(e=await a.tabs.get(t).catch((e=>null))),e&&e.id!==a.tabs.TAB_ID_NONE)return!0}let r;async function d(){if(!function(){const e=n.tn?a.runtime.getURL("/src/background/script.js"):a.runtime.getURL("/src/background/index.html");return self.location.href===e}())return a.runtime.sendMessage({id:"getPlatform"});let{os:e,arch:t}=await async function(){if(r)return r;if(n.tn)({platformInfo:r}=await o.Ay.get("platformInfo",{area:"session"}));else try{r=JSON.parse(window.sessionStorage.getItem("platformInfo"))}catch(e){}if(!r){let e,t;if("samsung"===n.Dm?(e="android",t=""):"safari"===n.Dm?({os:e,arch:t}=await a.runtime.sendNativeMessage("application.id",{id:"getPlatformInfo"})):({os:e,arch:t}=await a.runtime.getPlatformInfo()),r={os:e,arch:t},n.tn)await o.Ay.set({platformInfo:r},{area:"session"});else try{window.sessionStorage.setItem("platformInfo",JSON.stringify(r))}catch(e){}}return r}();"win"===e?e="windows":"mac"===e&&(e="macos"),["x86-32","i386"].includes(t)?t="386":["x86-64","x86_64"].includes(t)?t="amd64":t.startsWith("arm")&&(t="arm");const i="windows"===e,s="macos"===e,l="linux"===e,p="android"===e,c="ios"===e,d="ipados"===e,u=["android","ios","ipados"].includes(e),h="chrome"===n.Dm,m=["chrome","edge"].includes(n.Dm)&&/\sedg(?:e|a|ios)?\//i.test(navigator.userAgent),g="firefox"===n.Dm,b=["chrome","opera"].includes(n.Dm)&&/\sopr\//i.test(navigator.userAgent),f="safari"===n.Dm,v="samsung"===n.Dm;return{os:e,arch:t,targetEnv:n.Dm,isWindows:i,isMacos:s,isLinux:l,isAndroid:p,isIos:c,isIpados:d,isMobile:u,isChrome:h,isEdge:m,isFirefox:g,isOpera:b,isSafari:f,isSamsung:v}}function u(){return window.matchMedia("(prefers-color-scheme: dark)")}function h(e){return e||(e=Date.now()),e-e%864e5}function m(e){return new Promise((t=>self.setTimeout(t,e)))}},4910:(e,t,i)=>{i.d(t,{$n:()=>n.A,A_:()=>c.A,In:()=>a.A,K0:()=>s.A,dO:()=>p.A,l6:()=>l.A,qw:()=>o.A});var o=i(4953),n=i(658),a=(i(9608),i(9737),i(4322),i(7546),i(8982),i(9945),i(5937),i(113)),s=i(5189),l=(i(8733),i(6173),i(4703),i(3068),i(382),i(5806)),p=(i(2471),i(3652)),c=i(7626)}},i={};function o(e){var n=i[e];if(void 0!==n)return n.exports;var a=i[e]={exports:{}};return t[e].call(a.exports,a,a.exports,o),a.exports}o.m=t,e=[],o.O=(t,i,n,a)=>{if(!i){var s=1/0;for(r=0;r<e.length;r++){for(var[i,n,a]=e[r],l=!0,p=0;p<i.length;p++)(!1&a||s>=a)&&Object.keys(o.O).every((e=>o.O[e](i[p])))?i.splice(p--,1):(l=!1,a<s&&(s=a));if(l){e.splice(r--,1);var c=n();void 0!==c&&(t=c)}}return t}a=a||0;for(var r=e.length;r>0&&e[r-1][2]>a;r--)e[r]=e[r-1];e[r]=[i,n,a]},o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var i in t)o.o(t,i)&&!o.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.j=575,(()=>{var e={575:0};o.O.j=t=>0===e[t];var t=(t,i)=>{var n,a,[s,l,p]=i,c=0;if(s.some((t=>0!==e[t]))){for(n in l)o.o(l,n)&&(o.m[n]=l[n]);if(p)var r=p(o)}for(t&&t(i);c<s.length;c++)a=s[c],o.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return o.O(r)},i=globalThis.webpackChunkbuster=globalThis.webpackChunkbuster||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})();var n=o.O(void 0,[830],(()=>o(3332)));n=o.O(n)})();