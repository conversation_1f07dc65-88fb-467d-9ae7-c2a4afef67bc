(()=>{var it,_,qt,Ke,O,Vt,Wt,_t,Ye,nt={},Ft=[],tn=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,vt=Array.isArray;function I(t,e){for(var o in e)t[o]=e[o];return t}function Zt(t){var e=t.parentNode;e&&e.removeChild(t)}function n(t,e,o){var r,s,i,c={};for(i in e)i=="key"?r=e[i]:i=="ref"?s=e[i]:c[i]=e[i];if(arguments.length>2&&(c.children=arguments.length>3?it.call(arguments,2):o),typeof t=="function"&&t.defaultProps!=null)for(i in t.defaultProps)c[i]===void 0&&(c[i]=t.defaultProps[i]);return tt(t,c,r,s,null)}function tt(t,e,o,r,s){var i={type:t,props:e,key:o,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:s??++qt};return s==null&&_.vnode!=null&&_.vnode(i),i}function x(t){return t.children}function et(t,e){this.props=t,this.context=e}function Z(t,e){if(e==null)return t.__?Z(t.__,t.__.__k.indexOf(t)+1):null;for(var o;e<t.__k.length;e++)if((o=t.__k[e])!=null&&o.__e!=null)return o.__e;return typeof t.type=="function"?Z(t):null}function jt(t){var e,o;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((o=t.__k[e])!=null&&o.__e!=null){t.__e=t.__c.base=o.__e;break}return jt(t)}}function Rt(t){(!t.__d&&(t.__d=!0)&&O.push(t)&&!ot.__r++||Vt!==_.debounceRendering)&&((Vt=_.debounceRendering)||Wt)(ot)}function ot(){var t,e,o,r,s,i,c,l;for(O.sort(_t);t=O.shift();)t.__d&&(e=O.length,r=void 0,s=void 0,c=(i=(o=t).__v).__e,(l=o.__P)&&(r=[],(s=I({},i)).__v=i.__v+1,ht(l,i,s,o.__n,l.ownerSVGElement!==void 0,i.__h!=null?[c]:null,r,c??Z(i),i.__h),Kt(r,i),i.__e!=c&&jt(i)),O.length>e&&O.sort(_t));ot.__r=0}function Gt(t,e,o,r,s,i,c,l,u,d){var a,m,g,p,f,w,h,C=r&&r.__k||Ft,M=C.length;for(o.__k=[],a=0;a<e.length;a++)if((p=o.__k[a]=(p=e[a])==null||typeof p=="boolean"||typeof p=="function"?null:typeof p=="string"||typeof p=="number"||typeof p=="bigint"?tt(null,p,null,null,p):vt(p)?tt(x,{children:p},null,null,null):p.__b>0?tt(p.type,p.props,p.key,p.ref?p.ref:null,p.__v):p)!=null){if(p.__=o,p.__b=o.__b+1,(g=C[a])===null||g&&p.key==g.key&&p.type===g.type)C[a]=void 0;else for(m=0;m<M;m++){if((g=C[m])&&p.key==g.key&&p.type===g.type){C[m]=void 0;break}g=null}ht(t,p,g=g||nt,s,i,c,l,u,d),f=p.__e,(m=p.ref)&&g.ref!=m&&(h||(h=[]),g.ref&&h.push(g.ref,null,p),h.push(m,p.__c||f,p)),f!=null?(w==null&&(w=f),typeof p.type=="function"&&p.__k===g.__k?p.__d=u=Jt(p,u,t):u=Xt(t,p,g,C,f,u),typeof o.type=="function"&&(o.__d=u)):u&&g.__e==u&&u.parentNode!=t&&(u=Z(g))}for(o.__e=w,a=M;a--;)C[a]!=null&&(typeof o.type=="function"&&C[a].__e!=null&&C[a].__e==o.__d&&(o.__d=Qt(r).nextSibling),te(C[a],C[a]));if(h)for(a=0;a<h.length;a++)Yt(h[a],h[++a],h[++a])}function Jt(t,e,o){for(var r,s=t.__k,i=0;s&&i<s.length;i++)(r=s[i])&&(r.__=t,e=typeof r.type=="function"?Jt(r,e,o):Xt(o,r,r,s,r.__e,e));return e}function Xt(t,e,o,r,s,i){var c,l,u;if(e.__d!==void 0)c=e.__d,e.__d=void 0;else if(o==null||s!=i||s.parentNode==null)t:if(i==null||i.parentNode!==t)t.appendChild(s),c=null;else{for(l=i,u=0;(l=l.nextSibling)&&u<r.length;u+=1)if(l==s)break t;t.insertBefore(s,i),c=i}return c!==void 0?c:s.nextSibling}function Qt(t){var e,o,r;if(t.type==null||typeof t.type=="string")return t.__e;if(t.__k){for(e=t.__k.length-1;e>=0;e--)if((o=t.__k[e])&&(r=Qt(o)))return r}return null}function en(t,e,o,r,s){var i;for(i in o)i==="children"||i==="key"||i in e||rt(t,i,null,o[i],r);for(i in e)s&&typeof e[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||o[i]===e[i]||rt(t,i,e[i],o[i],r)}function Nt(t,e,o){e[0]==="-"?t.setProperty(e,o??""):t[e]=o==null?"":typeof o!="number"||tn.test(e)?o:o+"px"}function rt(t,e,o,r,s){var i;t:if(e==="style")if(typeof o=="string")t.style.cssText=o;else{if(typeof r=="string"&&(t.style.cssText=r=""),r)for(e in r)o&&e in o||Nt(t.style,e,"");if(o)for(e in o)r&&o[e]===r[e]||Nt(t.style,e,o[e])}else if(e[0]==="o"&&e[1]==="n")i=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase()in t?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+i]=o,o?r||t.addEventListener(e,i?Ut:Ot,i):t.removeEventListener(e,i?Ut:Ot,i);else if(e!=="dangerouslySetInnerHTML"){if(s)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e!=="rowSpan"&&e!=="colSpan"&&e in t)try{t[e]=o??"";break t}catch{}typeof o=="function"||(o==null||o===!1&&e[4]!=="-"?t.removeAttribute(e):t.setAttribute(e,o))}}function Ot(t){return this.l[t.type+!1](_.event?_.event(t):t)}function Ut(t){return this.l[t.type+!0](_.event?_.event(t):t)}function ht(t,e,o,r,s,i,c,l,u){var d,a,m,g,p,f,w,h,C,M,Q,F,Et,K,ft,P=e.type;if(e.constructor!==void 0)return null;o.__h!=null&&(u=o.__h,l=e.__e=o.__e,e.__h=null,i=[l]),(d=_.__b)&&d(e);try{t:if(typeof P=="function"){if(h=e.props,C=(d=P.contextType)&&r[d.__c],M=d?C?C.props.value:d.__:r,o.__c?w=(a=e.__c=o.__c).__=a.__E:("prototype"in P&&P.prototype.render?e.__c=a=new P(h,M):(e.__c=a=new et(h,M),a.constructor=P,a.render=on),C&&C.sub(a),a.props=h,a.state||(a.state={}),a.context=M,a.__n=r,m=a.__d=!0,a.__h=[],a._sb=[]),a.__s==null&&(a.__s=a.state),P.getDerivedStateFromProps!=null&&(a.__s==a.state&&(a.__s=I({},a.__s)),I(a.__s,P.getDerivedStateFromProps(h,a.__s))),g=a.props,p=a.state,a.__v=e,m)P.getDerivedStateFromProps==null&&a.componentWillMount!=null&&a.componentWillMount(),a.componentDidMount!=null&&a.__h.push(a.componentDidMount);else{if(P.getDerivedStateFromProps==null&&h!==g&&a.componentWillReceiveProps!=null&&a.componentWillReceiveProps(h,M),!a.__e&&a.shouldComponentUpdate!=null&&a.shouldComponentUpdate(h,a.__s,M)===!1||e.__v===o.__v){for(e.__v!==o.__v&&(a.props=h,a.state=a.__s,a.__d=!1),a.__e=!1,e.__e=o.__e,e.__k=o.__k,e.__k.forEach(function(Y){Y&&(Y.__=e)}),Q=0;Q<a._sb.length;Q++)a.__h.push(a._sb[Q]);a._sb=[],a.__h.length&&c.push(a);break t}a.componentWillUpdate!=null&&a.componentWillUpdate(h,a.__s,M),a.componentDidUpdate!=null&&a.__h.push(function(){a.componentDidUpdate(g,p,f)})}if(a.context=M,a.props=h,a.__P=t,F=_.__r,Et=0,"prototype"in P&&P.prototype.render){for(a.state=a.__s,a.__d=!1,F&&F(e),d=a.render(a.props,a.state,a.context),K=0;K<a._sb.length;K++)a.__h.push(a._sb[K]);a._sb=[]}else do a.__d=!1,F&&F(e),d=a.render(a.props,a.state,a.context),a.state=a.__s;while(a.__d&&++Et<25);a.state=a.__s,a.getChildContext!=null&&(r=I(I({},r),a.getChildContext())),m||a.getSnapshotBeforeUpdate==null||(f=a.getSnapshotBeforeUpdate(g,p)),Gt(t,vt(ft=d!=null&&d.type===x&&d.key==null?d.props.children:d)?ft:[ft],e,o,r,s,i,c,l,u),a.base=e.__e,e.__h=null,a.__h.length&&c.push(a),w&&(a.__E=a.__=null),a.__e=!1}else i==null&&e.__v===o.__v?(e.__k=o.__k,e.__e=o.__e):e.__e=nn(o.__e,e,o,r,s,i,c,u);(d=_.diffed)&&d(e)}catch(Y){e.__v=null,(u||i!=null)&&(e.__e=l,e.__h=!!u,i[i.indexOf(l)]=null),_.__e(Y,e,o)}}function Kt(t,e){_.__c&&_.__c(e,t),t.some(function(o){try{t=o.__h,o.__h=[],t.some(function(r){r.call(o)})}catch(r){_.__e(r,o.__v)}})}function nn(t,e,o,r,s,i,c,l){var u,d,a,m=o.props,g=e.props,p=e.type,f=0;if(p==="svg"&&(s=!0),i!=null){for(;f<i.length;f++)if((u=i[f])&&"setAttribute"in u==!!p&&(p?u.localName===p:u.nodeType===3)){t=u,i[f]=null;break}}if(t==null){if(p===null)return document.createTextNode(g);t=s?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,g.is&&g),i=null,l=!1}if(p===null)m===g||l&&t.data===g||(t.data=g);else{if(i=i&&it.call(t.childNodes),d=(m=o.props||nt).dangerouslySetInnerHTML,a=g.dangerouslySetInnerHTML,!l){if(i!=null)for(m={},f=0;f<t.attributes.length;f++)m[t.attributes[f].name]=t.attributes[f].value;(a||d)&&(a&&(d&&a.__html==d.__html||a.__html===t.innerHTML)||(t.innerHTML=a&&a.__html||""))}if(en(t,g,m,s,l),a)e.__k=[];else if(Gt(t,vt(f=e.props.children)?f:[f],e,o,r,s&&p!=="foreignObject",i,c,i?i[0]:o.__k&&Z(o,0),l),i!=null)for(f=i.length;f--;)i[f]!=null&&Zt(i[f]);l||("value"in g&&(f=g.value)!==void 0&&(f!==t.value||p==="progress"&&!f||p==="option"&&f!==m.value)&&rt(t,"value",f,m.value,!1),"checked"in g&&(f=g.checked)!==void 0&&f!==t.checked&&rt(t,"checked",f,m.checked,!1))}return t}function Yt(t,e,o){try{typeof t=="function"?t(e):t.current=e}catch(r){_.__e(r,o)}}function te(t,e,o){var r,s;if(_.unmount&&_.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||Yt(r,null,e)),(r=t.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(i){_.__e(i,e)}r.base=r.__P=null,t.__c=void 0}if(r=t.__k)for(s=0;s<r.length;s++)r[s]&&te(r[s],e,o||typeof t.type!="function");o||t.__e==null||Zt(t.__e),t.__=t.__e=t.__d=void 0}function on(t,e,o){return this.constructor(t,o)}function ee(t,e,o){var r,s,i;_.__&&_.__(t,e),s=(r=typeof o=="function")?null:o&&o.__k||e.__k,i=[],ht(e,t=(!r&&o||e).__k=n(x,null,[t]),s||nt,nt,e.ownerSVGElement!==void 0,!r&&o?[o]:s?null:e.firstChild?it.call(e.childNodes):null,i,!r&&o?o:s?s.__e:e.firstChild,r),Kt(i,t)}it=Ft.slice,_={__e:function(t,e,o,r){for(var s,i,c;e=e.__;)if((s=e.__c)&&!s.__)try{if((i=s.constructor)&&i.getDerivedStateFromError!=null&&(s.setState(i.getDerivedStateFromError(t)),c=s.__d),s.componentDidCatch!=null&&(s.componentDidCatch(t,r||{}),c=s.__d),c)return s.__E=s}catch(l){t=l}throw t}},qt=0,Ke=function(t){return t!=null&&t.constructor===void 0},et.prototype.setState=function(t,e){var o;o=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=I({},this.state),typeof t=="function"&&(t=t(I({},o),this.props)),t&&I(o,t),t!=null&&this.__v&&(e&&this._sb.push(e),Rt(this))},et.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),Rt(this))},et.prototype.render=x,O=[],Wt=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,_t=function(t,e){return t.__v.__b-e.__v.__b},ot.__r=0,Ye=0;var lt,S,wt,ne,bt=0,ce=[],at=[],oe=_.__b,re=_.__r,ie=_.diffed,ae=_.__c,se=_.unmount;function ue(t,e){_.__h&&_.__h(S,t,bt||e),bt=0;var o=S.__H||(S.__H={__:[],__h:[]});return t>=o.__.length&&o.__.push({__V:at}),o.__[t]}function b(t){return bt=1,rn(de,t)}function rn(t,e,o){var r=ue(lt++,2);if(r.t=t,!r.__c&&(r.__=[o?o(e):de(void 0,e),function(l){var u=r.__N?r.__N[0]:r.__[0],d=r.t(u,l);u!==d&&(r.__N=[d,r.__[1]],r.__c.setState({}))}],r.__c=S,!S.u)){var s=function(l,u,d){if(!r.__c.__H)return!0;var a=r.__c.__H.__.filter(function(g){return g.__c});if(a.every(function(g){return!g.__N}))return!i||i.call(this,l,u,d);var m=!1;return a.forEach(function(g){if(g.__N){var p=g.__[0];g.__=g.__N,g.__N=void 0,p!==g.__[0]&&(m=!0)}}),!(!m&&r.__c.props===l)&&(!i||i.call(this,l,u,d))};S.u=!0;var i=S.shouldComponentUpdate,c=S.componentWillUpdate;S.componentWillUpdate=function(l,u,d){if(this.__e){var a=i;i=void 0,s(l,u,d),i=a}c&&c.call(this,l,u,d)},S.shouldComponentUpdate=s}return r.__N||r.__}function k(t,e){var o=ue(lt++,3);!_.__s&&ln(o.__H,e)&&(o.__=t,o.i=e,S.__H.__h.push(o))}function an(){for(var t;t=ce.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(st),t.__H.__h.forEach(Ct),t.__H.__h=[]}catch(e){t.__H.__h=[],_.__e(e,t.__v)}}_.__b=function(t){S=null,oe&&oe(t)},_.__r=function(t){re&&re(t),lt=0;var e=(S=t.__c).__H;e&&(wt===S?(e.__h=[],S.__h=[],e.__.forEach(function(o){o.__N&&(o.__=o.__N),o.__V=at,o.__N=o.i=void 0})):(e.__h.forEach(st),e.__h.forEach(Ct),e.__h=[],lt=0)),wt=S},_.diffed=function(t){ie&&ie(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(ce.push(e)!==1&&ne===_.requestAnimationFrame||((ne=_.requestAnimationFrame)||sn)(an)),e.__H.__.forEach(function(o){o.i&&(o.__H=o.i),o.__V!==at&&(o.__=o.__V),o.i=void 0,o.__V=at})),wt=S=null},_.__c=function(t,e){e.some(function(o){try{o.__h.forEach(st),o.__h=o.__h.filter(function(r){return!r.__||Ct(r)})}catch(r){e.some(function(s){s.__h&&(s.__h=[])}),e=[],_.__e(r,o.__v)}}),ae&&ae(t,e)},_.unmount=function(t){se&&se(t);var e,o=t.__c;o&&o.__H&&(o.__H.__.forEach(function(r){try{st(r)}catch(s){e=s}}),o.__H=void 0,e&&_.__e(e,o.__v))};var le=typeof requestAnimationFrame=="function";function sn(t){var e,o=function(){clearTimeout(r),le&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(o,100);le&&(e=requestAnimationFrame(o))}function st(t){var e=S,o=t.__c;typeof o=="function"&&(t.__c=void 0,o()),S=e}function Ct(t){var e=S;t.__c=t.__(),S=e}function ln(t,e){return!t||t.length!==e.length||e.some(function(o,r){return o!==t[r]})}function de(t,e){return typeof e=="function"?e(t):e}var H=chrome,me=!0,yt="https://api.nopecha.com",A="https://www.nopecha.com",pe="https://developers.nopecha.com",y={doc:{url:pe,automation:{url:`${pe}/guides/extension_advanced/#automation-build`}},api:{url:yt,recognition:{url:`${yt}/recognition`},status:{url:`${yt}/status`}},www:{url:A,annoucement:{url:`${A}/json/announcement.json`},demo:{url:`${A}/captcha`,recaptcha:{url:`${A}/captcha/recaptcha`},funcaptcha:{url:`${A}/captcha/funcaptcha`},awscaptcha:{url:`${A}/captcha/awscaptcha`},textcaptcha:{url:`${A}/captcha/textcaptcha`},turnstile:{url:`${A}/captcha/turnstile`},perimeterx:{url:`${A}/captcha/perimeterx`},geetest:{url:`${A}/captcha/geetest`},lemincaptcha:{url:`${A}/captcha/lemincaptcha`}},manage:{url:`${A}/manage`},pricing:{url:`${A}/pricing`},setup:{url:`${A}/setup`}},discord:{url:`${A}/discord`},github:{url:`${A}/github`,release:{url:`${A}/github/release`}}};function ge(t){let e=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+t).split("").map(o=>o.charCodeAt(0));return _e(e)}var fe=new Uint32Array(256);for(let t=256;t--;){let e=t;for(let o=8;o--;)e=e&1?3988292384^e>>>1:e>>>1;fe[t]=e}function _e(t){let e=-1;for(let o of t)e=e>>>8^fe[e&255^o];return(e^-1)>>>0}async function v(t,e){let o=""+[+new Date,performance.now(),Math.random()],[r,s]=await new Promise(i=>{H.runtime.sendMessage([o,t,...e],i)});if(r===ge(o))return s}function ve(){let t;return e=>t||(t=e().finally(()=>t=void 0),t)}function ct(t){return new Promise(e=>setTimeout(e,t))}var cn=[];async function un(t,e){if(t.timedout)return!1;let o=t.condition(e);if(o===t.running())return!1;if(!o&&t.running())return t.quit(),!1;if(o&&!t.running()){for(;!t.ready();)await ct(200);return t.start(),!1}}function he(t){cn.forEach(e=>un(e,t))}var dn=ve(),$;function we(){return dn(async()=>($||($=await v("settings::get",[])),$))}function be(t){$&&($={...$,...t},he($))}function L(){return $}function Ce(){H.runtime.connect({name:"stream"}).onMessage.addListener(e=>{e.event==="settingsUpdate"&&be(e.settings)})}function ut({children:t}){return n("main",{id:"loading",class:"center"},n("div",{class:"glow"},n("img",{src:"/icon/32.png",alt:"NopeCHA logo"})),n("h2",null,t))}function ye(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{"fill-rule":"evenodd",d:"M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"}))}function B(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{"fill-rule":"evenodd",d:"M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"}))}function U(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{"fill-rule":"evenodd",d:"M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"}))}function xe(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"m13.498.795.149-.149a1.207 1.207 0 1 1 1.707 1.708l-.149.148a1.5 1.5 0 0 1-.059 2.059L4.854 14.854a.5.5 0 0 1-.233.131l-4 1a.5.5 0 0 1-.606-.606l1-4a.5.5 0 0 1 .131-.232l9.642-9.642a.5.5 0 0 0-.642.056L6.854 4.854a.5.5 0 1 1-.708-.708L9.44.854A1.5 1.5 0 0 1 11.5.796a1.5 1.5 0 0 1 1.998-.001zm-.644.766a.5.5 0 0 0-.707 0L1.95 11.756l-.764 3.057 3.057-.764L14.44 3.854a.5.5 0 0 0 0-.708l-1.585-1.585z"}))}function Se(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"}))}function ke(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207V13.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V7.207l5-5 5 5Z"}))}function Ae(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"}))}function Le(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M13.545 2.907a13.227 13.227 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.19 12.19 0 0 0-3.658 0 8.258 8.258 0 0 0-.412-.833.051.051 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.041.041 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032c.001.014.01.028.021.037a13.276 13.276 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019c.308-.42.582-.863.818-1.329a.05.05 0 0 0-.01-.059.051.051 0 0 0-.018-.011 8.875 8.875 0 0 1-1.248-.595.05.05 0 0 1-.02-.066.051.051 0 0 1 .015-.019c.084-.063.168-.129.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.052.052 0 0 1 .053.007c.08.066.164.132.248.195a.051.051 0 0 1-.004.085 8.254 8.254 0 0 1-1.249.594.05.05 0 0 0-.03.03.052.052 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.235 13.235 0 0 0 4.001-2.02.049.049 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.034.034 0 0 0-.02-.019Zm-8.198 7.307c-.789 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612Zm5.316 0c-.788 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612Z"}))}function Me(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M8.5 10c-.276 0-.5-.448-.5-1s.224-1 .5-1 .5.448.5 1-.224 1-.5 1z"}),n("path",{d:"M10.828.122A.5.5 0 0 1 11 .5V1h.5A1.5 1.5 0 0 1 13 2.5V15h1.5a.5.5 0 0 1 0 1h-13a.5.5 0 0 1 0-1H3V1.5a.5.5 0 0 1 .43-.495l7-1a.5.5 0 0 1 .398.117zM11.5 2H11v13h1V2.5a.5.5 0 0 0-.5-.5zM4 1.934V15h6V1.077l-6 .857z"}))}function Te(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M6 12.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5ZM3 8.062C3 6.76 4.235 5.765 5.53 5.886a26.58 26.58 0 0 0 4.94 0C11.765 5.765 13 6.76 13 8.062v1.157a.933.933 0 0 1-.765.935c-.845.147-2.34.346-4.235.346-1.895 0-3.39-.2-4.235-.346A.933.933 0 0 1 3 9.219V8.062Zm4.542-.827a.25.25 0 0 0-.217.068l-.92.9a24.767 24.767 0 0 1-1.871-.183.25.25 0 0 0-.068.495c.55.076 1.232.149 2.02.193a.25.25 0 0 0 .189-.071l.754-.736.847 1.71a.25.25 0 0 0 .404.062l.932-.97a25.286 25.286 0 0 0 1.922-.188.25.25 0 0 0-.068-.495c-.538.074-1.207.145-1.98.189a.25.25 0 0 0-.166.076l-.754.785-.842-1.7a.25.25 0 0 0-.182-.135Z"}),n("path",{d:"M8.5 1.866a1 1 0 1 0-1 0V3h-2A4.5 4.5 0 0 0 1 7.5V8a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v1a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-1a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1v-.5A4.5 4.5 0 0 0 10.5 3h-2V1.866ZM14 7.5V13a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7.5A3.5 3.5 0 0 1 5.5 4h5A3.5 3.5 0 0 1 14 7.5Z"}))}function Pe(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"}),n("path",{d:"M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z"}))}function He(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M8.515 1.019A7 7 0 0 0 8 1V0a8 8 0 0 1 .589.022l-.074.997zm2.004.45a7.003 7.003 0 0 0-.985-.299l.219-.976c.383.086.76.2 1.126.342l-.36.933zm1.37.71a7.01 7.01 0 0 0-.439-.27l.493-.87a8.025 8.025 0 0 1 .979.654l-.615.789a6.996 6.996 0 0 0-.418-.302zm1.834 1.79a6.99 6.99 0 0 0-.653-.796l.724-.69c.27.285.52.59.747.91l-.818.576zm.744 1.352a7.08 7.08 0 0 0-.214-.468l.893-.45a7.976 7.976 0 0 1 .45 1.088l-.95.313a7.023 7.023 0 0 0-.179-.483zm.53 2.507a6.991 6.991 0 0 0-.1-1.025l.985-.17c.067.386.106.778.116 1.17l-1 .025zm-.131 1.538c.033-.17.06-.339.081-.51l.993.123a7.957 7.957 0 0 1-.23 1.155l-.964-.267c.046-.165.086-.332.12-.501zm-.952 2.379c.184-.29.346-.594.486-.908l.914.405c-.16.36-.345.706-.555 1.038l-.845-.535zm-.964 1.205c.122-.122.239-.248.35-.378l.758.653a8.073 8.073 0 0 1-.401.432l-.707-.707z"}),n("path",{d:"M8 1a7 7 0 1 0 4.95 11.95l.707.707A8.001 8.001 0 1 1 8 0v1z"}),n("path",{d:"M7.5 3a.5.5 0 0 1 .5.5v5.21l3.248 1.856a.5.5 0 0 1-.496.868l-3.5-2A.5.5 0 0 1 7 9V3.5a.5.5 0 0 1 .5-.5z"}))}function xt(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M0 10.5a.5.5 0 0 1 .5-.5h15a.5.5 0 0 1 0 1H.5a.5.5 0 0 1-.5-.5zM12 0H4a2 2 0 0 0-2 2v7h1V2a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v7h1V2a2 2 0 0 0-2-2zm2 12h-1v2a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-2H2v2a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-2z"}))}function dt(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M13.5 1a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM11 2.5a2.5 2.5 0 1 1 .603 1.628l-6.718 3.12a2.499 2.499 0 0 1 0 1.504l6.718 3.12a2.5 2.5 0 1 1-.488.876l-6.718-3.12a2.5 2.5 0 1 1 0-3.256l6.718-3.12A2.5 2.5 0 0 1 11 2.5zm-8.5 4a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zm11 5.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3z"}))}function Be(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{"fill-rule":"evenodd",d:"M5 11.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z"}),n("path",{d:"M1.713 11.865v-.474H2c.217 0 .363-.137.363-.317 0-.185-.158-.31-.361-.31-.223 0-.367.152-.373.31h-.59c.016-.467.373-.787.986-.787.588-.002.954.291.957.703a.595.595 0 0 1-.492.594v.033a.615.615 0 0 1 .569.631c.003.533-.502.8-1.051.8-.656 0-1-.37-1.008-.794h.582c.008.178.186.306.422.309.254 0 .424-.145.422-.35-.002-.195-.155-.348-.414-.348h-.3zm-.004-4.699h-.604v-.035c0-.408.295-.844.958-.844.583 0 .96.326.96.756 0 .389-.257.617-.476.848l-.537.572v.03h1.054V9H1.143v-.395l.957-.99c.138-.142.293-.304.293-.508 0-.18-.147-.32-.342-.32a.33.33 0 0 0-.342.338v.041zM2.564 5h-.635V2.924h-.031l-.598.42v-.567l.629-.443h.635V5z"}))}function De(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:"bi bi-link-45deg",viewBox:"0 0 16 16"},n("path",{d:"M4.715 6.542 3.343 7.914a3 3 0 1 0 4.243 4.243l1.828-1.829A3 3 0 0 0 8.586 5.5L8 6.086a1.002 1.002 0 0 0-.154.199 2 2 0 0 1 .861 3.337L6.88 11.45a2 2 0 1 1-2.83-2.83l.793-.792a4.018 4.018 0 0 1-.128-1.287z"}),n("path",{d:"M6.586 4.672A3 3 0 0 0 7.414 9.5l.775-.776a2 2 0 0 1-.896-3.346L9.12 3.55a2 2 0 1 1 2.83 2.83l-.793.792c.112.42.155.855.128 1.287l1.372-1.372a3 3 0 1 0-4.243-4.243L6.586 4.672z"}))}function Ie(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"}))}function St(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"}))}function $e(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"M7.5 1v7h1V1h-1z"}),n("path",{d:"M3 8.812a4.999 4.999 0 0 1 2.578-4.375l-.485-.874A6 6 0 1 0 11 3.616l-.501.865A5 5 0 1 1 3 8.812z"}))}function ze(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:"bi bi-x-lg",viewBox:"0 0 16 16"},n("path",{d:"M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8 2.146 2.854Z"}))}function Ee(){return n("svg",{width:"16",height:"16",viewBox:"0 0 70 70"},n("path",{d:"M64 31.955l-.033-1.37V4.687l-7.16 7.16C50.948 4.674 42.033.093 32.05.093c-10.4 0-19.622 4.96-25.458 12.64l11.736 11.86a15.55 15.55 0 0 1 4.754-5.334c2.05-1.6 4.952-2.906 8.968-2.906.485 0 .86.057 1.135.163 4.976.393 9.288 3.14 11.828 7.124l-8.307 8.307L64 31.953",fill:"#1c3aa9"}),n("path",{d:"M31.862.094l-1.37.033H4.594l7.16 7.16C4.58 13.147 0 22.06 0 32.046c0 10.4 4.96 19.622 12.64 25.458L24.5 45.768a15.55 15.55 0 0 1-5.334-4.754c-1.6-2.05-2.906-4.952-2.906-8.968 0-.485.057-.86.163-1.135.393-4.976 3.14-9.288 7.124-11.828l8.307 8.307L31.86.095",fill:"#4285f4"}),n("path",{d:"M.001 32.045l.033 1.37v25.898l7.16-7.16c5.86 7.173 14.774 11.754 24.76 11.754 10.4 0 19.622-4.96 25.458-12.64l-11.736-11.86a15.55 15.55 0 0 1-4.754 5.334c-2.05 1.6-4.952 2.906-8.968 2.906-.485 0-.86-.057-1.135-.163-4.976-.393-9.288-3.14-11.828-7.124l8.307-8.307c-10.522.04-22.4.066-27.295-.005",fill:"#ababab"}))}function Ve(){return n("svg",{width:"16",height:"16",viewBox:"18 30 37 34"},n("path",{d:"M52.107,37.991,38.249,30a3.992,3.992,0,0,0-1.919-.533A3.606,3.606,0,0,0,34.412,30L20.555,37.991a3.829,3.829,0,0,0-1.919,3.3V57.338a3.9,3.9,0,0,0,1.919,3.3l.959.533,4.423,2.558V56.326l10.393-5.969,10.393,5.969v7.355l4.423-2.558.959-.586a3.829,3.829,0,0,0,1.919-3.3V41.243A3.857,3.857,0,0,0,52.107,37.991ZM46.617,47.9,38.2,43a3.99,3.99,0,0,0-1.918-.533A3.607,3.607,0,0,0,34.359,43l-8.474,4.9V43.268l8.688-5.01a3.425,3.425,0,0,1,3.358,0l8.688,5.01Z",fill:"#50b95d"}))}function Re(){return n("svg",{width:"16",height:"16",viewBox:"0 0 256 310"},n("path",{d:"M0 173.367l.985.52 15.49 1.762 19.455-1.082.856-.45-17.267-1.501-19.519.75z",fill:"#B6C99C"}),n("path",{d:"M128 .698L73.948 27.724V201.23L128 211.148l1.85-2.5V5.148L128 .699z",fill:"#4C612C"}),n("path",{d:"M128 .698v217.7l54.053-16.141V27.724L128 .698z",fill:"#769B3F"}),n("path",{d:"M219.214 174.117l.922.623 19.339 1.074 15.656-1.779.869-.669-19.52-.75-17.266 1.501z",fill:"#B6C99C"}),n("path",{d:"M219.214 210.153l20.27 2.627.543-.998v-35.397l-.543-1.141-20.27-1.126v36.035z",fill:"#4C612C"}),n("path",{d:"M36.786 210.153l-20.27 2.627-.342-.925v-36.001l.342-.61 20.27-1.126v36.035z",fill:"#769B3F"}),n("path",{d:"M125.748 208.651l-89.713-15.765-19.52 1.876.889.891 85.223 17.265.974-.513 22.147-3.754z",fill:"#B6C99C"}),n("path",{d:"M0 191.385v54.428L89.713 290.8v.055L128 310l1.6-3.002v-118.85l-1.6-3.746-38.287-3.753v28.888l-73.197-14.81v-19.483L0 173.367v18.018z",fill:"#4C612C"}),n("path",{d:"M128 209.026l21.771 3.754 2.804.118 85.285-17.129 1.624-1.007-19.144-1.877L128 209.026z",fill:"#B6C99C"}),n("path",{d:"M239.484 175.243v19.483l-73.196 14.811v-30.165L128 183.126V310l128-64.188v-72.446l-16.516 1.877z",fill:"#769B3F"}),n("path",{d:"M166.287 182.375L128 179.372l-38.288 3.003L128 186.13l38.287-3.754z",fill:"#B6C99C"}))}function Ne(){return n("svg",{width:"16",height:"16",viewBox:"5 5 42 42",fill:"#f38020",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M27.3146 7.26134C22.346 7.11258 17.5089 8.87182 13.7968 12.1778l1.2307-6.74308-3.1939-.58208L9.67207 16.6889 21.5124 18.8498 22.0945 15.6559l-6.0801-1.1096c2.9126-2.5542 6.6454-3.9781 10.5192-4.0126C30.4073 10.4993 34.1648 11.8565 37.1224 14.3585s4.9188 5.9826 5.5269 9.8085C43.2573 27.9929 42.4718 31.91 40.4356 35.2057c-2.0362 3.2956-5.1877 5.7509-8.8813 6.9191S23.8704 43.0965 20.3094 41.5711c-3.5609-1.5254-6.456-4.2784-8.1586-7.7582-1.7026-3.4797-2.0995-7.455-1.1185-11.2027L7.89023 21.7861c-1.02566 3.9081-.81053 8.0386.61559 11.8191C9.93194 37.3856 12.4985 40.629 15.8498 42.8861c3.3514 2.257 7.3217 3.4159 11.361 3.3162C31.2501 46.1025 35.1584 44.7491 38.3943 42.3294c3.2359-2.4196 5.6393-5.7858 6.8771-9.632C46.5092 28.8511 46.5202 24.7151 45.3029 20.8623c-1.2173-3.8528-3.6026-7.2317-6.8256-9.6685-3.2229-2.43693-7.124-3.81119-11.1627-3.93246z"}),n("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M38.8474 21.9189 35.9285 19 24.4761 30.4524l-4.5531-4.553L17 28.8224l7.4833 7.4832 2.923-2.923L27.3949 33.3713 38.8474 21.9189z"}))}function Oe(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",viewBox:"0 0 16 16"},n("path",{d:"m2.244 13.081.943-2.803H6.66l.944 2.803H8.86L5.54 3.75H4.322L1 13.081h1.244zm2.7-7.923L6.34 9.314H3.51l1.4-4.156h.034zm9.146 7.027h.035v.896h1.128V8.125c0-1.51-1.114-2.345-2.646-2.345-1.736 0-2.59.916-2.666 2.174h1.108c.068-.718.595-1.19 1.517-1.19.971 0 1.518.52 1.518 1.464v.731H12.19c-1.647.007-2.522.8-2.522 2.058 0 1.319.957 2.18 2.345 2.18 1.06 0 1.716-.43 2.078-1.011zm-1.763.035c-.752 0-1.456-.397-1.456-1.244 0-.65.424-1.115 1.408-1.115h1.805v.834c0 .896-.752 1.525-1.757 1.525z"}))}function Ue(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"20 40 220 220",fill:"#e52b50","fill-rule":"evenodd","clip-rule":"evenodd"},n("g",null,n("path",{d:"M 69.5,77.5 C 79.233,77.1091 88.8997,77.6091 98.5,79C 112.532,83.3672 120.365,92.8672 122,107.5C 122.667,124.833 122.667,142.167 122,159.5C 119.43,178.383 108.596,188.55 89.5,190C 77.1659,191.171 66.8326,187.338 58.5,178.5C 57.5007,193.152 57.1674,207.818 57.5,222.5C 51.6973,223.087 46.1973,222.42 41,220.5C 40.3333,184.167 40.3333,147.833 41,111.5C 42.4489,93.2316 51.9489,81.8983 69.5,77.5 Z M 77.5,91.5 C 92.4836,89.7086 101.65,96.0419 105,110.5C 105.667,125.833 105.667,141.167 105,156.5C 101.267,171.296 91.7671,177.462 76.5,175C 64.9759,172.125 58.8092,164.625 58,152.5C 57.3333,139.5 57.3333,126.5 58,113.5C 59.1531,101.191 65.6531,93.8582 77.5,91.5 Z"})),n("g",null,n("path",{d:"M 151.5,77.5 C 155.273,77.2633 158.94,77.7633 162.5,79C 170.65,87.9844 178.984,96.8177 187.5,105.5C 196.374,96.1272 205.374,86.9606 214.5,78C 218.782,77.0349 222.782,77.7016 226.5,80C 231.387,84.8874 236.221,89.7208 241,94.5C 242.899,98.1361 242.899,101.803 241,105.5C 231.935,114.642 223.102,123.975 214.5,133.5C 222.118,142.62 230.284,151.286 239,159.5C 242.597,163.411 243.263,167.744 241,172.5C 234.842,179.997 227.509,185.997 219,190.5C 217.5,190 216,189.5 214.5,189C 205.127,180.803 196.294,171.969 188,162.5C 178.9,170.267 170.4,178.767 162.5,188C 160.162,188.837 157.828,189.67 155.5,190.5C 153.618,189.469 151.618,188.635 149.5,188C 144,183.167 138.833,178 134,172.5C 133.146,168.375 133.48,164.375 135,160.5C 141.198,153.968 147.698,147.801 154.5,142C 162.161,138.685 170.161,137.685 178.5,139C 181.698,139.848 184.531,141.348 187,143.5C 190.071,140.352 192.904,137.019 195.5,133.5C 193.111,130.034 190.278,127.034 187,124.5C 175.405,131.295 163.905,131.128 152.5,124C 146.64,118.14 140.806,112.306 135,106.5C 133.48,102.625 133.146,98.6251 134,94.5C 139.782,88.5481 145.615,82.8814 151.5,77.5 Z M 155.5,91.5 C 163.356,98.5222 170.689,106.022 177.5,114C 170.832,117.407 164.499,116.741 158.5,112C 155.167,107.333 151.167,103.333 146.5,100C 149.396,96.9343 152.396,94.1009 155.5,91.5 Z M 218.5,91.5 C 221.751,93.3492 224.585,95.6826 227,98.5C 227.667,99.5 227.667,100.5 227,101.5C 219.707,108.628 212.54,115.961 205.5,123.5C 202.323,120.822 199.323,117.988 196.5,115C 204.064,107.384 211.397,99.5509 218.5,91.5 Z M 204.5,143.5 C 212.846,150.846 220.513,158.846 227.5,167.5C 224.804,170.273 221.971,172.94 219,175.5C 211.445,167.614 203.945,159.78 196.5,152C 199.427,149.245 202.094,146.411 204.5,143.5 Z M 166.5,150.5 C 170.463,150.089 174.13,150.922 177.5,153C 170.69,160.737 163.523,168.237 156,175.5C 153.063,172.947 150.23,170.281 147.5,167.5C 152.502,160.194 158.836,154.528 166.5,150.5 Z"})))}function qe(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 32 33",fill:"#347eff"},n("g",null,n("path",{d:"M31.4132299,15.0731132 L21.5168412,15.0731132 C20.7685687,12.1674923 17.9820968,10.2729999 15.0128625,10.6511277 C12.0436282,11.0292556 9.81802689,13.5620285 9.81802689,16.5629409 C9.81802689,19.5638532 12.0436282,22.0966262 15.0128625,22.474754 C17.9820968,22.8528818 20.7685687,20.9583894 21.5168412,18.0527685 L28.3819036,18.0527685 C27.7401286,23.6109911 23.559668,28.1006161 18.0730494,29.1240101 C12.5864307,30.1474041 7.07526456,27.4655142 4.48272671,22.5105732 C1.89018885,17.5556323 2.82186608,11.4850571 6.78020672,7.5407695 C10.7385474,3.5964819 16.798929,2.69982177 21.7248734,5.32964028 C20.9283685,7.55588796 21.8580336,10.0328622 23.9207791,11.1803513 C25.9835245,12.3278405 28.5717136,11.8078209 30.0341839,9.95204462 C31.4966542,8.09626831 31.4025961,5.45140372 29.8120149,3.70476992 C28.2214338,1.95813612 25.6028777,1.62425074 23.626882,2.91611947 C17.4530059,-0.657685124 9.65473085,0.371648118 4.61376657,5.42575546 C-0.427197707,10.4798628 -1.45389595,18.2985118 2.11055502,24.488545 C5.67500598,30.6785781 12.9418168,33.696522 19.8278979,31.8466485 C26.713979,29.9967749 31.5022986,23.7403466 31.5023865,16.5927374 C31.5023865,15.9968064 31.4726676,15.4902649 31.4132299,15.0731132 Z M15.7513775,19.5425962 C14.1100488,19.5425962 12.779489,18.2085591 12.779489,16.5629409 C12.779489,14.9173227 14.1100488,13.5832855 15.7513775,13.5832855 C17.3927062,13.5832855 18.723266,14.9173227 18.723266,16.5629409 C18.723266,18.2085591 17.3927062,19.5425962 15.7513775,19.5425962 Z M26.2421439,5.18065751 C26.9633539,5.18065751 27.6135489,5.61623934 27.8895441,6.28429187 C28.1655392,6.95234441 28.0129819,7.72130613 27.5030094,8.2326114 C26.9930369,8.74391667 26.2260796,8.89687267 25.5597684,8.62015625 C24.8934572,8.34343983 24.4590108,7.69154556 24.4590108,6.96845071 C24.4590108,5.98107979 25.2573467,5.18065751 26.2421439,5.18065751 Z"})))}function We(){return n("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"70 1 28 1",fill:"none"},n("g",{"clip-path":"url(#clip0_23_2)"},n("path",{d:"M83.9885 8.61757C82.4077 8.61757 78.5295 7.4943 78.5295 7.4943C78.3607 7.44546 78.0868 7.35628 77.9212 7.29683L71.1872 4.86661C71.1479 4.8528 71.1118 4.83051 71.0811 4.80184C71.0503 4.77318 71.0248 4.7392 71.0078 4.70204C70.9897 4.66382 70.9802 4.62348 70.9791 4.58101C70.9781 4.5396 70.9844 4.4982 70.9993 4.45891L72.6003 0.207886C72.63 0.128259 72.6905 0.0645573 72.768 0.0295213C72.8455 -0.0055146 72.9336 -0.00976138 73.0132 0.0189044L79.9723 2.54362C80.1379 2.60413 80.4118 2.69331 80.5817 2.74215C80.5817 2.74215 83.0086 3.4503 83.9906 3.4503C84.9726 3.4503 87.3995 2.74215 87.3995 2.74215C87.5683 2.69331 87.8422 2.60413 88.0079 2.54362L94.9648 0.0189044C95.0444 -0.00976138 95.1336 -0.0055146 95.21 0.0295213C95.2875 0.0645573 95.348 0.129321 95.3778 0.207886L96.9787 4.45891C96.9936 4.4982 97 4.5396 96.9989 4.58101C96.9978 4.62242 96.9872 4.66382 96.9702 4.70204C96.9522 4.74026 96.9278 4.77424 96.897 4.80184C96.8662 4.83051 96.8301 4.85174 96.7908 4.86661L90.0568 7.29683C89.8912 7.35628 89.6173 7.44546 89.4485 7.49536C89.4485 7.49536 85.634 8.61651 83.9895 8.61651L83.9885 8.61757Z",fill:"#FFD832"})))}function kt(){let t=H.runtime.getManifest();return n("footer",null,n("a",{href:y.github.release.url,target:"_blank"},t.version_name??t.version),n("a",{href:y.www.url,target:"_blank"},"\xA9 2022 - ",new Date().getUTCFullYear()," NopeCHA"))}function T({title:t,leftAction:e,rightAction:o}){return n("nav",null,n("div",{class:e?"center":"invisible"},e),n("h1",null,t),n("div",{class:o?"center":"invisible"},o))}function At({goal:t,onGoal:e}){let[o,r]=b(Fe(t));return k(()=>{let s=setInterval(()=>{r(Fe(t))},1e3);return()=>clearInterval(s)},[t]),k(()=>{o==="00:00:00"&&e?.()},[o]),n("span",{class:"mono",title:"Time remaining"},o)}function Fe(t){let e=Math.max(0,t-new Date().getTime()/1e3)^0,o=e/3600^0;e%=3600;let r=e/60^0;e%=60;let s=e^0,i=Intl.NumberFormat(void 0,{minimumIntegerDigits:2});return[o,r,s].map(i.format).join(":")}var j={icon:Me,name:"Auto-Open",description:"Automatically opens CAPTCHA challenges.",id:"auto_open",type:0},z={icon:Te,name:"Auto-Solve",description:"Automatically solves CAPTCHA challenges.",id:"auto_solve",type:0},E={icon:Pe,name:"Delay Solving",description:"Adds a delay to avoid detection.",id:"solve_delay",type:0},V={icon:He,name:"Delay Timer",description:"Milliseconds to delay solving.",placeholder:"Enter delay",id:"solve_delay_time",type:1},q=[{icon:Ee,name:"reCAPTCHA",id:"recaptcha",href:y.www.demo.recaptcha.url,options:[j,z,E,V]},{icon:Ve,name:"FunCAPTCHA",id:"funcaptcha",href:y.www.demo.funcaptcha.url,options:[j,z,E,V]},{icon:Re,name:"AWS CAPTCHA",id:"awscaptcha",href:y.www.demo.awscaptcha.url,options:[j,z,E,V]},{icon:Oe,name:"Text CAPTCHA",id:"textcaptcha",href:y.www.demo.textcaptcha.url,options:[z,E,V,{icon:xt,name:"Image Element",description:"CSS selector for the captcha image.",placeholder:"Enter CSS",id:"image_selector",type:2},{icon:xt,name:"Input Element",description:"CSS selector for the captcha input.",placeholder:"Enter CSS",id:"input_selector",type:2}]},{icon:Ne,name:"Cloudflare Turnstile",id:"turnstile",href:y.www.demo.turnstile.url,options:[z,E,V]},{icon:Ue,name:"Human (PerimeterX)",id:"perimeterx",href:y.www.demo.perimeterx.url,options:[z,E,V]},{icon:qe,name:"GeeTest",id:"geetest",href:y.www.demo.geetest.url,options:[j,z,E,V]},{icon:We,name:"Lemin CAPTCHA (beta)",id:"lemincaptcha",href:y.www.demo.lemincaptcha.url,options:[j,z,E,V]}];function G(t,e){if("field"in t)return e[t.field];if("value"in t)return t.value;if("condition"in t){let s=G(t.condition,e);return t.type==="NOT"?!s:t.type==="PLUS"?+s:-s}let o=G(t.left,e);if(t.type==="AND"&&!o)return o;if(t.type==="OR"&&o)return o;let r=G(t.right,e);switch(t.type){case"EQ":return o===r;case"NEQ":return o!==r;case"LT":return o<r;case"LE":return o<=r;case"GT":return o>r;case"GE":return o>=r;case"AND":return o&&r;case"OR":return o||r;case"ADD":return o+r;case"SUB":return o-r;case"MULT":return o*r;case"DIV":return o/r;case"MOD":return o%r}}var pt=[];try{let t=localStorage.getItem("dismissed");t&&pt.push(...JSON.parse(t))}catch{}function pn({text:t,id:e,href:o,urgent:r}){let[s,i]=b(pt.includes(e));return e&&e.startsWith("!")&&document.documentElement?.remove(),s&&e?null:n("div",{class:`banner${r?" urgent":""}`},n("div",null),o?n("a",{target:"_blank",href:o},t):n("span",null,t),e?n("button",{onClick:()=>{i(!0),pt.push(e),localStorage.setItem("dismissed",JSON.stringify(pt))},title:"Dismiss"},n(ze,null)):n("div",null))}function W({status:t,location:e}){let[o,r]=b();if(k(()=>{if(me){let u=localStorage.getItem("announcements");u&&r(JSON.parse(u));let d=localStorage.getItem("announcements:last-fetched");if(d&&+d>new Date().getTime()-36e5)return}fetch(`${y.www.annoucement.url}`).then(u=>u.json()).then(u=>{r(u),localStorage.setItem("announcements",JSON.stringify(u)),localStorage.setItem("announcements:last-fetched",""+new Date().getTime())})},[]),!o)return;let s=L(),i=H.runtime.getManifest().version.split(".").map(u=>+u);for(;i.length<4;)i.push(0);let c={bucket:mn(),time:new Date().getTime(),version:i.reverse().reduce((u,d,a)=>u|(d?d<<8*a:0),0),status:t&&!("error"in t),"flag.automation":!1,"flag.debug":!1,...t?Ze(t,"status."):[],...Ze(s,"settings.")},l=o.filter(u=>u.location===e).filter(u=>G(u.condition,c));return n(x,null,l.map(u=>n(pn,{...u.banner})))}function Ze(t,e){return Object.fromEntries(Object.entries(t).map(([o,r])=>[e+o,r]))}function mn(){let t=localStorage.getItem("bucket");if(t!==null)return+t;let e=Math.random();return localStorage.setItem("bucket",""+e),e}function Lt(t){let[e,o]=b(L().enabled);return n(x,null,n(T,{title:"NopeCHA",leftAction:n("button",{onClick:()=>t.setState([3,""]),class:"center"},n(ye,null)),rightAction:n("button",{class:"power","data-enabled":e,onClick:()=>{o(!e),v("settings::update",[{enabled:!e}])}},n($e,null))}),n("main",null,n(W,{status:t.status,location:"main"}),n(gn,{...t}),n(yn,{setState:t.setState})),n(kt,null))}function gn({status:t,refreshing:e,refresh:o}){return n("div",{class:"status"},n(Cn,{status:t,refreshing:e,refresh:o}),"error"in t?n("div",{class:"warning center"},t.error===12?n(x,null,n("p",null,"Your IP is ineligible for free credits."),n("p",null,n("a",{href:y.www.pricing.url,target:"_blank"},"Purchase a key")," ","to use with VPN/proxy.")):t.error===17?n(x,null,n("p",null,"You are using an outdated extension version."),n("a",{href:y.github.release.url,target:"_blank"},"Download newest release")):t.message??"Unknown error, contact support."):n(vn,{status:t,refresh:()=>!e&&o()}))}var fn=["Enterprise","Admin"],_n=["Free","GitHub","Discord"];function vn({status:t,refresh:e}){let o=mt(t),r=typeof t.duration>"u"&&typeof t.lastreset>"u",s=je(t),i=hn(t),c=s<=Math.max(i/100,10),l=s<=i/2,u="";return r||o?u="Refills":t.duration<0?u="Expires in":u="Refills in",k(()=>{let d=document.querySelector("#prog-credit");if(d){let m=i!==0?Math.min(Math.floor(100*s/i),100):0;(o||r)&&(m=0),d.dataset.per=`${m}%`,d.style.width=`${m}%`}let a=document.querySelector("#prog-timeleft");if(a&&typeof t.lastreset<"u"&&typeof t.duration<"u"){let m=Math.min(Math.floor(100*(1-t.ttl/Math.abs(t.duration))),100);t.duration<0&&(m=100-m),(o||r)&&(m=0),a.dataset.per=`${m}%`,a.style.width=`${m}%`}},[t]),n(x,null,n("div",null,n("span",null,"Credits"),n("span",null)),n("div",{class:"border-botton"},n("span",{class:r||o||c?"value color-danger":l?"value color-warning":"value",title:"Credits remaining"},wn(t)),n("div",{class:"prog-main"},n("div",{class:"prog-bar"},n("div",{id:"prog-credit",class:"prog-per","data-per":"0%"})))),n("div",null,n("span",null,u),n("span",null)),n("div",null,n("span",{class:r||o?"value color-danger":"value"},r||o?"Unavailable":n(At,{goal:t.ttl+Date.now()/1e3,onGoal:()=>setTimeout(e,1500)})),n("div",{class:"prog-main"},n("div",{class:"prog-bar"},n("div",{id:"prog-timeleft",class:"prog-per","data-per":"0%"})))))}function mt(t){return!("status"in t&&typeof t.status<"u"&&t.status==="Active")}function je(t){let e=typeof t.credit<"u";return mt(t)?0:e?t.credit:0}function hn(t){let e=typeof t.quota<"u";return mt(t)?0:e?t.quota:0}function wn(t){return`${je(t).toLocaleString()}`}function bn(t){return"status"in t&&typeof t.status<"u"?t.status:""}function Cn({status:t,refreshing:e,refresh:o}){let[r,s]=b(!1),[i,c]=b(""),l=mt(t);return k(()=>{r&&c(L().key)},[r]),n(x,null,n("div",null,n("span",{title:"Plan name",class:"plan"in t?"":"color-danger"},"plan"in t?`${t.plan} Plan`:"Unavailable"),r?n("div",{class:"key-input-wrapper"},n("input",{class:"key-input",type:"text",value:i,autoComplete:"off",spellcheck:!1,placeholder:"Enter your key",autofocus:!0,onInput:u=>{let d=u.currentTarget.value;c(d),d.length>i.length+1&&v("settings::update",[{key:d}]).then(o)},onChange:u=>c(u.currentTarget.value),onKeyPress:u=>{u.key==="Enter"&&(u.preventDefault(),s(!1),v("settings::update",[{key:u.currentTarget.value}]).then(o))},onBlur:u=>{s(!1),v("settings::update",[{key:u.currentTarget.value}]).then(o)}})):n("span",null,n("button",{class:"key-icon font-normal color-brand",title:"Click to edit",onClick:()=>{s(!0)},disabled:e,onMouseUp:()=>{setTimeout(()=>{document.querySelector(".key-input")?.select()},10)}},!("key"in t)||"key"in t&&t.key===""?"Enter your key":"Change key",n(xe,null)))),n("div",{class:"border-botton"},n("span",{class:l?"value color-danger":"value",title:"Plan status"},bn(t)),n("a",{href:y.www.manage.url,target:"_blank",class:"key-icon font-normal color-brand",title:"Manage or upgrade plan"},!("plan"in t)||_n.includes(t.plan)?"Create a key":fn.includes(t.plan)?"Manage keys":"Upgrade",n("svg",{class:"link-icon",fill:"currentColor",viewBox:"0 0 16 16",version:"1.1",width:"16",height:"16"},n("path",{d:"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z"})))))}function yn({setState:t}){let[e,o]=b({}),r=i=>{let c=!e[i];o({...e,[i]:c}),v("settings::update",[{[i]:c}])},s=i=>{let c=!((!(`${i}_auto_open`in e)||e[`${i}_auto_open`])&&(!(`${i}_auto_solve`in e)||e[`${i}_auto_solve`]));`${i}_auto_open`in e&&e[`${i}_auto_open`]!==c&&r(`${i}_auto_open`),`${i}_auto_solve`in e&&e[`${i}_auto_solve`]!==c&&r(`${i}_auto_solve`)};return k(()=>{let i=L();o({...i})}),n("ol",{class:"captchas"},q.map(i=>n("li",{key:i.name},n("button",{onClick:()=>{t([5,i.id])},class:"icon"},n("span",null,i.icon?n(i.icon,null):n("div",{class:"no-icon"}),i.name),`${i.id}_auto_open`in e||`${i.id}_auto_solve`in e?n("span",{class:"inline-switch"},n("button",{class:"switch small",checked:(!(`${i.id}_auto_open`in e)||e[`${i.id}_auto_open`])&&(!(`${i.id}_auto_solve`in e)||e[`${i.id}_auto_solve`]),onClick:c=>{c.stopPropagation(),s(i.id)}})):"",n(U,null)))))}var xn=[{name:"Documentation and Guides",href:y.doc.url,icon:Se},{name:"Homepage",href:y.www.url,icon:ke},{name:"Discord",href:y.discord.url,icon:Le},{name:"Github",href:y.github.url,icon:Ae}];function Mt({setState:t}){return n(x,null,n(T,{title:"Settings",leftAction:n("button",{onClick:()=>t([2,""]),class:"center"},n(B,null))}),n("main",null,n(W,{location:"settings"}),n("ol",null,n("li",null,n("button",{onClick:()=>t([4,""]),class:"icon"},n("span",null,n(Be,null),"Disabled Hosts"),n(U,null))),n("li",null,n("button",{onClick:()=>{let e=L(),o=Object.entries(e).map(([r,s])=>r==="key"?s:r==="version"?"":`${r}=${s}`).filter(r=>r).join("|");open(`${y.www.setup.url}#${o}`)},class:"icon"},n("span",null,n(dt,null),"Export Settings"),n(U,null))),n("li",null,n("button",{onClick:()=>t([6,""]),class:"icon"},n("span",null,n(dt,null),"Debug"),n(U,null))),n("li",null,n("button",{onClick:()=>t([7,""]),class:"icon"},n("span",null,n(dt,null),"Logs"),n(U,null)))),n("hr",null),n("h2",null,"Links"),n("ol",null,xn.map(e=>n("li",{key:e.name},n("a",{href:e.href,target:"_blank",class:"icon"},n("span",null,n(e.icon,null),e.name),n(De,null)))))))}function Sn(){return new Promise(t=>{H.tabs.query({active:!0,currentWindow:!0},([e])=>t(e))})}async function gt(){let t=await Sn();return t&&t.url&&new URL(t.url).href}function Tt({setState:t}){let[e,o]=b(""),[r,s]=b(L().disabled_hosts);return k(()=>{gt().then(i=>i&&o(new URL(i).hostname))},[]),n(x,null,n(T,{title:"Disabled Hosts",leftAction:n("button",{onClick:()=>t([3,""]),class:"center"},n(B,null))}),n("main",{id:"hosts"},n("h3",null,"Current Page"),n("ol",null,n("li",null,e===""?n("button",{class:"icon cursor-normal"},"cannot disable on this page"):n("button",{class:"icon",title:r.includes(e)?"Remove disabled host":"Disable NopeCHA on this site",onClick:()=>{let i=r.includes(e)?r.filter(c=>c!==e):[...r,e];v("settings::update",[{disabled_hosts:i}]),s(i)}},e,r.includes(e)?n(St,null):n(Ie,null)))),n("h3",null,"Disabled Hosts"),n("ol",null,r.length===0?n("li",null,n("button",{class:"icon cursor-normal"},"no disabled hosts")):r.map(i=>n("li",{key:i},n("button",{class:"icon",title:"Remove disabled host",onClick:()=>{let c=r.filter(l=>l!==i);v("settings::update",[{disabled_hosts:c}]),s(c)}},i,n(St,null)))))))}function Pt({setState:t,captcha:e}){return n(x,null,n(T,{title:e.name,leftAction:n("button",{onClick:()=>t([2,""]),class:"center"},n(B,null)),rightAction:n("a",{href:e.href,target:"_blank"},e.icon?n(e.icon,null):n("div",{class:"no-icon"}))}),n("main",null,n(W,{location:`captcha:${e.id}`}),e.disclaimer&&n("div",{class:"warning center"},e.disclaimer),n("div",{id:"captcha"},e.options.map(o=>n(Je,{captcha:e,option:o})))))}var kn={[0]:function({captcha:e,option:o}){let r=`${e.id}_${o.id}`,[s,i]=b(L()[r]);return n("button",{class:"switch",checked:s,onClick:()=>{i(!s),v("settings::update",[{[r]:!s}])}})},[2]:function({captcha:e,option:o}){let r=`${e.id}_${o.id}`;return n("button",{type:"button",class:"locate",onClick:async()=>{try{this.disabled=!0,await v("locator::inject",[]),await ct(500),await v("tab::broadcast",[{action:"start_locate",locate:r}])}finally{this.disabled=!1}}},"Locate on page")}};function Ge({captcha:t,option:e,inputType:o}){let r=`${t.id}_${e.id}`,[s,i]=b(L()[r]);return k(()=>{let c=setTimeout(()=>{v("settings::update",[{[r]:s}])},2e3);return()=>clearTimeout(c)},[s]),n("input",{type:o,autocomplete:"off",spellcheck:!1,placeholder:e.placeholder,value:s,onInput:c=>{i(c.currentTarget.value),v("settings::update",[{[r]:c.currentTarget.value}])},onKeyPress:c=>{c.key==="Enter"&&v("settings::update",[{[r]:c.currentTarget.value}])},onChange:c=>{i(c.currentTarget.value),v("settings::update",[{[r]:c.currentTarget.value}])},onBlur:c=>{v("settings::update",[{[r]:c.currentTarget.value}])}})}var An={[1]:function({captcha:e,option:o}){return n(Ge,{captcha:e,option:o,inputType:"number"})},[2]:function({captcha:e,option:o}){return n(Ge,{captcha:e,option:o,inputType:"text"})}};function Je({captcha:t,option:e}){let o=kn[e.type],r=An[e.type];return n("div",null,n("div",{class:"icon"},n("h3",null,n(e.icon,null),e.name),o&&n(o,{captcha:t,option:e})),n("div",null,e.description,r&&n(r,{captcha:t,option:e})))}var Ht=H.runtime.getManifest(),Xe=[{name:"OS",value:navigator.platform},{name:"Target",value:["chromium",3]},{name:"Machine",value:["browser"in window?"firefox":"chromium",Ht.manifest_version]},{name:"Version",value:[Ht.version,Ht.version_name]},{name:"Build flags",value:[!1,!1].filter(t=>t)},{name:"User-Agent",value:navigator.userAgent}];function Bt({setState:t}){let[e,o]=b([]);k(()=>{(async()=>{let s=L(),i=null;try{i=new URL(await gt()).href}catch{}let c=null;i&&(c=await v("tab::getDetectedCaptchas",[]));let l=new Date().getTime(),u=await v("api::fetchStatus",[]),d=new Date().getTime();o([{name:"Subscription key",value:s.key},{name:"Status",value:u},{name:"Status latency",value:`${Math.floor(d-l)}ms`},{name:"Enabled CAPTCHAs",value:q.filter(a=>s[`${a.id}_auto_solve`]).map(a=>a.name)},{name:"Global switch",value:s.enabled},{name:"Page URL",value:i},{name:"Detected CAPTCHAs",value:c}])})()},[]);let r=null;return n(x,null,n(T,{title:"Debug",leftAction:n("button",{onClick:()=>t([3,""]),class:"center"},n(B,null))}),n("div",{id:"debug"},n("button",{onClick:()=>{let s=[...Xe,...e].map(({name:i,value:c})=>`**${i}**: \`${JSON.stringify(c)}\``).join(`
`);navigator.clipboard.writeText(s),document.querySelector("#copy").classList.add("hidden"),document.querySelector("#copied").classList.remove("hidden"),document.querySelector("#debug button").classList.add("cursor-normal"),clearTimeout(r),r=setTimeout(()=>{document.querySelector("#copy").classList.remove("hidden"),document.querySelector("#copied").classList.add("hidden"),document.querySelector("#debug button").classList.remove("cursor-normal")},1e3)},class:"center"},e.length?n("div",{id:"copy-text"},n("div",{id:"copy"},"Copy to clipboard"),n("div",{id:"copied",class:"hidden"},"Copied!")):n("div",{id:"loading-text"},n("div",{class:"loading"},n("div",null),n("div",null),n("div",null),n("div",null)))),[...Xe,...e].map(({name:s,value:i})=>n("div",{key:s},n("h3",null,s),n("p",null,JSON.stringify(i))))))}function D(t,e){let o=document.createElement("canvas");return o.width=t,o.height=e,o}function R(t){return new Promise((e,o)=>{let r=new Image;r.onload=()=>e(r),r.onerror=s=>o(s),r.src=`data:image/png;base64,${t}`})}function N(t){return t.toDataURL("image/jpeg").replace(/data:image\/[a-z]+;base64,/g,"")}function Ln(t){let e={};return t.options.body&&(e.type=t.options.body.type,e.task=t.options.body.task,e.key=t.options.body.key,e.url=t.options.body.url),{...e}}async function Qe(t,e=100,o=100){let r=await R(t),s=r.width,i=r.height,c=s/i,l=s,u=i;if(l>e&&(l=e,u=Math.floor(l/c)),u>o&&(u=o,l=Math.floor(u*c)),l===s&&u===i)return{imageData:t,width:s,height:i};let d=D(s,i);d.getContext("2d").drawImage(r,0,0);let m=D(l,u);return m.getContext("2d").drawImage(d,0,0,s,i,0,0,l,u),{imageData:N(m),width:l,height:u}}async function Dt(t,e,o){let r=await R(t),i=D(r.width,r.height).getContext("2d");i.drawImage(r,0,0);let c=r.width/o,l=r.height/e,u=[];for(let d=0;d<e;d++)for(let a=0;a<o;a++){let m=D(c,l),g=m.getContext("2d"),p=i.getImageData(a*c,d*l,c,l);g.putImageData(p,0,0),u.push(N(m))}return u}async function It(t,e,o,r){let s=await Promise.all(t.map(async l=>await R(l))),i=D(100*o,100*e),c=i.getContext("2d");for(let l=0;l<s.length;l++){let u=s[l];c.drawImage(u,l%o*100,Math.floor(l/o)*100,100,100),r&&(c.beginPath(),c.strokeStyle="white",c.lineWidth=2,c.rect(l%o*100,Math.floor(l/o)*100,100,100),c.stroke())}return N(i)}async function X(t){let e=await R(t),o=D(e.width,e.height),r=o.getContext("2d");r.drawImage(e,0,0);let s=r.getImageData(0,0,e.width,e.height);for(let l=0;l<s.data.length;l+=4)s.data[l]>0&&(s.data[l+1]=Math.min(s.data[l+1]+100,255));r.putImageData(s,0,0);let i=2,c=2;return r.beginPath(),r.strokeStyle="green",r.lineWidth=i,r.rect(c,c,e.width-(i+c),e.height-(i+c)),r.stroke(),N(o)}async function Mn(t,e,o){let r=await R(t),s=D(r.width,r.height),i=s.getContext("2d");return i.drawImage(r,0,0),i.beginPath(),e=e/100*r.width,o=o/100*r.height,i.arc(e,o,10,0,2*Math.PI),i.fillStyle="red",i.fill(),N(s)}async function Tn(t,e,o,r,s){let i=await R(t),c=D(i.width,i.height),l=c.getContext("2d");return l.drawImage(i,0,0),l.beginPath(),e=e/100*i.width,o=o/100*i.height,r=r/100*i.width,s=s/100*i.height,l.rect(e,o,r,s),l.strokeStyle="red",l.lineWidth=5,l.stroke(),N(c)}function $t({setState:t}){let[e,o]=b([]);async function r(){let i=await v("log::getLogs",[]),c=JSON.parse(i);c.sort((l,u)=>u.postreq.time-l.postreq.time);for(let l of c)if(l.postreq.options.method==="POST"){l.audio=null,l.images=[];let u=l.postreq.options.body.audio_data;u&&(l.audio=`data:audio/wav;base64,${u}`);let d=l.postreq.options.body.image_data;if(d){if(d.length===1){let a=await Qe(d[0],300,300),{imageData:m,width:g,height:p}=a;if(l.answer){if(typeof l.answer=="object"&&"x"in l.answer&&"y"in l.answer&&"w"in l.answer&&"h"in l.answer&&(l.answer.w>0||l.answer.h>0))m=await Tn(m,l.answer.x,l.answer.y,l.answer.w,l.answer.h);else if(typeof l.answer=="object"&&"x"in l.answer&&"y"in l.answer)m=await Mn(m,l.answer.x,l.answer.y);else if(Array.isArray(l.answer)&&"grid"in l.postreq.options.body)if(l.postreq.options.body.grid==="4x4"){let f=await Dt(m,4,4);for(let w=0;w<f.length;w++)l.answer[w]===!0&&(f[w]=await X(f[w]));m=await It(f,4,4,!0)}else if(l.postreq.options.body.grid==="3x3"){let f=await Dt(m,3,3);for(let w=0;w<f.length;w++)l.answer[w]===!0&&(f[w]=await X(f[w]));m=await It(f,3,3,!1)}else l.postreq.options.body.grid==="1x1"&&(m=await X(m));else if(Array.isArray(l.answer)){let f=Math.ceil(g/100),w=Math.ceil(p/100),h=await Dt(m,w,f);for(let C=0;C<h.length;C++)l.answer[C]===!0&&(h[C]=await X(h[C]));m=await It(h,w,f,!1)}}l.images=[[`data:image/png;base64,${m}`,g,p]]}else if(d.length&&d.length<=9){let a=D(300,100*Math.ceil(d.length/3)),m=a.getContext("2d");for(let p=0;p<d.length;p++){let f=await Qe(d[p],100,100),{imageData:w,width:h,height:C}=f;l.answer&&Array.isArray(l.answer)&&l.answer.length===d.length&&l.answer[p]===!0&&(w=await X(w));let M=await R(w);m.drawImage(M,p%3*100,Math.floor(p/3)*100,100,100)}let g=N(a);l.images=[[`data:image/png;base64,${g}`,a.width,a.height]]}}}o(c)}k(()=>{r()},[]);let s=null;return n(x,null,n(T,{title:"Logs",leftAction:n("button",{onClick:()=>t([3,""]),class:"center"},n(B,null))}),n("div",{id:"logs"},n("button",{class:"center",onClick:async()=>{document.querySelector("#loading-text").classList.remove("hidden"),document.querySelector("#refresh-text").classList.add("hidden"),document.querySelector("#logs button").classList.add("cursor-normal"),document.querySelector("#no-logs")?.classList?.add("hidden"),await r(),clearTimeout(s),s=setTimeout(()=>{document.querySelector("#loading-text").classList.add("hidden"),document.querySelector("#refresh-text").classList.remove("hidden"),document.querySelector("#logs button").classList.remove("cursor-normal"),document.querySelector("#no-logs")?.classList?.remove("hidden")},1e3)}},n("div",{id:"refresh-text"},"Refresh"),n("div",{id:"loading-text",class:"hidden"},n("div",{class:"loading"},n("div",null),n("div",null),n("div",null),n("div",null)))),!e.length&&n("h3",{id:"no-logs"},"No logs"),e.map(({id:i,postreq:c,audio:l,images:u,answer:d})=>n("div",{"data-id":i,key:i,class:"log"},n("h3",null,"Submitted ",((Date.now()-c.time)/1e3).toFixed(1),"s ago"),n("p",{class:"mono color-muted"},JSON.stringify(Ln(c),null,2)),l?n("div",{style:"width: 280px; overflow: auto;"},n("audio",{controls:!0,src:l})):"",u?n(x,null,u.map(([a,m,g])=>n("img",{src:a,width:m-20,height:g}))):"",d?n("p",{class:"mono"},JSON.stringify(d)):n("p",{class:"mono"},"solution not yet available")))))}function zt(){let[t,e]=b([0,""]),[o,r]=b(null),[s,i]=b(!0),[c,l]=b(!1),u=async()=>{i(!0);let d=await v("api::fetchStatus",[]);r(d),i(!1)};return k(()=>{if(t[0]===0){Ce();let d=setTimeout(()=>{l(!0)},2e3);return we().then(a=>{a?e([1,""]):l(!0)}),()=>{clearTimeout(d)}}t[0]===1&&v("api::getCachedStatus",[]).then(d=>{d&&(r(d),e([2,""])),u().then(()=>{d||e([2,""])})})},[t]),n("div",{id:"app"},t[0]===0?n(ut,null,c?n("div",{class:"warning center"},n("p",null,"It appears the background service has crashed."),n("a",{href:"chrome://extensions/",target:"_blank"},"Please reload the extension.")):n("span",null,"Loading your settings...")):t[0]===1?n(ut,null,"Connecting to servers..."):t[0]===2?n(Lt,{status:o,setState:e,refreshing:s,refresh:u}):t[0]===3?n(Mt,{setState:e}):t[0]===4?n(Tt,{setState:e}):t[0]===5?n(Pt,{setState:e,captcha:q.find(d=>d.id===t[1])}):t[0]===6?n(Bt,{setState:e}):t[0]===7?n($t,{setState:e}):n("p",null,"How did you get here"))}[...document.body.children].forEach(t=>t.remove());ee(n(zt,{}),document.body);})();
