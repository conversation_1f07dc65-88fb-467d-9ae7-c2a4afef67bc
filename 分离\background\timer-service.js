// 计时数据存储（内存中）
let timers = {};
let activeSurveyId = null;

// 获取计时器数据
function getTimers() {
  return timers;
}

// 获取活跃的调查ID
function getActiveSurveyId() {
  return activeSurveyId;
}

// 设置活跃的调查ID
function setActiveSurveyId(id) {
  activeSurveyId = id;
}

// 设置计时器
function setTimers(newTimers) {
  timers = newTimers;
}

// 获取特定调查的计时器
function getTimer(surveyId) {
  if (!timers[surveyId]) {
    timers[surveyId] = { startTime: Date.now(), pausedTime: 0 };
  }
  return timers[surveyId];
}

// 重置计时器
function resetTimer(surveyId) {
  timers[surveyId] = { startTime: Date.now(), pausedTime: 0 };
  return timers[surveyId];
}

// 计算已过时间
function calculateElapsedTime(surveyId) {
  if (!timers[surveyId]) return 0;
  return Date.now() - timers[surveyId].startTime + timers[surveyId].pausedTime;
}

// 导出函数
export {
  getTimers,
  setTimers,
  getActiveSurveyId,
  setActiveSurveyId,
  getTimer,
  resetTimer,
  calculateElapsedTime
};