{"extensionName": {"message": "Buster: <PERSON><PERSON> for Humans"}, "extensionDescription": {"message": "Save time by asking <PERSON> to solve CAPTCHAs for you."}, "optionSectionTitle_services": {"message": "Services"}, "optionTitle_speechService": {"message": "Speech recognition"}, "optionValue_speechService_googleSpeechApi": {"message": "Google Cloud Speech-to-Text"}, "optionValue_speechService_ibmSpeechApi": {"message": "IBM Watson Speech to Text"}, "optionValue_speechService_microsoftSpeechApi": {"message": "Microsoft Azure Speech to Text"}, "optionValue_speechService_witSpeechApiDemo": {"message": "Wit.ai (managed)"}, "optionValue_speechService_witSpeechApi": {"message": "Wit.ai"}, "optionTitle_microsoftSpeechApiLoc": {"message": "API location"}, "optionValue_microsoftSpeechApiLoc_southafricanorth": {"message": "South Africa North"}, "optionValue_microsoftSpeechApiLoc_eastasia": {"message": "East Asia"}, "optionValue_microsoftSpeechApiLoc_southeastasia": {"message": "Southeast Asia"}, "optionValue_microsoftSpeechApiLoc_australiaeast": {"message": "Australia East"}, "optionValue_microsoftSpeechApiLoc_centralindia": {"message": "Central India"}, "optionValue_microsoftSpeechApiLoc_japaneast": {"message": "Japan East"}, "optionValue_microsoftSpeechApiLoc_japanwest": {"message": "Japan West"}, "optionValue_microsoftSpeechApiLoc_koreacentral": {"message": "Korea Central"}, "optionValue_microsoftSpeechApiLoc_canadacentral": {"message": "Canada Central"}, "optionValue_microsoftSpeechApiLoc_northeurope": {"message": "North Europe"}, "optionValue_microsoftSpeechApiLoc_westeurope": {"message": "West Europe"}, "optionValue_microsoftSpeechApiLoc_francecentral": {"message": "France Central"}, "optionValue_microsoftSpeechApiLoc_germanywestcentral": {"message": "Germany West Central"}, "optionValue_microsoftSpeechApiLoc_norwayeast": {"message": "Norway East"}, "optionValue_microsoftSpeechApiLoc_swedencentral": {"message": "Sweden Central"}, "optionValue_microsoftSpeechApiLoc_switzerlandnorth": {"message": "Switzerland North"}, "optionValue_microsoftSpeechApiLoc_switzerlandwest": {"message": "Switzerland West"}, "optionValue_microsoftSpeechApiLoc_uksouth": {"message": "UK South"}, "optionValue_microsoftSpeechApiLoc_uaenorth": {"message": "UAE North"}, "optionValue_microsoftSpeechApiLoc_brazilsouth": {"message": "Brazil South"}, "optionValue_microsoftSpeechApiLoc_qatarcentral": {"message": "Qatar Central"}, "optionValue_microsoftSpeechApiLoc_centralus": {"message": "Central US"}, "optionValue_microsoftSpeechApiLoc_eastus": {"message": "East US"}, "optionValue_microsoftSpeechApiLoc_eastus2": {"message": "East US 2"}, "optionValue_microsoftSpeechApiLoc_northcentralus": {"message": "North Central US"}, "optionValue_microsoftSpeechApiLoc_southcentralus": {"message": "South Central US"}, "optionValue_microsoftSpeechApiLoc_westcentralus": {"message": "West Central US"}, "optionValue_microsoftSpeechApiLoc_westus": {"message": "West US"}, "optionValue_microsoftSpeechApiLoc_westus2": {"message": "West US 2"}, "optionValue_microsoftSpeechApiLoc_westus3": {"message": "West US 3"}, "optionSectionTitle_client": {"message": "Client app"}, "optionSectionDescription_client": {"message": "The client app enables you to simulate user interactions and improves the success rate of the extension."}, "optionTitle_simulateUserInput": {"message": "Simulate user interactions"}, "optionTitle_navigateWithKeyboard": {"message": "Navigate with keyboard"}, "optionTitle_autoUpdateClientApp": {"message": "Auto-update client app"}, "optionSectionTitle_misc": {"message": "Miscellaneous"}, "optionTitle_loadEnglishChallenge": {"message": "Load challenge in English"}, "optionTitle_tryEnglishSpeechModel": {"message": "Retry speech recognition with English model"}, "optionTitle_appTheme": {"message": "Theme"}, "optionValue_appTheme_auto": {"message": "System default"}, "optionValue_appTheme_light": {"message": "Light"}, "optionValue_appTheme_dark": {"message": "Dark"}, "optionTitle_showContribPage": {"message": "Show contribution page"}, "optionTitle_witSpeechApiLang": {"message": "API language"}, "optionValue_witSpeechApiLang_arabic": {"message": "Arabic"}, "optionValue_witSpeechApiLang_bengali": {"message": "Bengali"}, "optionValue_witSpeechApiLang_chinese": {"message": "Chinese"}, "optionValue_witSpeechApiLang_dutch": {"message": "Dutch"}, "optionValue_witSpeechApiLang_english": {"message": "English"}, "optionValue_witSpeechApiLang_finnish": {"message": "Finnish"}, "optionValue_witSpeechApiLang_french": {"message": "French"}, "optionValue_witSpeechApiLang_german": {"message": "German"}, "optionValue_witSpeechApiLang_hindi": {"message": "Hindi"}, "optionValue_witSpeechApiLang_indonesian": {"message": "Indonesian"}, "optionValue_witSpeechApiLang_italian": {"message": "Italian"}, "optionValue_witSpeechApiLang_japanese": {"message": "Japanese"}, "optionValue_witSpeechApiLang_kannada": {"message": "Kannada"}, "optionValue_witSpeechApiLang_korean": {"message": "Korean"}, "optionValue_witSpeechApiLang_malay": {"message": "Malay"}, "optionValue_witSpeechApiLang_malayalam": {"message": "Malayalam"}, "optionValue_witSpeechApiLang_marathi": {"message": "Marathi"}, "optionValue_witSpeechApiLang_polish": {"message": "Polish"}, "optionValue_witSpeechApiLang_portuguese": {"message": "Portuguese"}, "optionValue_witSpeechApiLang_russian": {"message": "Russian"}, "optionValue_witSpeechApiLang_sinhala": {"message": "Sinhala"}, "optionValue_witSpeechApiLang_spanish": {"message": "Spanish"}, "optionValue_witSpeechApiLang_swedish": {"message": "Swedish"}, "optionValue_witSpeechApiLang_tamil": {"message": "Tamil"}, "optionValue_witSpeechApiLang_thai": {"message": "Thai"}, "optionValue_witSpeechApiLang_turkish": {"message": "Turkish"}, "optionValue_witSpeechApiLang_urdu": {"message": "Urdu"}, "optionValue_witSpeechApiLang_vietnamese": {"message": "Vietnamese"}, "inputLabel_apiUrl": {"message": "API endpoint"}, "inputLabel_apiKey": {"message": "API key"}, "inputLabel_apiKeyType": {"message": "API key: $TYPE$", "placeholders": {"type": {"content": "$1", "example": "English"}}}, "inputLabel_appLocation": {"message": "App location"}, "inputLabel_manifestLocation": {"message": "Manifest location"}, "buttonLabel_addApi": {"message": "Add API"}, "buttonLabel_solve": {"message": "Solve the challenge"}, "buttonLabel_reset": {"message": "Reset the challenge"}, "buttonLabel_downloadApp": {"message": "Download app"}, "buttonLabel_installApp": {"message": "Install app"}, "buttonLabel_goBack": {"message": "Go back"}, "buttonLabel_contribute": {"message": "Contribute"}, "linkText_installGuide": {"message": "Installation guide"}, "linkText_apiGuide": {"message": "How to get an API key?"}, "pageContent_optionClientAppDownloadDesc": {"message": "Download and install the client app to get started. $INSTALLGUIDE$", "placeholders": {"installGuide": {"content": "$1", "example": "Installation guide"}}}, "pageContent_optionClientAppOSError": {"message": "Your operating system is not supported."}, "pageContent_installTitle": {"message": "Install Buster Client"}, "pageContent_installDesc": {"message": "The client app enables you to simulate user interactions and improves the success rate of the extension."}, "pageContent_installSuccessTitle": {"message": "Installation finished"}, "pageContent_installSuccessDesc": {"message": "The client app has been installed for the current browser."}, "pageContent_installErrorTitle": {"message": "Something went wrong"}, "pageContent_installErrorDesc": {"message": "The installation has failed. Check the browser console for more details, and open an issue on GitHub if this error persists."}, "pageContent_manifestLocationDesc": {"message": "The manifest location is browser-dependent, edit the path only if the installation does not succeed."}, "pageTitle": {"message": "$PAGETITLE$ - $EXTENSIONNAME$", "placeholders": {"pageTitle": {"content": "$1", "example": "Options"}, "extensionName": {"content": "$2", "example": "Extension Name"}}}, "pageTitle_options": {"message": "Options"}, "pageTitle_contribute": {"message": "Contribute"}, "info_updatingClientApp": {"message": "Updating client app. Solving will continue in a moment, do not switch away from the current tab."}, "error_captchaNotSolved": {"message": "CAPTCHA could not be solved. Try again after requesting a new challenge."}, "error_captchaNotSolvedWitai": {"message": "Wit.ai could not detect any speech. Try a new challenge, or switch to a more reliable service from the extension's options, such as IBM Watson."}, "error_missingApiUrl": {"message": "API endpoint missing. Visit the extension's options to configure the service."}, "error_missingApiKey": {"message": "API key missing. Visit the extension's options to configure the service."}, "error_apiQuotaExceeded": {"message": "API quota exceeded. Try again later, or visit the extension's options and switch to a different service."}, "error_scriptsNotAllowed": {"message": "Content scripts are not allowed on this page."}, "error_missingClientApp": {"message": "Cannot connect to client app. Finish setting up the app or turn off the simulation of user interactions from the extension's options."}, "error_outdatedClientApp": {"message": "The client app is outdated. Download and install the latest version from the extension's options."}, "error_clientAppUpdateFailed": {"message": "The client app cannot be updated. Download and install the latest version from the extension's options."}, "error_internalError": {"message": "Something went wrong. Open the browser console for more details."}}