(()=>{function A(){if("ancestorOrigins"in location){let t=location.ancestorOrigins,n=t[1]??t[0];if(n)return n.split("/")[2]}let e=document.referrer;return e?e.split("/")[2]:location.origin}var h=chrome;var x="https://api.nopecha.com",i="https://www.nopecha.com",L="https://developers.nopecha.com",_e={doc:{url:L,automation:{url:`${L}/guides/extension_advanced/#automation-build`}},api:{url:x,recognition:{url:`${x}/recognition`},status:{url:`${x}/status`}},www:{url:i,annoucement:{url:`${i}/json/announcement.json`},demo:{url:`${i}/captcha`,recaptcha:{url:`${i}/captcha/recaptcha`},funcaptcha:{url:`${i}/captcha/funcaptcha`},awscaptcha:{url:`${i}/captcha/awscaptcha`},textcaptcha:{url:`${i}/captcha/textcaptcha`},turnstile:{url:`${i}/captcha/turnstile`},perimeterx:{url:`${i}/captcha/perimeterx`},geetest:{url:`${i}/captcha/geetest`},lemincaptcha:{url:`${i}/captcha/lemincaptcha`}},manage:{url:`${i}/manage`},pricing:{url:`${i}/pricing`},setup:{url:`${i}/setup`}},discord:{url:`${i}/discord`},github:{url:`${i}/github`,release:{url:`${i}/github/release`}}};function P(e){let t=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(n=>n.charCodeAt(0));return q(t)}var E=new Uint32Array(256);for(let e=256;e--;){let t=e;for(let n=8;n--;)t=t&1?3988292384^t>>>1:t>>>1;E[e]=t}function q(e){let t=-1;for(let n of e)t=t>>>8^E[t&255^n];return(t^-1)>>>0}async function u(e,t){let n=""+[+new Date,performance.now(),Math.random()],[a,o]=await new Promise(r=>{h.runtime.sendMessage([n,e,...t],r)});if(a===P(n))return o}function b(){let e;return t=>e||(e=t().finally(()=>e=void 0),e)}var oe=b(),g;function O(){return oe(async()=>(g||(g=await u("settings::get",[])),g))}function F(e){g&&(g={...g,...e},U(g))}function _(){return g}function l(e){return new Promise(t=>setTimeout(t,e))}var N=[];function T(e,t){e.timedout=!1,N.push(e);let n,a=setInterval(async()=>{await W(e,_())||(clearTimeout(n),clearInterval(a))},400);t&&(n=setTimeout(()=>clearInterval(a),t),e.timedout=!0)}async function W(e,t){if(e.timedout)return!1;let n=e.condition(t);if(n===e.running())return!1;if(!n&&e.running())return e.quit(),!1;if(n&&!e.running()){for(;!e.ready();)await l(200);return e.start(),!1}}function U(e){N.forEach(t=>W(t,e))}function z(){h.runtime.connect({name:"stream"}).onMessage.addListener(t=>{t.event==="settingsUpdate"&&F(t.settings)})}function k(e){if(document.readyState!=="loading")setTimeout(e,0);else{let t;t=()=>{removeEventListener("DOMContentLoaded",t),e()},addEventListener("DOMContentLoaded",t)}}var Oe=b();var re=["#home_children_button","#wrong_children_button","#wrongTimeout_children_button","button[data-theme*=verifyButton]","[class*=game-fail] .button",".error .button"],G=["#root","#app","#home","#wrong","#wrongTimeout",".container[dir]"].join(", "),C,S=!1;function V(){return!!document.querySelector(G)}function j(){S=!0;let e=()=>{ie(),document.querySelectorAll(G).forEach(t=>{C.observe(t,{childList:!0})})};C=new MutationObserver(e),e()}function Q(){C.disconnect(),S=!1}function K(){return S}async function ie(){await l(400),re.map(e=>document.querySelector(e)).filter(e=>e).map(e=>e.click())}function ce(e,t){let n=document.createElement("canvas");return n.width=e,n.height=t,n}function I(e){return e.toDataURL("image/jpeg").replace(/data:image\/[a-z]+;base64,/g,"")}function se(e){try{e.getContext("2d").getImageData(0,0,1,1)}catch{return!0}return!1}async function B(e,t,n=1e4){if(!t&&!e.complete&&!await new Promise(c=>{let d=setTimeout(()=>{c(!1)},n);e.addEventListener("load",()=>{clearTimeout(d),c(!0)})}))return;let a=ce(e.naturalWidth||t?.clientWidth,e.naturalHeight||t?.clientHeight);return a.getContext("2d").drawImage(e,0,0),!se(a)&&a}async function M(e){let n=getComputedStyle(e).backgroundImage;if(!n||n==="none")if("src"in e&&e.src)n=`url("${e.src}")`;else return;if("computedStyleMap"in e&&!/url\(["']https?:\/\//.test(n)){let s=e.computedStyleMap().get("background-image");if(s instanceof CSSImageValue){let f=await B(s,e);if(f)return f}}let a=/"(.+)"/.exec(n);if(!a)return;n=a[1];let o=document.createElement("a");if(o.href=n,new URL(o.href).origin===document.location.origin){let s=new Image;s.crossOrigin="anonymous",s.src=n;let f=await B(s);if(f)return f}let r=await u("fetch::asData",[n,{}]),c=new Image;c.crossOrigin="anonymous",c.src=r.data;let d=await B(c);if(d)return d}function ue(e,t,n,a){let o=(a*t+n)*4;return[e[o],e[o+1],e[o+2]]}function le(e,t){return e.every(n=>n<=t)}function de(e,t){return e.every(n=>n>=t)}function $(e,t=0,n=230,a=.99){let o=e.getContext("2d"),r=o.canvas.width,c=o.canvas.height;if(r===0||c===0)return!0;let d=o.getImageData(0,0,r,c).data,s=0;for(let v=0;v<c;v++)for(let w=0;w<r;w++){let D=ue(d,r,w,v);(le(D,t)||de(D,n))&&s++}return s/(r*c)>a}function J(){return[]}function X(e){return new Promise(t=>{e.push(t)})}function m(e){e.forEach(t=>t()),e.splice(0)}async function Y(e,t){let n={v:h.runtime.getManifest().version,key:ge(e)};return n.url=await u("tab::getURL",[]),n}function ge(e){return!e.keys||!e.keys.length?e.key:e.keys[Math.floor(Math.random()*e.keys.length)]}var p=J(),R,y=!1;function ee(){return H()!==void 0}function te(){y=!0,m(p),R=new MutationObserver(e=>{let t=H();for(let n of e)if(n.type==="childList"&&n.removedNodes.length&&["app","game"].includes(n.target.id)){setTimeout(()=>m(p),200);return}t===1&&e.length===24&&!document.querySelector(".loading-spinner")&&setTimeout(()=>m(p),200),t===2&&[8,13].includes(e.length)&&!document.querySelector(".loading-spinner")&&setTimeout(()=>m(p),200)}),R.observe(document,{childList:!0,subtree:!0,attributes:!0}),fe()}function ne(){R.disconnect(),y=!1,m(p)}function ae(){return y}var me={[0]:{async getTask(){let e=document.querySelector("#game_children_text h2"),t=document.querySelector("#game_challengeItem_image"),n=[...document.querySelectorAll("#game_children_challenge a")];if(!(!e||!t||n.length!==6))return{payload:{type:"funcaptcha",task:e.textContent,image_data:[t.src.replace(/data:image\/[a-z]+;base64,/g,"")]},cells:n}},async solution(e,t){e.cells.forEach((n,a)=>{t.data[a]&&n.click()})}},[1]:{async getTask(){let e=document.querySelector(".tile-game h2"),t=[...document.querySelectorAll(".challenge-container button")];if(!e||t.length!==6)return;let n=await M(t[0]);if(n&&!$(n))return{payload:{type:"funcaptcha",task:e.textContent,image_data:[I(n)]},cells:t}},async solution(e,t){e.cells.forEach((n,a)=>{t.data[a]&&n.click()})}},[2]:{async getTask(){let e=document.querySelector(".match-game h2"),t=document.querySelector(".key-frame-image");if(!e||!t)return;let n=await M(t);if(n&&!$(n))return{payload:{type:"funcaptcha_match",task:e.textContent,image_data:[I(n)]}}},async solution(e,t){let n=document.querySelector(".right-arrow"),a=t.data.indexOf(!0);for(let r=0;r<a;r++)n.click(),await l(100);await l(500),document.querySelector(".button").click()}}},pe=[["script[src*=tile-game-ui]",0],[".tile-game",1],[".match-game",2]];function H(){return pe.filter(([e])=>document.querySelector(e)).map(([e,t])=>t)[0]}var Z=!1;async function fe(){if(!Z)for(Z=!0;y;){let e=H();if(e===void 0){await l(500);continue}let t=me[e],n=await t.getTask();if(!n){await l(500);continue}let a=_(),o=new Date().valueOf(),r=await u("api::recognition",[{...n.payload,...await Y(a,!0)}]);if(!r||"error"in r){await l(2e3);continue}let c=new Date().valueOf();if(a.funcaptcha_solve_delay){let s=a.funcaptcha_solve_delay_time-c+o;s>0&&await l(s)}await t.solution(n,r);let d=setTimeout(()=>{m(p)},1e3*5);await X(p),clearTimeout(d)}}async function he(){z(),await O(),await u("tab::registerDetectedCaptcha",["funcaptcha"]);let e=A();T({name:"funcaptcha/auto-open",condition:t=>t.enabled&&t.funcaptcha_auto_open&&!t.disabled_hosts.includes(e),ready:V,start:j,quit:Q,running:K}),T({name:"funcaptcha/auto-solve",condition:t=>t.enabled&&t.funcaptcha_auto_solve&&!t.disabled_hosts.includes(e),ready:ee,start:te,quit:ne,running:ae})}k(he);})();
