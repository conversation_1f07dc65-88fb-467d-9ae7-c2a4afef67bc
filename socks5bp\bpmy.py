import re

# 定义输入和输出文件名
input_file = 'http_responses.txt'  # 输入文件名，可根据需要修改
output_file = 'offer_details.txt'  # 输出文件名，可根据需要修改

# 读取文件内容
try:
    with open(input_file, 'r', encoding='utf-8') as file:
        content = file.read()
except FileNotFoundError:
    print(f"文件 {input_file} 未找到，请确保文件存在。")
    exit(1)
except Exception as e:
    print(f"读取文件时出错: {e}")
    exit(1)

# 定义正则表达式模式
# 匹配 &offer_id=xxx 和 &offer_name=xxx 的组合
pattern = r'&offer_id=([^&]+)&offer_name=([^&]+)&'
matches = re.findall(pattern, content)

# 去重并整理结果
unique_matches = sorted(set(matches))  # 使用 set 去重，并排序

# 将结果保存到新文件，每行包含 offer_id 和 offer_name
try:
    with open(output_file, 'w', encoding='utf-8') as file:
        if unique_matches:
            file.write("offer_id\toffer_name\n")  # 写入表头
            for offer_id, offer_name in unique_matches:
                file.write(f"{offer_id}\t{offer_name}\n")
            print(f"已提取 {len(unique_matches)} 条唯一匹配项，保存到 {output_file}")
        else:
            file.write("文件中未找到任何 &offer_id=xxx&offer_name=xxx& 模式。\n")
            print(f"文件中未找到任何 &offer_id=xxx&offer_name=xxx& 模式，结果已保存到 {output_file}")
except Exception as e:
    print(f"写入文件时出错: {e}")
    exit(1)