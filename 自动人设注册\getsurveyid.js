// JWT 解码函数
function decodeJWT(token) {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
  
        return JSON.parse(jsonPayload);
    } catch (e) {
        console.error('JWT解码错误:', e);
        return null;
    }
  }
  
  // 定义一个函数来查找和修改元素
  function updateSurveyCards() {
    // 获取所有SurveyCard_link元素
    const surveyLinks = document.querySelectorAll('[class*="SurveyCard_link"]');
  
    // 遍历每个SurveyCard_link元素
    surveyLinks.forEach(link => {
        // 获取href中的token
        const url = new URL(link.href);
        const token = url.searchParams.get('token');
  
  
        if (token) {
            // 使用自定义的decodeJWT函数解码JWT
            const decoded = decodeJWT(token);
            if (decoded) {
  
                // 获取survey_id
                const surveyId = decoded.survey_id;
  
                // 找到SurveyCard_code元素并替换内容
                const surveyCodeElement = link.querySelector('[class*="SurveyCard_code"]');
                if (surveyCodeElement && surveyId) {
                    surveyCodeElement.textContent = surveyId;
                }
                
            }
        }
    });
  }
  
// ... existing code ...

// 恢复域名检测
setInterval(() => {
    const currentUrl = window.location.href;
    const targetPattern = /^https:\/\/app\.lifepointspanel\.com(\/[^\/]+)*\/dashboard/;

    if (targetPattern.test(currentUrl)) {
        updateSurveyCards();
    }
}, 5000);