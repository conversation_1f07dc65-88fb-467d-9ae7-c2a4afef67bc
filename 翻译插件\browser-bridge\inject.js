// 移除引导流程相关的代码
(()=>{var g={BUILD_TIME:"2025-04-29T07:31:40.000Z",VERSION:"1.16.8",PROD:"1",REDIRECT_URL:"https://dash.  /auth-done/",PROD_API:"1",BETA:"0",userscript_domains:'["google.com","translate.googleapis.com","api-edge.cognitive.microsofttranslator.com","edge.microsoft.com","transmart.qq.com","translate.yandex.net","tmt.tencentcloudapi.com","www2.deepl.com","w.deepl.com","immersive-translate.owenyoung.com","generativelanguage.googleapis.com","chat.openai.com","bing.com","www.bing.com","open.volcengineapi.com","fanyi.baidu.com","api.fanyi.baidu.com","api.interpreter.caiyunai.com","api-free.deepl.com","api.deepl.com","api.openl.club","openapi.youdao.com","translate.volcengine.com","api.niutrans.com","  ","test-api2.  ","api2.  ","config.  ","app.  ","dash.  ","api.  ","immersive-translate.deno.dev","www.googleapis.com","www.google-analytics.com","translate-pa.googleapis.com","api.cognitive.microsofttranslator.com","api.groq.com","api.x.ai","api.papago-chrome.com","api.openai.com","api.interpreter.caiyunai.com","api.cognitive.microsofttranslator.com","aidemo.youdao.com","dict.youdao.com","openai.azure.com","mt.aliyuncs.com","subhub.weixin.so","api.anthropic.com","localhost","127.0.0.1","ai.  ","test-ai.  ","openrouter.ai","dashscope.aliyuncs.com","api.deepseek.com","aip.baidubce.com","ark.cn-beijing.volces.com","hunyuan.tencentcloudapi.com","public-beta-api.siliconflow.cn","api.siliconflow.cn","open.bigmodel.cn","store.  "]',MOCK:"0",DEBUG:"0",INSTALL_FROM:"chrome_zip"};function h(){return typeof process>"u"&&typeof Deno<"u"?Deno.env.toObject():g}var E=h();function B(t){return t&&globalThis?.document?.querySelector("meta[name=immersive-translate-options]")?!!globalThis.document?.getElementById("immersive-translate-manifest")?.value?.includes("_isUserscript"):E.IMMERSIVE_TRANSLATE_USERSCRIPT==="1"}function x(){return E.PROD==="1"}function I(){return E.PROD_API==="1"}function N(){if(E.IMMERSIVE_TRANSLATE_SAFARI==="1")return!0;if(typeof globalThis.immersiveTranslateBrowserAPI<"u"&&globalThis.immersiveTranslateBrowserAPI.runtime&&globalThis.immersiveTranslateBrowserAPI.runtime.getManifest){let e=globalThis.immersiveTranslateBrowserAPI.runtime.getManifest();return!!(e&&e._isSafari)}else return!1}var be=h().PROD==="1",Ae=h().PROD!=="1";var le="";function D(){return le||globalThis.navigator.userAgent}function F(){return D().includes("ImtFxiOS")}function k(){let t=D();return t.includes("ImtFxAndroid")||t.includes("ImtFxAOS")}var M=class{bridge;waitForBridge(e=1e4){return!k()&&!F()?Promise.resolve(!1):globalThis.WebViewJavascriptBridge?(this.bridge=globalThis.WebViewJavascriptBridge,Promise.resolve(!0)):new Promise(r=>{let n=Date.now(),s=()=>{if(globalThis.WebViewJavascriptBridge)return this.bridge=globalThis.WebViewJavascriptBridge,r(!0);if(Date.now()-n>e)return r(!1);requestAnimationFrame(s)};s()})}registerHandler(e,r){if(!this.bridge){console.error("Bridge not initialized");return}this.bridge.registerHandler(e,r)}callHandler(e,r,n){if(!this.bridge){console.error("Bridge not initialized");return}this.bridge.doSend({type:e,...r},n)}},l=new M;var o="immersiveTranslate";var i="immersive-translate",G="imt",ce="immersivetranslate";var u="  ",ue=`https://config.${u}/`,Be=`https://app.${u}/`,c=x()||I()?`https://${u}/`:`https://test.${u}/`,b=`https://dash.${u}/`,Ne=x()||I()?`https://api2.${u}/`:`https://test-api2.${u}/`,De=x()||I()?`https://ai.${u}/`:`https://test-ai.${u}/`,Fe=`https://assets.${ce}.cn/`,$=c+"accounts/login?from=plugin",H=c+"profile/",d=c+"auth/pricing/",f=c+"pricing/";N()&&(d=c+"accounts/safari-iap/",f=c+"accounts/safari-iap/");var W=`https://github.com/${i}/${i}/`,ke=`https://s.${u}/`,Ge=`https://onboarding.${u}/`;var $e=o+"DeeplGlobalState",He=o+"BingGlobalState",We=o+"YandexGlobalState",Ve=o+"BaiduQianfanGlobalConfigStorageKey",Je=o+"SiliconCloudGlobalConfigStorageKey",Ke=o+"ZhipuGlobalConfigStorageKey";var qe=o+"GoogleAccessToken",je=o+"AuthFlow",Ye=i+"-config-latest.json",Qe=o+"AuthState",ze=o+"IframeMessage",Xe=o+"WaitForRateLimit",Ze=o+"DocumentMessageAsk",P=o+"DocumentMessageTellThirdParty",et=o+"showError",tt=o+"showModal",rt=o+"showToast",V=o+"DocumentMessageThirdPartyTell",ot=o+"DocumentMessageEventUpload",nt=o+"DocumentMessageTypeStopJsSDK",st=o+"DocumentMessageHandler",at=o+"DocumentSetFloatBallActive",it=`${o}Share`,lt=`${o}ShowFloatBallGuide`,ct=o+"DocumentMessageTempEnableSubtitleChanged",ut=o+"DocumentMessageUpdateQuickButtonAiSubtitle",mt=`${o}ToggleMouseHoverTranslateDirectly`,gt=`${o}ReqDraft`,pt=`${o}ResDraft`,dt=`${o}Container`,ft=`${o}SpecifiedContainer`;var _t=`${o}PageTranslatedStatus`,ht=`${o}MangaTranslatedStatus`,xt=`${o}PageUrlChanged`,Tt=`${o}ReceiveCommand`,yt=o+"LastUseMouseHoverTime",St=o+"LastUseInputTime",Et=o+"LastUseManualTranslatePageTime",It=`${o}PopupReceiveMessage`,bt=o+"DocumentMessageEventTogglePopup",At=`${ue}default_config.json`,Rt=`${o}Mark`,Mt=`${o}Root`,Pt=`${o}Walked`,vt=`data-${i}-walked`,Ot=`${o}Paragraph`,Ct=`data-${i}-paragraph`,Lt=`data-${i}-translation-element-mark`,Ut=`${o}TranslationElementMark`,wt=`${o}TranslatedMark`,Bt=`${i}-input-injected-css`,Nt=`${o}LoadingId`,Dt=`data-${i}-loading-id`,Ft=`${o}ErrorId`,kt=`data-${i}-error-id`,Gt=`${o}AtomicBlockMark`,$t=`${o}ExcludeMark`,Ht=`data-${i}-exclude-mark`,Wt=`${o}StayOriginalMark`,Vt=`${o}PreWhitespaceMark`,Jt=`${o}InlineMark`,Kt=`${o}BlockMark`,qt=`${o}Left`,jt=`${o}Right`,Yt=`${o}Width`,Qt=`${o}Height`,zt=`${o}Top`,Xt=`${o}FontSize`;var Zt=`${o}GlobalStyleMark`;var er=`${i}-target-wrapper`,tr=`${i}-pdf-target-container`,rr=`${i}-target-inner`,or=`${i}-source-wrapper`,nr=`${i}-target-translation-block-wrapper`,sr=`${i}-root-translation-theme`,ar=`${o}RootTranslationTheme`,ir=`${i}-target-translation-vertical-block-wrapper`,lr=`${i}-target-translation-pdf-block-wrapper`,cr=`${i}-target-translation-pre-whitespace`,ur=`${i}-target-translation-inline-wrapper`;var mr=["https://immersive-translate.owenyoung.com/options/","https://immersive-translate.owenyoung.com/auth-done/",b,b+"auth-done/","http://localhost:8000/dist/userscript/options/","http://localhost:8000/auth-done/","http://************:8000/dist/userscript/options/","http://*************:8000/dist/userscript/options/","http://************:8000/dist/userscript/options/","https://www.deepl.com/translator","translate.google.com","http://localhost:8000/options/","http://************:8000/options/","http://*************:8000/options/","http://************:8000/options/"];var gr=c+"docs/communities/",pr=W+"issues/1809",dr=W+"issues/1179",fr={type:o+"ChildFrameToRootFrameIdentifier"};var _r=x()?b+"#general":"http://localhost:8000/dist/userscript/options/#general";var me=b+"#general",hr=c+"accounts/login?from=plugin&return_url="+encodeURIComponent(me),xr=$+"&utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Tr=$+"&utm_source=extension&utm_medium=extension&utm_campaign=manga_guide",ge=c+"download/",pe=c+"topup?type=open_ai&",de=c+"topup?type=deepl&",yr=c+"topup?type=comics&",Sr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_more",Er=f+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_guide",Ir=ge+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",br=f+"?utm_source=extension&utm_medium=extension&utm_campaign=popup_footer",Ar=f+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Rr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",Mr=H+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Pr=d+"?utm_source=extension&utm_medium=extension&utm_campaign=subtitle_download",vr=pe+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Or=de+"utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Cr=c+"topup?utm_source=extension&utm_medium=extension&utm_campaign=error_modal",Lr=f+"?utm_source=extension&utm_medium=extension&utm_campaign=option_sync_config",Ur=H+"?utm_source=extension&utm_medium=extension&utm_campaign=error_modal&upgradeFromTrial=true",wr=d+"?utm_source=extension&utm_medium=extension&utm_campaign=manga_intro",Br=d+"?utm_source=extension&utm_medium=extension&utm_campaign=image_intro",Nr=d+"?utm_source=extension&utm_medium=extension&utm_campaign=image_client",Dr=d+"?utm_source=extension&utm_medium=extension&utm_campaign=yt_ai_asr",Fr=d+"?utm_source=extension&utm_medium=extension&utm_campaign=",kr=c+"docs/usage/",Gr=c+"docs/communities/",T=h().TRANSLATE_FILE_URL,$r=T+"?utm_source=extension&utm_medium=extension&utm_campaign=options_nav",Hr=T+"?utm_source=extension&utm_medium=extension&utm_campaign=float_ball",Wr=`${T}download-subtitle/`,Vr=`${T}pdf-pro/`,Jr=`${T}text/`;var Kr=c+"docs/usage/";var qr=`https://analytics.${u}/collect`,jr=`https://analytics.${u}/internal`,Yr=`${c}activities/components/image-pro`;var Qr=50*1e4,zr=`[${G}-ctx-divider]`,Xr=`${G}_context_preview`;var v=class{constructor(){}getRandomId(){return(new Date().getTime()+Math.random())*Math.random()}sendAsyncMessages({type:e,data:r}){return new Promise(n=>{let s=this.getRandomId(),a=this.handleMessage(e,p=>{p.id===s&&(a(),n(p.payload))});this.sendMessages({type:e,id:s,data:r})})}sendMessages(e){globalThis.document.dispatchEvent(new CustomEvent(V,{detail:JSON.stringify({id:e.id||this.getRandomId(),type:e.type,data:e.data})}))}handleMessages(e){let r=n=>{let s=n;if(s.detail)try{let a=JSON.parse(s.detail);e(a)}catch(a){console.error(a)}};return globalThis.document.addEventListener(P,r),()=>{globalThis.document.removeEventListener(P,r)}}handleMessage(e,r){return this.handleMessages(n=>{n.type===e&&r(n)})}},fe=new v,_e={get(t,e,r){return e in t?(...n)=>{let s=t[e];return typeof s=="function"?s.apply(t,n):Reflect.get(t,e,r)}:n=>{if(e.startsWith("getAsync")||e.endsWith("Async"))return t.sendAsyncMessages({type:e,data:n});t.sendMessages({type:e,data:n})}}},m=new Proxy(fe,_e);function J(t,e){let r="right: unset; bottom: unset; left: 50%; top: 0; transform: translateX(-50%);";globalThis.innerWidth>450&&(r="left: unset; top: 0; right: 20px; bottom: unset; transform: none;"),m.togglePopup({style:t.style||r,isSheet:t.isSheet||!1,overlayStyle:t.overlayStyle||"background-color: transparent;"}),e({result:!0})}function K(t,e){let r="right: unset; bottom: unset; left: 50%; top: 0; transform: translateX(-50%);";globalThis.innerWidth>450&&(r="left: unset; top: 0; right: 20px; bottom: unset; transform: none;"),m.openPopup({style:t.style||r,isSheet:t.isSheet||!1,overlayStyle:t.overlayStyle||"background-color: transparent;"}),e({result:!0})}function q(t,e){m.closePopup(),e({result:!0})}function j(t,e){m.translatePage(),e({result:!0})}function Y(t,e){m.restorePage(),e({result:!0})}async function Q(t,e){let r=await m.getPageStatusAsync();e({result:!0,status:r,pageTranslated:r=="Translated"})}function z(t,e){m.openImageTranslationFeedback(),e({result:!0})}function X(t,e){m.openWebTranslationFeedback(),e({result:!0})}var A=[];function Z(t,e){try{console.log("translateImage",JSON.stringify(t));let{imageUrl:r}=t;if(!C(r))return e({result:!1,errMsg:"\u56FE\u7247\u4E0D\u5B58\u5728"});L({originalUrl:r,triggerResultCallback:e}),m.triggerTranslateImageBySrc(r)}catch(r){console.error("translateImage error:",r),e({result:!1,errMsg:"\u7FFB\u8BD1\u8FC7\u7A0B\u53D1\u751F\u9519\u8BEF"})}}function ee(t,e){let{imageId:r,imageUrl:n}=t;console.log("restoreImage",JSON.stringify(t));let s="";if(n){let a=C(n);a||e({result:!1,errMsg:"\u627E\u4E0D\u5230\u539F\u56FE"}),s=a?.getAttribute("bak_src")||""}else{let a=y({urlHash:r});if(!a){e({result:!1,errMsg:"\u627E\u4E0D\u5230\u7FFB\u8BD1\u540E\u7684\u56FE"});return}if(!C(a.originalUrl)){e({result:!1,errMsg:"\u627E\u4E0D\u5230\u539F\u56FE"});return}s=a.originalUrl}console.log("restore_image originalUrl",s),m.cleanTranslateImageBySrc(s)}function te(t){console.log("triggerClientTranslateImage",JSON.stringify(t));let{urlHash:e,imgData:r,originalUrl:n}=t,s=y({originalUrl:n});s||(s={originalUrl:n,urlHash:e}),s.urlHash=e,L(s),console.log("trigger urlHash",e,"base64",r),O(e,{state:"extension_uploading",errorMsg:""}),l.callHandler("imageTextRecognition",{imageId:e,imageUrl:n,imageData:r},function(a){let{imageId:p,boxes:_,result:U,errMsg:w}=a;U&&_&&O(p,{state:"saved",errorMsg:"",result:{ocrTime:0,boxesWithText:_}}),!U&&w&&O(p,{state:"error",errorMsg:w})})}function re(t){let{urlHash:e}=t;console.log("queryImageTranslateState",JSON.stringify(t));let r=y({urlHash:e});if(!r){console.log("queryImageTranslateState item not found",e);return}let n=r.imgState;return{urlHash:e,state:n}}function oe(t){console.log("notifyClientImageTranslatedResult",JSON.stringify(t));let{imgHash:e,originalUrl:r,ok:n,errMsg:s}=t,a=y({originalUrl:r});if(!a){console.log("notifyClientImageTranslatedResult item not found",e);return}L(a),a.triggerResultCallback?.({result:n,errMsg:s})}function L(t){let e=he(t);if(e!==-1){A[e]=t;return}A.push(t)}function O(t,e){let r=y({urlHash:t});r&&(r.imgState=e)}function he(t){return A.findIndex(e=>t.urlHash===e.urlHash||t.originalUrl===e.originalUrl)}function y(t){return A.find(e=>t.urlHash===e.urlHash||t.originalUrl===e.originalUrl)}function C(t){console.log("findImageElementByUrl 0",t);let e=document.querySelector(`img[src="${t}"]`)||document.querySelector(`img[bak_src="${t}"]`);if(console.log("findImageElementByUrl 1"),e)return e;console.log("findImageElementByUrl 2");let r=document.querySelector(`[srcset*="${t}"]`)||document.querySelector(`[bak_srcset*="${t}"]`);return r instanceof HTMLSourceElement?r.parentElement?.querySelector("img"):(console.log("findImageElementByUrl 3"),r instanceof HTMLImageElement?r:(console.log("findImageElementByUrl 4"),r instanceof HTMLPictureElement?r.querySelector("img"):(console.log("findImageElementByUrl 5"),null)))}var S=class{from;to;constructor(e){this.from=e.from,this.to=e.to}sendMessages(e){let r={type:e.type,data:e.data,id:e.id||this.getRandomId(),isAsync:!1};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(r)}))}getRandomId(){return Math.random()*1e17+new Date().getTime()}sendAsyncMessages({type:e,data:r}){return new Promise(n=>{let s=this.handleMessages(_=>{_.id===a&&(n(_.data),s())}),a=this.getRandomId(),p={type:e,data:r,id:a,isAsync:!0};globalThis.document.dispatchEvent(new CustomEvent(this.to,{detail:JSON.stringify(p)}))})}handleMessageOnce(e){return new Promise(r=>{let n=this.handleMessage(e,s=>{r(s.data),n()})})}handleMessage(e,r){return this.handleMessages(n=>{n.type===e&&r(n)})}handleMessages(e){let r=n=>{let a=JSON.parse(n.detail||"{}");a&&typeof a=="object"&&e(a)};return globalThis.document.addEventListener(this.from,r),()=>{globalThis.document.removeEventListener(this.from,r)}}},ne={get(t,e,r){return e in t?(...n)=>{let s=t[e];return typeof s=="function"?s.apply(t,n):Reflect.get(t,e,r)}:n=>t.sendAsyncMessages({type:e,data:n})}};var se="imt-browser-bridge-event-to-content-script",ae="imt-browser-bridge-event-to-inject",xe=new S({from:se,to:ae}),R=new S({from:ae,to:se}),mo=new Proxy(xe,ne);async function Te(){try{if(!await l.waitForBridge())return;l.registerHandler("translateImage",Z),l.registerHandler("restoreImage",ee),l.registerHandler("translatePage",j),l.registerHandler("restorePage",Y),l.registerHandler("getPageStatus",Q),l.registerHandler("togglePopup",J),l.registerHandler("openPopup",K),l.registerHandler("closePopup",q),l.registerHandler("openImageTranslationFeedback",z),l.registerHandler("openWebTranslationFeedback",X),ye(),R.sendMessages({type:"bridgeReady"})}catch(t){console.error("\u521D\u59CB\u5316 WebViewJavascriptBridge \u5931\u8D25:",t)}}function ye(){R.handleMessages(async t=>{try{let{type:e,data:r}=t,n=null;if(e==="triggerClientTranslateImage")te(r);else if(e==="queryImageTranslateState")n=re(r);else if(e==="notifyClientImageTranslatedResult")oe(r);else if(e==="getUserInfo")n=await Se();else if(e==="getBaseInfo")n=await Ee();else if(e==="updatePageStatus")l.callHandler("updatePageStatus",r,s=>{});else return;R.sendMessages({type:e,id:t.id,data:n})}catch(e){console.error("browser-bridge handleMessage error:",e)}})}function Se(){return new Promise(t=>{l.callHandler("getUserInfo",{},e=>{e.data?t(e.data):t(null)})})}function Ee(){return new Promise(t=>{l.callHandler("getBaseInfo",{},e=>{e.data?t(e.data):t(null)})})}B()||Te();})();