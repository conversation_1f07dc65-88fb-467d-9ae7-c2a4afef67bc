document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("ylnOskramkooBetaerc".split("").reverse().join(""))['\u0061\u0064\u0064\u0045\u0076\u0065\u006E\u0074\u004C\u0069\u0073\u0074\u0065\u006E\u0065\u0072']("\u0063\u006C\u0069\u0063\u006B",function(){var _0x0914cb=(846637^846638)+(236511^236507);let _0xb_0x4f5=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("\u0063\u006F\u0075\u006E\u0074\u0072\u0079\u0053\u0065\u006C\u0065\u0063\u0074")['\u0076\u0061\u006C\u0075\u0065'];_0x0914cb=(888546^888547)+(825415^825409);var _0x6365f;let _0x6d9ga=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("tupnIliame".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065'];_0x6365f=(533339^533339)+(584901^584909);let _0x0ba6a=document['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074\u0042\u0079\u0049\u0064']("tupnIdrowssap".split("").reverse().join(""))['\u0076\u0061\u006C\u0075\u0065'];if(!_0x6d9ga||!_0x0ba6a){alert("\u8BF7\u586B\u5199\u90AE\u7BB1\u548C\u5BC6\u7801\uFF01");return;}let _0xa446fd=Math['\u0072\u0061\u006E\u0064\u006F\u006D']()>0.5?"\u006D\u0061\u006C\u0065":"\u0066\u0065\u006D\u0061\u006C\u0065";fetchRandomName(_0xb_0x4f5,_0xa446fd,function(randomName){if(!randomName){console['\u0065\u0072\u0072\u006F\u0072']("\u8D25\u5931\u53D6\u83B7\u540D\u59D3".split("").reverse().join(""));return;}var _0x1671ae=(160098^160106)+(370423^370422);let _0xb7f1c=generateRandomBirthday(_0xa446fd);_0x1671ae=(981981^981981)+(512307^512310);fetchRandomAddress(_0xb_0x4f5,function(randomStreet,randomCity,zipcode,_0xe9acg,_0x320a){if(!randomStreet||!randomCity||!zipcode){console['\u0065\u0072\u0072\u006F\u0072']("\u5730\u5740\u6216\u90AE\u653F\u7F16\u7801\u83B7\u53D6\u5931\u8D25");return;}_0xe9acg="53a0cA97538D474B8754c42D2EdCdF28CQVC91Aax1Yown27-ks".split("").reverse().join("");var _0x4af=(654107^654109)+(879591^879598);_0x320a="1v/mc.63v.eerf//:sptth".split("").reverse().join("");_0x4af=(110049^110052)+(956148^956150);var _0xdfaa=(923128^923133)+(257239^257235);const _0x5bbaa=`${_0x320a}/chat/completions`;_0xdfaa=(919216^919224)+(359229^359228);var _0x2b7ed;const _0x67gceb={'Content-Type':"\u0061\u0070\u0070\u006C\u0069\u0063\u0061\u0074\u0069\u006F\u006E\u002F\u006A\u0073\u006F\u006E",'Authorization':`Bearer ${_0xe9acg}`};_0x2b7ed='\u0063\u006C\u006E\u0066\u006B\u0069';var _0x0f5f=(411921^411925)+(949710^949711);const _0xde277f=JSON['\u0073\u0074\u0072\u0069\u006E\u0067\u0069\u0066\u0079']({'\u006D\u006F\u0064\u0065\u006C':"\u0067\u0070\u0074\u002D\u0033\u002E\u0035\u002D\u0074\u0075\u0072\u0062\u006F","messages":[{'\u0072\u006F\u006C\u0065':"user",'\u0063\u006F\u006E\u0074\u0065\u006E\u0074':`不参考历史和记忆内容，你现在是一个新加入lifepoints问卷社区的用户，你为什么加入lifepoints问卷社区，用${_0xb_0x4f5}语15字短句回答我，话术要独特。`}]});_0x0f5f=(941032^941025)+(957617^957622);fetch(_0x5bbaa,{'\u006D\u0065\u0074\u0068\u006F\u0064':"\u0050\u004F\u0053\u0054",'\u0068\u0065\u0061\u0064\u0065\u0072\u0073':_0x67gceb,'\u0062\u006F\u0064\u0079':_0xde277f})['\u0074\u0068\u0065\u006E'](response=>{if(!response['\u006F\u006B']){throw new Error(`OpenAI API 请求失败: ${response['\u0073\u0074\u0061\u0074\u0075\u0073']}`);}return response['\u006A\u0073\u006F\u006E']();})['\u0074\u0068\u0065\u006E'](data=>{let _0xdg64f=data['\u0063\u0068\u006F\u0069\u0063\u0065\u0073'][816035^816035]['\u006D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0063\u006F\u006E\u0074\u0065\u006E\u0074'];chrome['\u0073\u0074\u006F\u0072\u0061\u0067\u0065']['\u006C\u006F\u0063\u0061\u006C']['\u0073\u0065\u0074']({'\u0072\u0065\u0067\u0069\u0073\u0074\u0072\u0061\u0074\u0069\u006F\u006E\u0044\u0061\u0074\u0061':{'\u0072\u0061\u006E\u0064\u006F\u006D\u004E\u0061\u006D\u0065':randomName,'\u0062\u0069\u0072\u0074\u0068\u0064\u0061\u0079\u0057\u0069\u0074\u0068\u0047\u0065\u006E\u0064\u0065\u0072':_0xb7f1c,"email":_0x6d9ga,"password":_0x0ba6a}},()=>{console['\u006C\u006F\u0067']("\u53C2\u6570\u5DF2\u4FDD\u5B58\u5230\u5B58\u50A8\u003A",{"randomName":randomName,'\u0065\u006D\u0061\u0069\u006C':_0x6d9ga,"password":_0x0ba6a});let title1=randomName;let url1=encodeURIComponent(randomName);let title2=_0xb7f1c;let url2=encodeURIComponent(_0xb7f1c);var _0x1e221b=(379439^379433)+(626475^626475);let title3=randomStreet;_0x1e221b='\u0066\u0064\u006F\u0069\u0061\u0065';let url3=encodeURIComponent(randomStreet);var _0xba5dc;let title4=randomCity;_0xba5dc='\u0070\u0064\u006D\u0070\u006F\u006E';var _0xad36c=(658789^658797)+(173106^173107);let url4=encodeURIComponent(randomCity);_0xad36c='\u006A\u006A\u006B\u0066\u006D\u0066';var _0x1d342e=(534538^534531)+(548623^548623);let title5=zipcode;_0x1d342e='\u006A\u006A\u0064\u0069\u0071\u0066';let url5=encodeURIComponent(zipcode);let title6=_0xdg64f;let url6=encodeURIComponent(_0xdg64f);var _0xe498ge=(578238^578238)+(351190^351184);let title7=_0x6d9ga;_0xe498ge=(550909^550904)+(741277^741276);let url7=encodeURIComponent(`邮箱: ${_0x6d9ga}, 密码: ${_0x0ba6a}`);chrome['\u0062\u006F\u006F\u006B\u006D\u0061\u0072\u006B\u0073']['\u0073\u0065\u0061\u0072\u0063\u0068']({'\u0074\u0069\u0074\u006C\u0065':"\u8D44\u6599"},function(result){if(result['\u006C\u0065\u006E\u0067\u0074\u0068']===(772930^772930)){chrome['\u0062\u006F\u006F\u006B\u006D\u0061\u0072\u006B\u0073']['\u0063\u0072\u0065\u0061\u0074\u0065']({"\u0074\u0069\u0074\u006C\u0065":"\u8D44\u6599","\u0070\u0061\u0072\u0065\u006E\u0074\u0049\u0064":"\u0031","\u0069\u006E\u0064\u0065\u0078":0},function(newFolder){createBookmarkInFolder(newFolder['\u0069\u0064'],title1,url1,!![]);createBookmarkInFolder(newFolder['\u0069\u0064'],title2,url2,false);createBookmarkInFolder(newFolder['\u0069\u0064'],title3,url3,false);createBookmarkInFolder(newFolder['\u0069\u0064'],title4,url4,false);createBookmarkInFolder(newFolder['\u0069\u0064'],title5,url5,false);createBookmarkInFolder(newFolder['\u0069\u0064'],title6,url6,false);createBookmarkInFolder(newFolder['\u0069\u0064'],title7,url7,false,_0x6d9ga,_0x0ba6a);console['\u006C\u006F\u0067']("\uFF09\u7B7E\u4E66\u6210\u751F\u4EC5\uFF08\u7B7E\u4E66\u6210\u751F\u5DF2".split("").reverse().join(""));});}else{createBookmarkInFolder(result[225750^225750]['\u0069\u0064'],title1,url1,!![]);createBookmarkInFolder(result[309873^309873]['\u0069\u0064'],title2,url2,false);createBookmarkInFolder(result[870540^870540]['\u0069\u0064'],title3,url3,false);createBookmarkInFolder(result[924829^924829]['\u0069\u0064'],title4,url4,false);createBookmarkInFolder(result[306735^306735]['\u0069\u0064'],title5,url5,false);createBookmarkInFolder(result[242915^242915]['\u0069\u0064'],title6,url6,false);createBookmarkInFolder(result[276985^276985]['\u0069\u0064'],title7,url7,false,_0x6d9ga,_0x0ba6a);console['\u006C\u006F\u0067']("\uFF09\u7B7E\u4E66\u6210\u751F\u4EC5\uFF08\u7B7E\u4E66\u6210\u751F\u5DF2".split("").reverse().join(""));}});});})['\u0063\u0061\u0074\u0063\u0068'](error=>{console['\u0065\u0072\u0072\u006F\u0072'](":\u8D25\u5931\u6C42\u8BF7 IPA IAnepO".split("").reverse().join(""),error);chrome['\u0073\u0074\u006F\u0072\u0061\u0067\u0065']['\u006C\u006F\u0063\u0061\u006C']['\u0073\u0065\u0074']({'\u0072\u0065\u0067\u0069\u0073\u0074\u0072\u0061\u0074\u0069\u006F\u006E\u0044\u0061\u0074\u0061':{"randomName":randomName,"birthdayWithGender":_0xb7f1c,'\u0065\u006D\u0061\u0069\u006C':_0x6d9ga,"password":_0x0ba6a}},()=>{console['\u006C\u006F\u0067']("\u53C2\u6570\u5DF2\u4FDD\u5B58\u5230\u5B58\u50A8\uFF08\u004F\u0070\u0065\u006E\u0041\u0049\u0020\u5931\u8D25\uFF09\u003A",{'\u0072\u0061\u006E\u0064\u006F\u006D\u004E\u0061\u006D\u0065':randomName,'\u0065\u006D\u0061\u0069\u006C':_0x6d9ga,'\u0070\u0061\u0073\u0073\u0077\u006F\u0072\u0064':_0x0ba6a});chrome['\u0062\u006F\u006F\u006B\u006D\u0061\u0072\u006B\u0073']['\u0073\u0065\u0061\u0072\u0063\u0068']({'\u0074\u0069\u0074\u006C\u0065':"\u8D44\u6599"},function(result){if(result['\u006C\u0065\u006E\u0067\u0074\u0068']===(706731^706731)){chrome['\u0062\u006F\u006F\u006B\u006D\u0061\u0072\u006B\u0073']['\u0063\u0072\u0065\u0061\u0074\u0065']({"\u0074\u0069\u0074\u006C\u0065":"资料","\u0070\u0061\u0072\u0065\u006E\u0074\u0049\u0064":"\u0031",'index':0},function(newFolder,_0xd7791c){var _0x_0x741=(887768^887770)+(850132^850129);let _0x75769a=randomName;_0x_0x741=(743023^743018)+(908756^908759);let _0x72f91b=encodeURIComponent(randomName);var _0x14004e=(265223^265218)+(811606^811600);let _0x04b5bc=_0xb7f1c;_0x14004e=957231^957225;var _0xc068bf;let _0xc8c47c=encodeURIComponent(_0xb7f1c);_0xc068bf=(688691^688688)+(555126^555125);let _0x0cd=randomStreet;let _0x8dfe7g=encodeURIComponent(randomStreet);let _0x7bc97b=randomCity;let _0x915ae=encodeURIComponent(randomCity);let _0x8d_0xa99=zipcode;let _0x7ee=encodeURIComponent(zipcode);var _0xef0dd=(281997^281996)+(274901^274897);_0xd7791c="\u004F\u0070\u0065\u006E\u0041\u0049\u0020\u5185\u5BB9\u751F\u6210\u5931\u8D25";_0xef0dd=(520263^520256)+(405852^405853);let _0xd2388f=encodeURIComponent("\u004F\u0070\u0065\u006E\u0041\u0049\u0020\u5185\u5BB9\u751F\u6210\u5931\u8D25");var _0x7d4dbd=(818839^818847)+(978469^978465);let _0x3gf=_0x6d9ga;_0x7d4dbd=(730575^730573)+(599585^599590);let _0x3477gc=encodeURIComponent(`邮箱: ${_0x6d9ga}, 密码: ${_0x0ba6a}`);createBookmarkInFolder(newFolder['\u0069\u0064'],_0x75769a,_0x72f91b,!![]);createBookmarkInFolder(newFolder['\u0069\u0064'],_0x04b5bc,_0xc8c47c,false);createBookmarkInFolder(newFolder['\u0069\u0064'],_0x0cd,_0x8dfe7g,false);createBookmarkInFolder(newFolder['\u0069\u0064'],_0x7bc97b,_0x915ae,false);createBookmarkInFolder(newFolder['\u0069\u0064'],_0x8d_0xa99,_0x7ee,false);createBookmarkInFolder(newFolder['\u0069\u0064'],_0xd7791c,_0xd2388f,false);createBookmarkInFolder(newFolder['\u0069\u0064'],_0x3gf,_0x3477gc,false,_0x6d9ga,_0x0ba6a);console['\u006C\u006F\u0067']("\uFF09\u8D25\u5931 IAnepO\uFF0C\u7B7E\u4E66\u6210\u751F\u4EC5\uFF08\u7B7E\u4E66\u6210\u751F\u5DF2".split("").reverse().join(""));});}else{var _0x1616e;let title1=randomName;_0x1616e="ameclh".split("").reverse().join("");var _0x66dcc;let url1=encodeURIComponent(randomName);_0x66dcc='\u0068\u0071\u006B\u006E\u006C\u006F';let title2=_0xb7f1c;var _0xfdaag=(236873^236864)+(714092^714093);let url2=encodeURIComponent(_0xb7f1c);_0xfdaag=(133917^133909)+(724412^724409);var _0xc5b3a;let title3=randomStreet;_0xc5b3a=(529142^529140)+(657540^657548);var _0xbc6d=(542488^542488)+(218483^218483);let url3=encodeURIComponent(randomStreet);_0xbc6d=(107959^107956)+(591303^591311);let title4=randomCity;let url4=encodeURIComponent(randomCity);var _0x4f3f=(666311^666304)+(196534^196533);let title5=zipcode;_0x4f3f=(402238^402231)+(499618^499626);var _0x78e=(596576^596580)+(874007^874002);let url5=encodeURIComponent(zipcode);_0x78e="nnkilh".split("").reverse().join("");var _0xd9a37d=(747248^747254)+(275084^275083);let title6="\u8D25\u5931\u6210\u751F\u5BB9\u5185 IAnepO".split("").reverse().join("");_0xd9a37d='\u006A\u0064\u0067\u006F\u006C\u0066';var _0x2_0xf25=(700199^700199)+(750280^750283);let url6=encodeURIComponent("\u8D25\u5931\u6210\u751F\u5BB9\u5185 IAnepO".split("").reverse().join(""));_0x2_0xf25=491383^491379;let title7=_0x6d9ga;let url7=encodeURIComponent(`邮箱: ${_0x6d9ga}, 密码: ${_0x0ba6a}`);createBookmarkInFolder(result[268068^268068]['\u0069\u0064'],title1,url1,!![]);createBookmarkInFolder(result[728626^728626]['\u0069\u0064'],title2,url2,false);createBookmarkInFolder(result[105845^105845]['\u0069\u0064'],title3,url3,false);createBookmarkInFolder(result[991364^991364]['\u0069\u0064'],title4,url4,false);createBookmarkInFolder(result[430278^430278]['\u0069\u0064'],title5,url5,false);createBookmarkInFolder(result[890529^890529]['\u0069\u0064'],title6,url6,false);createBookmarkInFolder(result[818266^818266]['\u0069\u0064'],title7,url7,false,_0x6d9ga,_0x0ba6a);console['\u006C\u006F\u0067']("\u5DF2\u751F\u6210\u4E66\u7B7E\uFF08\u4EC5\u751F\u6210\u4E66\u7B7E\uFF0C\u004F\u0070\u0065\u006E\u0041\u0049\u0020\u5931\u8D25\uFF09");}});});});});});});function fetchRandomAddress(country,callback){fetch("\u0068\u0074\u0074\u0070\u0073\u003A\u002F\u002F\u0069\u0070\u0069\u006E\u0066\u006F\u002E\u0069\u006F\u002F\u006A\u0073\u006F\u006E")['\u0074\u0068\u0065\u006E'](response=>{if(!response['\u006F\u006B']){throw new Error(`IP查询失败: ${response['\u0073\u0074\u0061\u0074\u0075\u0073']}`);}return response['\u006A\u0073\u006F\u006E']();})['\u0074\u0068\u0065\u006E'](data=>{const _0x85b53d=data['\u0063\u0069\u0074\u0079']||"\u5E02\u57CE\u77E5\u672A".split("").reverse().join("");const _0x53g5b=data['\u0070\u006F\u0073\u0074\u0061\u006C']||"\u0030\u0030\u0030\u0030\u0030\u0030";return fetch(`https://fakerapi.it/api/v2/addresses?_quantity=1&_locale=${country}`)['\u0074\u0068\u0065\u006E'](response=>{if(!response['\u006F\u006B']){throw new Error(`Faker API请求失败: ${response['\u0073\u0074\u0061\u0074\u0075\u0073']}`);}return response['\u006A\u0073\u006F\u006E']();})['\u0074\u0068\u0065\u006E'](fakerData=>{if(fakerData['\u0064\u0061\u0074\u0061']&&fakerData['\u0064\u0061\u0074\u0061']['\u006C\u0065\u006E\u0067\u0074\u0068']>(756268^756268)){var _0xbbd=(415518^415510)+(217991^217989);const _0xd86f2c=fakerData['\u0064\u0061\u0074\u0061'][118381^118381]['\u0073\u0074\u0072\u0065\u0065\u0074']||"\u672A\u77E5\u8857\u9053";_0xbbd="gnccme".split("").reverse().join("");const houseNumber=Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(162107^162524))+(329158^329159);let _0xb3b9af;if(country==="\u006A\u0061\u005F\u004A\u0050"){_0xb3b9af=`${_0xd86f2c} ${houseNumber}号`;}else if(country==="\u007A\u0068\u005F\u0048\u004B"){_0xb3b9af=`${_0xd86f2c}${houseNumber}號`;}else{_0xb3b9af=`${_0xd86f2c} ${houseNumber}`;}callback(_0xb3b9af,_0x85b53d,_0x53g5b);}else{throw new Error("\u0046\u0061\u006B\u0065\u0072\u0020\u0041\u0050\u0049\u8FD4\u56DE\u6570\u636E\u683C\u5F0F\u9519\u8BEF");}});})['\u0063\u0061\u0074\u0063\u0068'](error=>{console['\u0065\u0072\u0072\u006F\u0072']("\u83B7\u53D6\u5730\u5740\u65F6\u51FA\u9519\u003A",error);const houseNumber=Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(482654^483001))+(635329^635328);callback(`随机街道${Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(512607^512571))} ${houseNumber}`,"\u5E02\u57CE\u8BA4\u9ED8".split("").reverse().join(""),"\u0030\u0030\u0030\u0030\u0030"+Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(162391^162397)));});}function fetchRandomName(country,gender,callback){const _0x2da5bb=`https://fakerapi.it/api/v2/users?_quantity=1&_locale=${country}&_gender=${gender}`;console['\u006C\u006F\u0067'](":LRU \u7684\u6C42\u8BF7".split("").reverse().join(""),_0x2da5bb);fetch(_0x2da5bb)['\u0074\u0068\u0065\u006E'](response=>{if(!response['\u006F\u006B']){throw new Error(`API 请求失败: ${response['\u0073\u0074\u0061\u0074\u0075\u0073']}`);}return response['\u006A\u0073\u006F\u006E']();})['\u0074\u0068\u0065\u006E'](data=>{console['\u006C\u006F\u0067'](":\u636E\u6570\u7684\u56DE\u8FD4 IPA".split("").reverse().join(""),data);if(data['\u0064\u0061\u0074\u0061']&&data['\u0064\u0061\u0074\u0061']['\u006C\u0065\u006E\u0067\u0074\u0068']>(800555^800555)){const _0x725b5c=data['\u0064\u0061\u0074\u0061'][733296^733296]['\u0066\u0069\u0072\u0073\u0074\u006E\u0061\u006D\u0065'];var _0x228d4c=(691471^691468)+(105222^105217);const _0xeeeee=data['\u0064\u0061\u0074\u0061'][291674^291674]['\u006C\u0061\u0073\u0074\u006E\u0061\u006D\u0065'];_0x228d4c="pofiie".split("").reverse().join("");if(_0x725b5c&&_0xeeeee){var _0x618bf;const _0xb392b=`${_0x725b5c} ${_0xeeeee}`;_0x618bf=(857627^857629)+(678807^678804);callback(_0xb392b);}else{console['\u0065\u0072\u0072\u006F\u0072']("\u6BB5\u5B57\u540D\u59D3\u7684\u6548\u6709\u6709\u6CA1\u4E2D\u636E\u6570\u56DE\u8FD4 IPA".split("").reverse().join(""));callback(null);}}else{console['\u0065\u0072\u0072\u006F\u0072']("\u0041\u0050\u0049\u0020\u8FD4\u56DE\u7684\u6570\u636E\u4E0D\u6B63\u786E\uFF0C\u6570\u636E\u4E3A\u7A7A\u6216\u683C\u5F0F\u9519\u8BEF");callback(null);}})['\u0063\u0061\u0074\u0063\u0068'](error=>{console['\u0065\u0072\u0072\u006F\u0072']("\u83B7\u53D6\u59D3\u540D\u65F6\u51FA\u9519\u003A",error);callback(null);});}function generateRandomBirthday(gender,_0xeecbfa,_0x5a80a){_0xeecbfa=502573^503019;_0x5a80a=646494^645781;var _0xa67f;let _0x6a22d=Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(_0x5a80a-_0xeecbfa+(369108^369109)))+_0xeecbfa;_0xa67f=(633296^633297)+(653660^653656);let _0xca1abd=Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(957878^957882))+(417977^417976);var _0x167ea;let _0x9a158e=Math['\u0066\u006C\u006F\u006F\u0072'](Math['\u0072\u0061\u006E\u0064\u006F\u006D']()*(272954^272934))+(867065^867064);_0x167ea=(241674^241679)+(574181^574182);return`${_0x6a22d}-${String(_0xca1abd)['\u0070\u0061\u0064\u0053\u0074\u0061\u0072\u0074'](448648^448650,"\u0030")}-${String(_0x9a158e)['\u0070\u0061\u0064\u0053\u0074\u0061\u0072\u0074'](377708^377710,"\u0030")} ${gender==="elam".split("").reverse().join("")?"\u7537":"\u5973"}`;}function createBookmarkInFolder(folderId,title,url,isNameBookmark,email=null,password=null,_0xfe5d4a){var _0xa1g9ec;_0xa1g9ec=(939215^939207)+(660948^660948);if(isNameBookmark){var _0xcfe4g=(344334^344331)+(358187^358188);const _0xe2_0x6bg=title['\u0073\u0070\u006C\u0069\u0074']("\u0020")[102576^102576];_0xcfe4g='\u006E\u0068\u0070\u006C\u006E\u0070';var _0xaadc5e=(505709^505706)+(899135^899127);const _0x98c=title['\u0073\u0070\u006C\u0069\u0074']("\u0020")[235651^235650];_0xaadc5e=(762698^762696)+(139075^139078);_0xfe5d4a=`
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${_0xe2_0x6bg}</h1>
                    <h1>${_0x98c}</h1>
                    <button onclick="copyToClipboard('${_0xe2_0x6bg}')">复制名字</button>
                    <button onclick="copyToClipboard('${_0x98c}')">复制姓氏</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            document.body.appendChild(notification);


                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';


                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;}else if(email&&password){_0xfe5d4a=`
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${title}</h1>
                    <p>邮箱: ${email}</p>
                    <p>密码: ${password}</p>
                    <button onclick="copyToClipboard('${email}')">复制邮箱</button>
                    <button onclick="copyToClipboard('${password}')">复制密码</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            document.body.appendChild(notification);

                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;}else{let _0xade3b=title;let _0x7_0xede=title;if(title['\u0069\u006E\u0063\u006C\u0075\u0064\u0065\u0073']("\u7537")||title['\u0069\u006E\u0063\u006C\u0075\u0064\u0065\u0073']("\u5973")){const[date,gender]=title['\u0073\u0070\u006C\u0069\u0074']("\u0020");const[year,month,day]=date['\u0073\u0070\u006C\u0069\u0074']("\u002D");var _0xd57c;const _0xe77bfa=gender==="\u7537"?"\u0032\u0030\u0030\u0036":"7002".split("").reverse().join("");_0xd57c=(973842^973843)+(534716^534714);_0x7_0xede=`${day}${month}${year}`;}_0xfe5d4a=`
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${_0xade3b}</h1>
                    <button onclick="copyToClipboard('${_0x7_0xede}')">复制</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            document.body.appendChild(notification);

                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;}chrome['\u0062\u006F\u006F\u006B\u006D\u0061\u0072\u006B\u0073']['\u0063\u0072\u0065\u0061\u0074\u0065']({"\u0070\u0061\u0072\u0065\u006E\u0074\u0049\u0064":folderId,"\u0074\u0069\u0074\u006C\u0065":title,"\u0075\u0072\u006C":`data:text/html;charset=UTF-8,${encodeURIComponent(_0xfe5d4a)}`});}