document.getElementById('createBookmarksOnly').addEventListener('click', function() {
    let selectedCountry = document.getElementById('countrySelect').value;  // 获取选中的国家代码
    let email = document.getElementById('emailInput').value;  // 获取用户输入的邮箱
    let password = document.getElementById('passwordInput').value;  // 获取用户输入的密码

    if (!email || !password) {
        alert('请填写邮箱和密码！');
        return;
    }

    // 随机选择性别
    let gender = Math.random() > 0.5 ? 'male' : 'female';  // 随机选择男或女

    // 获取随机姓名（本地生成）
    let randomName = generateRandomName(selectedCountry, gender);
    if (!randomName) {
        console.error("姓名生成失败");
        return;
    }

    // 生成1987到1995年之间的随机日期，并添加性别
    let birthdayWithGender = generateRandomBirthday(gender);

    // 获取随机地址（本地生成）
    let { randomStreet, randomCity, zipcode } = generateRandomAddress(selectedCountry);
    if (!randomStreet || !randomCity || !zipcode) {
        console.error("地址或邮政编码生成失败");
        return;
    }

    // 获取随机短句（本地生成）
    let reasonContent = generateRandomReason(selectedCountry);

    // 保存参数到 chrome.storage.local
    chrome.storage.local.set({
        registrationData: {
            randomName: randomName,
            birthdayWithGender: birthdayWithGender,
            email: email,
            password: password
        }
    }, () => {
        console.log('参数已保存到存储:', { randomName, email, password });

        // 创建书签：姓名、出生日期和性别、街道、城市、邮政编码、短句、邮箱
        let title1 = randomName;
        let url1 = encodeURIComponent(randomName);  // 书签名称和 URL 都是姓名

        let title2 = birthdayWithGender;
        let url2 = encodeURIComponent(birthdayWithGender);  // 书签名称和 URL 都是出生日期和性别

        let title3 = randomStreet;
        let url3 = encodeURIComponent(randomStreet);  // 书签名称和 URL 都是街道

        let title4 = randomCity;
        let url4 = encodeURIComponent(randomCity);  // 书签名称和 URL 都是城市

        let title5 = zipcode;
        let url5 = encodeURIComponent(zipcode);  // 书签名称和 URL 都是邮政编码

        let title6 = reasonContent;
        let url6 = encodeURIComponent(reasonContent);  // 书签名称和 URL 都是短句

        let title7 = email; // 书签名称改为邮箱
        let url7 = encodeURIComponent(`邮箱: ${email}, 密码: ${password}`); // 书签 URL 包含邮箱和密码

        // 查找文件夹"资料"是否存在
        chrome.bookmarks.search({ title: "资料" }, function(result) {
            if (result.length === 0) {
                // 文件夹"资料"不存在，创建文件夹并指定父级为书签栏（parentId = "1"）并设置索引为0
                chrome.bookmarks.create({
                    'title': "资料",
                    'parentId': '1',  // 将文件夹放在书签栏上
                    'index': 0        // 设置文件夹为书签栏中的第一项
                }, function(newFolder) {
                    // 文件夹创建成功后，生成书签并放入该文件夹
                    createBookmarkInFolder(newFolder.id, title1, url1, true); // 姓名书签分成两行
                    createBookmarkInFolder(newFolder.id, title2, url2, false);
                    createBookmarkInFolder(newFolder.id, title3, url3, false); // 街道书签
                    createBookmarkInFolder(newFolder.id, title4, url4, false); // 城市书签
                    createBookmarkInFolder(newFolder.id, title5, url5, false); // 邮政编码书签
                    createBookmarkInFolder(newFolder.id, title6, url6, false); // 短句书签
                    createBookmarkInFolder(newFolder.id, title7, url7, false, email, password); // 邮箱和密码书签

                    console.log('已生成书签（仅生成书签）');
                });
            } else {
                // 文件夹"资料"已存在，直接在该文件夹内生成书签
                createBookmarkInFolder(result[0].id, title1, url1, true); // 姓名书签分成两行
                createBookmarkInFolder(result[0].id, title2, url2, false);
                createBookmarkInFolder(result[0].id, title3, url3, false); // 街道书签
                createBookmarkInFolder(result[0].id, title4, url4, false); // 城市书签
                createBookmarkInFolder(result[0].id, title5, url5, false); // 邮政编码书签
                createBookmarkInFolder(result[0].id, title6, url6, false); // 短句书签
                createBookmarkInFolder(result[0].id, title7, url7, false, email, password); // 邮箱和密码书签

                console.log('已生成书签（仅生成书签）');
            }
        });
    });
});

// 本地生成随机姓名
function generateRandomName(country, gender) {
    const names = {
        'en_US': {
            male: {
               

 firstNames: ['James', 'Michael', 'William', 'David', 'John', 'Robert', 'Thomas', 'Charles', 'Christopher', 'Daniel', 'Matthew', 'Andrew', 'Joseph', 'Mark', 'Paul'],
                lastNames: ['Smith', 'Johnson', 'Brown', 'Taylor', 'Wilson', 'Davis', 'Clark', 'Harris',  'Lewis', 'Walker', 'Hall', 'Allen', 'Young', 'King', 'Wright']
            },
            female: {
                firstNames: ['Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Susan', 'Jessica', 'Sarah', 'Karen', 'Nancy', 'Lisa', 'Betty', 'Dorothy', 'Sandra', 'Ashley'],
                lastNames: ['Smith', 'Johnson', 'Brown', 'Taylor', 'Wilson', 'Davis', 'Clark', 'Harris', 'Lewis', 'Walker', 'Hall', 'Allen', 'Young', 'King', 'Wright']
            }
        },
        'en_GB': {
            male: {
                firstNames: ['Oliver', 'George', 'Harry', 'Jack', 'Thomas', 'Charlie', 'Jacob', 'Alfie', 'Freddie', 'William', 'James', 'Henry', 'Edward', 'Oscar', 'Leo'],
                lastNames: ['Smith', 'Jones', 'Taylor', 'Wilson', 'Davies', 'Brown', 'Evans', 'Thomas', 'Johnson', 'Roberts', 'Walker', 'Wright', 'Thompson', 'Hughes', 'Lewis']
            },
            female: {
                firstNames: ['Olivia', 'Amelia', 'Isla', 'Ava', 'Emily', 'Sophia', 'Grace', 'Lily', 'Mia', 'Charlotte', 'Evie', 'Poppy', 'Sophie', 'Isabella', 'Freya'],
                lastNames: ['Smith', 'Jones', 'Taylor', 'Wilson', 'Davies', 'Brown', 'Evans', 'Thomas', 'Johnson', 'Roberts', 'Walker', 'Wright', 'Thompson', 'Hughes', 'Lewis']
            }
        }
    };

    const countryNames = names[country] || names['en_US']; // 默认使用美国名字
    const genderNames = countryNames[gender];
    const firstName = genderNames.firstNames[Math.floor(Math.random() * genderNames.firstNames.length)];
    const lastName = genderNames.lastNames[Math.floor(Math.random() * genderNames.lastNames.length)];
    return `${firstName} ${lastName}`;
}

// 本地生成随机地址和邮编
function generateRandomAddress(country) {
    const addresses = {
        'en_US': {
            streets: ['Main Street', 'Park Avenue', 'Elm Street', 'Cedar Road', 'Oak Lane', 'Maple Drive', 'Washington Boulevard', 'Lincoln Street', 'Jefferson Avenue', 'Pine Road', 'Spruce Lane', 'Birch Street', 'Walnut Drive', 'Chestnut Road', 'Highland Avenue'],
            cities: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'San Francisco', 'Columbus', 'Seattle']
        },
        'en_GB': {
            streets: ['High Street', 'Station Road', 'Church Lane', 'Park Road', 'Victoria Street', 'Queen’s Road', 'King’s Lane', 'Castle Street', 'Mill Lane', 'Bridge Road', 'Grove Street', 'Chapel Road', 'Manor Drive', 'Green Lane', 'West End'],
            cities: ['London', 'Manchester', 'Birmingham', 'Glasgow', 'Edinburgh', 'Liverpool', 'Bristol', 'Sheffield', 'Leeds', 'Leicester', 'Coventry', 'Bradford', 'Cardiff', 'Belfast', 'Nottingham'],
            postcodeAreas: ['SW', 'EC', 'NW', 'SE', 'W', 'WC', 'E', 'N', 'M', 'B', 'G', 'EH', 'L', 'BS', 'S', 'LE', 'CV', 'BD', 'CF', 'BT']
        }
    };

    const countryAddresses = addresses[country] || addresses['en_US']; // 默认使用美国地址
    const street = countryAddresses.streets[Math.floor(Math.random() * countryAddresses.streets.length)];
    const city = countryAddresses.cities[Math.floor(Math.random() * countryAddresses.cities.length)];
    const houseNumber = Math.floor(Math.random() * 999) + 1;

    let zipcode;
    if (country === 'en_GB') {
         // 英国邮编前半段格式（如SW1A）
         const letters = 'ABCDEFGHIJKLMNOQRSTUVWXYZ';
         const numbers = '0123456789';
         const randomChar = (str) => str.charAt(Math.floor(Math.random() * str.length));
                  
         zipcode = `${randomChar(letters)}${Math.floor(Math.random() * 9) + 1}`;
    } else {
        zipcode = `00000${Math.floor(Math.random() * 100000)}`.slice(-5); // 美国邮编保持5位数字
    }

    // 街道格式（美国和英国使用相同格式）
    const finalStreet = `${street} ${houseNumber}`;

    return { randomStreet: finalStreet, randomCity: city, zipcode };
}

// 本地生成随机短句
function generateRandomReason(country) {
    const reasons = {
        'en_US': [
            'I joined LifePoints to share my opinions and earn rewards.',
            'LifePoints lets me influence brands while earning extra cash.',
            'I enjoy giving feedback on products through LifePoints surveys.',
            'Joining LifePoints helps me shape the future of services.',
            'I use LifePoints to voice my views and get rewards.',
            'LifePoints is a fun way to share my thoughts daily.',
            'I joined LifePoints to impact brands and earn points.',
            'LifePoints allows me to share insights and gain benefits.'
        ],
        'en_GB': [
            'I joined LifePoints to share my views and earn rewards.',
            'LifePoints helps me influence brands while earning money.',
            'I enjoy shaping products with my feedback on LifePoints.',
            'Joining LifePoints lets me share opinions and get perks.',
            'LifePoints is my way to impact services and earn.',
            'I use LifePoints to voice thoughts and gain rewards.',
            'LifePoints offers a fun way to share my insights.',
            'I joined LifePoints to shape brands and earn points.'
        ]
    };

    const countryReasons = reasons[country] || reasons['en_US']; // 默认使用美国短句
    return countryReasons[Math.floor(Math.random() * countryReasons.length)];
}

// 生成1990到1995年之间的随机日期，并添加性别
function generateRandomBirthday(gender) {
    let minYear = 1990;
    let maxYear = 1995;

    // 随机选择一个出生年份
    let year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;

    // 随机选择生日月份和日期
    let month = Math.floor(Math.random() * 12) + 1; // 1-12月
    let day = Math.floor(Math.random() * 28) + 1;   // 1-28日

    // 返回格式化的日期和性别：YYYY-MM-DD 性别
    return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')} ${gender === 'male' ? '男' : '女'}`;
}

// 在指定文件夹中创建书签的函数
function createBookmarkInFolder(folderId, title, url, isNameBookmark, email = null, password = null) {
    let htmlContent;
    if (isNameBookmark) {
        // 如果是姓名书签，分成两行显示
        const firstName = title.split(' ')[0];
        const lastName = title.split(' ')[1];

        htmlContent = `
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${firstName}</h1>
                    <h1>${lastName}</h1>
                    <button onclick="copyToClipboard('${firstName}')">复制名字</button>
                    <button onclick="copyToClipboard('${lastName}')">复制姓氏</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    console.log('已复制到剪贴板');
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                console.log('已复制到剪贴板');
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            // 添加到页面
                            document.body.appendChild(notification);

                            // 设置样式
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            // 显示通知
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            // 隐藏通知
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;
    } else if (email && password) {
        // 如果是合并的邮箱和密码书签
        htmlContent = `
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${title}</h1>
                    <p>邮箱: ${email}</p>
                    <p>密码: ${password}</p>
                    <button onclick="copyToClipboard('${email}')">复制邮箱</button>
                    <button onclick="copyToClipboard('${password}')">复制密码</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    console.log('已复制到剪贴板');
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                console.log('已复制到剪贴板');
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            // 添加到页面
                            document.body.appendChild(notification);

                            // 设置样式
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            // 显示通知
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            // 隐藏通知
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;
    } else {
        // 其他书签，显示标题和 URL
        let displayTitle = title;
        let copyText = title;

        if (title.includes('男') || title.includes('女')) {
            // 如果标题包含"男"或"女"，假设是生日和性别格式
            const [date, gender] = title.split(' ');
            const [year, month, day] = date.split('-');
            const genderCode = gender === '男' ? '2006' : '2007';
            copyText = `${day}${month}${year}`;
        }

        htmlContent = `
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${displayTitle}</h1>
                    <button onclick="copyToClipboard('${copyText}')">复制</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    console.log('标题已复制到剪贴板');
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                console.log('已复制到剪贴板');
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            // 添加到页面
                            document.body.appendChild(notification);

                            // 设置样式
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            // 显示通知
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            // 隐藏通知
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;
    }

    chrome.bookmarks.create({
        'parentId': folderId,  // 使用文件夹 ID
        'title': title,
        'url': `data:text/html;charset=UTF-8,${encodeURIComponent(htmlContent)}`
    });
}