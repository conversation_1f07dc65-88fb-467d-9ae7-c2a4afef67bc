// Popup脚本 - 管理用户界面
class PopupManager {
    constructor() {
        this.elements = {
            loading: document.getElementById('loading'),
            content: document.getElementById('content'),
            statusText: document.getElementById('statusText'),
            statusDot: document.getElementById('statusDot'),
            toggleBtn: document.getElementById('toggleBtn')
        };
        
        this.init();
    }

    async init() {
        try {
            // 获取当前状态
            const response = await this.sendMessage({ action: 'getStatus' });
            this.updateUI(response.enabled);
            
            // 绑定事件
            this.elements.toggleBtn.addEventListener('click', () => this.toggle());
            
            // 监听状态变化
            chrome.runtime.onMessage.addListener((message) => {
                if (message.action === 'statusChanged') {
                    this.updateUI(message.enabled);
                }
            });
            
            // 隐藏加载界面
            this.elements.loading.style.display = 'none';
            this.elements.content.style.display = 'block';
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }

    async toggle() {
        try {
            this.elements.toggleBtn.disabled = true;
            this.elements.toggleBtn.textContent = '切换中...';
            
            const response = await this.sendMessage({ action: 'toggle' });
            this.updateUI(response.enabled);
            
        } catch (error) {
            console.error('切换状态失败:', error);
            this.showError('操作失败，请重试');
        } finally {
            this.elements.toggleBtn.disabled = false;
        }
    }

    updateUI(enabled) {
        // 更新状态文本和指示器
        if (enabled) {
            this.elements.statusText.textContent = 'UA头移除已启用';
            this.elements.statusDot.className = 'status-dot enabled';
            this.elements.toggleBtn.textContent = '禁用';
            this.elements.toggleBtn.className = 'toggle-btn enabled';
        } else {
            this.elements.statusText.textContent = 'UA头移除已禁用';
            this.elements.statusDot.className = 'status-dot disabled';
            this.elements.toggleBtn.textContent = '启用';
            this.elements.toggleBtn.className = 'toggle-btn disabled';
        }
    }

    showError(message) {
        this.elements.statusText.textContent = message;
        this.elements.statusDot.className = 'status-dot disabled';
        this.elements.toggleBtn.textContent = '重试';
        this.elements.toggleBtn.className = 'toggle-btn disabled';
    }

    sendMessage(message) {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(message, (response) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(response);
                }
            });
        });
    }
}

// 当DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
