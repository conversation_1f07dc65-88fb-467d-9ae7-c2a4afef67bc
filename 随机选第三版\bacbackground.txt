// 内存中存储计时数据
let timers = {};
let activeSurveyId = null;

// 初始化：从 local 存储加载数据
chrome.storage.local.get(['timers', 'activeSurveyId'], (data) => {
    timers = data.timers || {};
    activeSurveyId = data.activeSurveyId || null;
    console.log('后台脚本初始化，加载计时数据:', timers, '活跃 surveyId:', activeSurveyId);
});

// 广播消息到所有标签页
function broadcastToAllTabs(message) {
    chrome.tabs.query({}, (tabs) => {
        tabs.forEach((tab) => {
            chrome.tabs.sendMessage(tab.id, message, (response) => {
                if (chrome.runtime.lastError) {
                    console.log(`向标签页 ${tab.id} 发送消息失败:`, chrome.runtime.lastError.message);
                }
            });
        });
    });
}

// WebSocket 连接管理
let ws = null;
const WS_PORT = 9876;  // 修改端口号
let reconnectTimeout = null;
let isConnecting = false;

async function connectWebSocket() {
    if (isConnecting) {
        console.log('WebSocket 正在连接中...');
        return;
    }

    try {
        isConnecting = true;
        console.log('尝试连接 WebSocket...');
        
        if (ws) {
            console.log('关闭现有连接...');
            ws.close();
            ws = null;
        }

        ws = new WebSocket(`ws://localhost:${WS_PORT}`);

        ws.onopen = () => {
            console.log('WebSocket 已连接');
            clearTimeout(reconnectTimeout);
            isConnecting = false;
        };

        ws.onmessage = async (event) => {
            try {
                console.log('收到消息:', event.data);
                const command = JSON.parse(event.data);
                const tabs = await chrome.tabs.query({});
                console.log('发送消息到', tabs.length, '个标签页');
                
                tabs.forEach(tab => {
                    chrome.tabs.sendMessage(tab.id, command).catch(err => 
                        console.log(`发送到标签页 ${tab.id} 失败:`, err)
                    );
                });
            } catch (error) {
                console.error('处理 WebSocket 消息时出错:', error);
            }
        };

        ws.onclose = (event) => {
            console.log('WebSocket 连接已关闭:', event.code, event.reason);
            ws = null;
            isConnecting = false;
            // 使用指数退避重连
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
            console.log(`将在 ${delay}ms 后重试连接...`);
            reconnectTimeout = setTimeout(() => {
                reconnectAttempts++;
                connectWebSocket();
            }, delay);
        };

        ws.onerror = (error) => {
            console.error('WebSocket 错误:', error);
            isConnecting = false;
        };
    } catch (error) {
        console.error('创建 WebSocket 连接时出错:', error);
        isConnecting = false;
        // 出错时也使用指数退避重连
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
        console.log(`将在 ${delay}ms 后重试连接...`);
        reconnectTimeout = setTimeout(() => {
            reconnectAttempts++;
            connectWebSocket();
        }, delay);
    }
}

// 初始化连接
let reconnectAttempts = 0;
connectWebSocket();

// 定期检查连接状态
setInterval(() => {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
        console.log('检测到连接断开，尝试重新连接...');
        connectWebSocket();
    }
}, 5000);

// 更新计时器
function updateTimers() {
    const currentTime = Date.now();
    if (activeSurveyId && timers[activeSurveyId]) {
        const elapsedTime = currentTime - timers[activeSurveyId].startTime + timers[activeSurveyId].pausedTime;
        if (elapsedTime >= 60 * 60 * 1000) { // 超过60分钟重置
            timers[activeSurveyId] = { startTime: currentTime, pausedTime: 0 };
            console.log(`计时超过60分钟，已重置: ${activeSurveyId}`);
        }
        broadcastToAllTabs({
            action: 'timerUpdate',
            surveyId: activeSurveyId,
            elapsedTime: elapsedTime
        });
    }
    chrome.storage.local.set({ timers, activeSurveyId });
}

// 每秒更新计时器
setInterval(updateTimers, 1000);

// 处理消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('收到消息:', message);
    const { action, surveyId } = message;

    switch (action) {
        case 'getTimer':
            if (!timers[surveyId]) {
                timers[surveyId] = { startTime: Date.now(), pausedTime: 0 };
            }
            activeSurveyId = surveyId.toString();
            const elapsedTime = Date.now() - timers[surveyId].startTime + timers[surveyId].pausedTime;
            chrome.storage.local.set({ timers, activeSurveyId });
            sendResponse({ surveyId: activeSurveyId, elapsedTime });
            break;

        case 'resetTimer':
            timers[surveyId] = { startTime: Date.now(), pausedTime: 0 };
            activeSurveyId = surveyId;
            chrome.storage.local.set({ timers, activeSurveyId });
            broadcastToAllTabs({
                action: 'timerUpdate',
                surveyId: activeSurveyId,
                elapsedTime: 0
            });
            sendResponse({ success: true });
            break;

        case 'updateSurveyId':
            activeSurveyId = surveyId;
            if (!timers[surveyId]) {
                timers[surveyId] = { startTime: Date.now(), pausedTime: 0 };
            }
            chrome.storage.local.set({ timers, activeSurveyId });
            const updatedElapsedTime = Date.now() - timers[surveyId].startTime + timers[surveyId].pausedTime;
            broadcastToAllTabs({
                action: 'timerUpdate',
                surveyId: activeSurveyId,
                elapsedTime: updatedElapsedTime
            });
            sendResponse({ surveyId: activeSurveyId, elapsedTime: updatedElapsedTime });
            break;

        default:
            console.warn('未知的消息操作:', action);
            break;
    }
    return true; // 异步响应
});