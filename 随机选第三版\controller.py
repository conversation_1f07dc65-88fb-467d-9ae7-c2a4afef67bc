import tkinter as tk
from tkinter import ttk, messagebox
import asyncio
import websockets
import json
import threading
from datetime import datetime
import sys

class PluginController:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("控制面板")
        self.root.geometry("400x600")
        self.root.resizable(False, False)

        # 设置样式
        style = ttk.Style()
        style.configure("Switch.TCheckbutton", padding=5)
        style.configure("Action.TButton", padding=10)
        style.configure("Status.TLabel", font=("Arial", 10))

        self.setup_ui()
        self.connected_clients = set()
        self.server = None
        self.server_task = None
        self.loop = None
        self.start_websocket_server()

        # 添加窗口关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding="5")
        status_frame.pack(fill=tk.X, pady=5)

        self.server_status = ttk.Label(
            status_frame, 
            text="正在启动服务器...", 
            style="Status.TLabel",
            foreground="orange"
        )
        self.server_status.pack(fill=tk.X)

        self.status_label = ttk.Label(
            status_frame, 
            text="等待连接...", 
            style="Status.TLabel",
            wraplength=350
        )
        self.status_label.pack(fill=tk.X)

        self.clients_label = ttk.Label(
            status_frame, 
            text="已连接: 0", 
            style="Status.TLabel",
            wraplength=350
        )
        self.clients_label.pack(fill=tk.X)

        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="控制", padding="5")
        control_frame.pack(fill=tk.X, pady=5)

        # 自动答题开关
        self.auto_answer_var = tk.BooleanVar(value=False)
        self.auto_answer_switch = ttk.Checkbutton(
            control_frame,
            text="自动答题",
            variable=self.auto_answer_var,
            style="Switch.TCheckbutton",
            command=self.toggle_auto_answer
        )
        self.auto_answer_switch.pack(fill=tk.X, pady=5)

    # 是否提交开关
        self.submit_toggle_var = tk.BooleanVar(value=False)
        self.submit_toggle_switch = ttk.Checkbutton(
            control_frame,
            text="是否提交",
            variable=self.submit_toggle_var,
            style="Switch.TCheckbutton"
        )
        self.submit_toggle_switch.pack(fill=tk.X, pady=5)

        # 快速提交按钮
        self.submit_button = ttk.Button(
            control_frame,
            text="⚡",
            style="Action.TButton",
            command=self.quick_submit
        )
        self.submit_button.pack(fill=tk.X, pady=5)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

    def add_log(self, message, level="info"):
        current_time = datetime.now().strftime("%H:%M:%S")
        color = {
            "info": "black",
            "warning": "#FF6B00",
            "error": "red",
            "success": "green"
        }.get(level, "black")
        
        self.log_text.tag_config(level, foreground=color)
        self.log_text.insert(tk.END, f"[{current_time}] ", level)
        self.log_text.insert(tk.END, f"{message}\n", level)
        self.log_text.see(tk.END)

        # 更新到GUI线程
        self.root.update_idletasks()

    def toggle_auto_answer(self):
        if not self.connected_clients:
            messagebox.showwarning("警告", "没有已连接的客户端！")
            self.auto_answer_var.set(not self.auto_answer_var.get())  # 恢复开关状态
            return

        state = self.auto_answer_var.get()
        message = {
            "action": "toggleAutoAnswer",
            "value": state
        }
        if self.loop:
            asyncio.run_coroutine_threadsafe(
                self.broadcast_message(json.dumps(message)), 
                self.loop
            )
        self.add_log(f"自动答题: {'开启' if state else '关闭'}", "info")

    def quick_submit(self):
        if not self.connected_clients:
            messagebox.showwarning("警告", "没有已连接的客户端！")
            return

        # 根据是否提交开关的状态发送不同的命令
        if self.submit_toggle_var.get():
            message = {
                "action": "quickSubmit",
            }
            self.add_log("quickSubmit", "info")
        else:
            message = {
                "action": "quickSelect"
            }
            self.add_log("quickSelect", "info")

        if self.loop:
            asyncio.run_coroutine_threadsafe(
                self.broadcast_message(json.dumps(message)),
                self.loop
            )

    async def websocket_handler(self, websocket):
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"

        # 检查是否已经存在相同的客户端连接
        for client in self.connected_clients:
            if client.remote_address == websocket.remote_address:
                self.add_log(f"拒绝重复连接: {client_info}", "warning")
                await websocket.close(reason="重复连接")
                return

        connection_id = id(websocket)  # 为每个连接分配唯一标识符
        self.connected_clients.add(websocket)
        self.update_client_count()
        self.add_log(f"新客户端连接: {client_info} (连接ID: {connection_id})", "success")

        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    self.add_log(f"收到消息 ({client_info}, 连接ID: {connection_id}): {data}", "info")
                except json.JSONDecodeError:
                    self.add_log(f"收到消息 ({client_info}, 连接ID: {connection_id}): {message}", "info")
        except websockets.exceptions.ConnectionClosed:
            self.add_log(f"客户端断开连接: {client_info} (连接ID: {connection_id})", "warning")
        except Exception as e:
            self.add_log(f"处理客户端消息时出错 ({client_info}, 连接ID: {connection_id}): {str(e)}", "error")
        finally:
            self.connected_clients.remove(websocket)
            self.update_client_count()

    def update_client_count(self):
        count = len(self.connected_clients)
        self.clients_label.config(text=f"已连接客户端: {count}")
        
        if count > 0:
            self.status_label.config(text="正常运行中", foreground="green")
        else:
            self.status_label.config(text="等待连接...", foreground="orange")

        # 更新到GUI线程
        self.root.update_idletasks()

    async def broadcast_message(self, message):
        if not self.connected_clients:
            self.add_log("没有连接的客户端", "warning")
            return
        
        disconnected = set()
        for client in self.connected_clients:
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected.add(client)
            except Exception as e:
                self.add_log(f"发送消息失败: {str(e)}", "error")
                disconnected.add(client)

        # 移除断开的客户端
        self.connected_clients -= disconnected
        if disconnected:
            self.update_client_count()

    async def start_server(self):
        port = 9876  # 修改端口号
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                self.server = await websockets.serve(
                    self.websocket_handler, 
                    "localhost", 
                    port,
                    ping_interval=None  # 禁用自动ping以降低负载
                )
                self.add_log(f"WebSocket 服务器启动成功: ws://localhost:{port}", "success")
                self.server_status.config(
                    text=f"服务器运行中 (ws://localhost:{port})",
                    foreground="green"
                )
                
                # 保持服务器运行
                await self.server.wait_closed()
                return
                
            except OSError as e:
                retry_count += 1
                self.add_log(f"启动服务器失败 (尝试 {retry_count}/{max_retries}): {str(e)}", "error")
                self.server_status.config(
                    text=f"启动失败 (端口 {port} 可能被占用)",
                    foreground="red"
                )
                if retry_count < max_retries:
                    await asyncio.sleep(1)
                else:
                    messagebox.showerror("错误", f"无法启动WebSocket服务器: {str(e)}\n请检查端口 {port} 是否被占用")
                    raise

    async def stop_server(self):
        if self.server:
            try:
                self.server.close()
                await self.server.wait_closed()
                # 等待一小段时间确保端口完全释放
                await asyncio.sleep(1)
                self.server = None
                self.connected_clients.clear()
                self.update_client_count()
                self.add_log("服务器已停止", "warning")
                self.server_status.config(
                    text="服务器已停止",
                    foreground="orange"
                )
            except Exception as e:
                self.add_log(f"停止服务器时出错: {str(e)}", "error")

    def start_websocket_server(self):
        async def run_server():
            while True:  # 添加循环以处理重启
                try:
                    await self.start_server()
                except Exception as e:
                    self.add_log(f"服务器异常: {str(e)}", "error")
                    # 等待一段时间后重试
                    await asyncio.sleep(2)

        def run_async_loop():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            try:
                self.server_task = self.loop.create_task(run_server())
                self.loop.run_forever()
            except Exception as e:
                self.add_log(f"服务器线程异常: {str(e)}", "error")
            finally:
                if self.loop.is_running():
                    self.loop.stop()
                self.loop.close()
                self.loop = None

        # 在新线程中启动 WebSocket 服务器
        self.server_thread = threading.Thread(target=run_async_loop, daemon=True)
        self.server_thread.start()

    def on_closing(self):
        if messagebox.askokcancel("确认", "确定要关闭控制面板吗？"):
            async def cleanup():
                if self.server:
                    await self.stop_server()
                if self.loop and self.loop.is_running():
                    self.loop.stop()

            if self.loop:
                asyncio.run_coroutine_threadsafe(cleanup(), self.loop)
                # 等待清理完成
                self.server_thread.join(timeout=3)
            
            self.root.destroy()
            sys.exit(0)

    def run(self):
        try:
            self.root.mainloop()
        except Exception as e:
            self.add_log(f"程序异常: {str(e)}", "error")
            raise

if __name__ == "__main__":
    try:
        controller = PluginController()
        controller.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")