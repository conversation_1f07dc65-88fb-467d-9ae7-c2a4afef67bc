(()=>{var e={4665:e=>{function t(e,t,r){for(var n=0;n<r.length;n++)e.setUint8(t+n,r.charCodeAt(n))}e.exports=function(e,r){r=r||{};var n=e.numberOfChannels,s=e.sampleRate,i=r.float32?3:1,o=3===i?32:16;return function(e,r,n,s,i){var o=i/8,a=s*o,u=new ArrayBuffer(44+e.length*o),c=new DataView(u);return t(c,0,"RIFF"),c.setUint32(4,36+e.length*o,!0),t(c,8,"WAVE"),t(c,12,"fmt "),c.setUint32(16,16,!0),c.setUint16(20,r,!0),c.setUint16(22,s,!0),c.setUint32(24,n,!0),c.setUint32(28,n*a,!0),c.setUint16(32,a,!0),c.setUint16(34,i,!0),t(c,36,"data"),c.setUint32(40,e.length*o,!0),1===r?function(e,t,r){for(var n=0;n<r.length;n++,t+=2){var s=Math.max(-1,Math.min(1,r[n]));e.setInt16(t,s<0?32768*s:32767*s,!0)}}(c,44,e):function(e,t,r){for(var n=0;n<r.length;n++,t+=4)e.setFloat32(t,r[n],!0)}(c,44,e),u}(2===n?function(e,t){for(var r=e.length+t.length,n=new Float32Array(r),s=0,i=0;s<r;)n[s++]=e[i],n[s++]=t[i],i++;return n}(e.getChannelData(0),e.getChannelData(1)):e.getChannelData(0),i,s,n,o)}},9033:function(e){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(n,s,function(t){return e[t]}.bind(null,s));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=r(18),s=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var s=e.getVersionPrecision(t),i=e.getVersionPrecision(r),o=Math.max(s,i),a=0,u=e.map([t,r],(function(t){var r=o-e.getVersionPrecision(t),n=t+new Array(r+1).join(".0");return e.map(n.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(n&&(a=o-Math.min(s,i)),o-=1;o>=a;){if(u[0][o]>u[1][o])return 1;if(u[0][o]===u[1][o]){if(o===a)return 0;o-=1}else if(u[0][o]<u[1][o])return-1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var s=e[r];if(t(s,r))return s}},e.assign=function(e){for(var t,r,n=e,s=arguments.length,i=new Array(s>1?s-1:0),o=1;o<s;o++)i[o-1]=arguments[o];if(Object.assign)return Object.assign.apply(Object,[e].concat(i));var a=function(){var e=i[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){n[t]=e[t]}))};for(t=0,r=i.length;t<r;t+=1)a();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=s,e.exports=t.default},18:function(e,t,r){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(91))&&n.__esModule?n:{default:n},i=r(18);function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function(){function e(){}var t,r;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new s.default(e,t)},e.parse=function(e){return new s.default(e).getResult()},t=e,r=[{key:"BROWSER_MAP",get:function(){return i.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return i.ENGINE_MAP}},{key:"OS_MAP",get:function(){return i.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return i.PLATFORMS_MAP}}],null&&o(t.prototype,null),r&&o(t,r),e}();t.default=a,e.exports=t.default},91:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=u(r(92)),s=u(r(93)),i=u(r(94)),o=u(r(95)),a=u(r(17));function u(e){return e&&e.__esModule?e:{default:e}}var c=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,s={},i=0;if(Object.keys(e).forEach((function(t){var o=e[t];"string"==typeof o?(s[t]=o,i+=1):"object"==typeof o&&(r[t]=o,n+=1)})),n>0){var o=Object.keys(r),u=a.default.find(o,(function(e){return t.isOS(e)}));if(u){var c=this.satisfies(r[u]);if(void 0!==c)return c}var l=a.default.find(o,(function(e){return t.isPlatform(e)}));if(l){var g=this.satisfies(r[l]);if(void 0!==g)return g}}if(i>0){var f=Object.keys(s),d=a.default.find(f,(function(e){return t.isBrowser(e,!0)}));if(void 0!==d)return this.compareVersion(s[d])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),s=a.default.getBrowserTypeByAlias(n);return t&&s&&(n=s.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,s=this.getBrowserVersion();if("string"==typeof s)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(a.default.compareVersions(s,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=c,e.exports=t.default},92:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=/version\/(\d+(\.?_?\d+)+)/i,o=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=s.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=s.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=s.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=s.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=s.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=s.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=s.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=s.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=s.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=s.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=s.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=s.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=s.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=s.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=s.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=s.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=s.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=s.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=s.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=s.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=s.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=s.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=s.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=s.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=s.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=s.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=s.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:s.default.getFirstMatch(t,e),version:s.default.getSecondMatch(t,e)}}}];t.default=o,e.exports=t.default},93:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:[/Roku\/DVP/],describe:function(e){var t=s.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:i.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=s.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=s.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=s.default.getWindowsVersionName(t);return{name:i.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:i.OS_MAP.iOS},r=s.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=s.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=s.default.getMacOSVersionName(t),n={name:i.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=s.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:i.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=s.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=s.default.getAndroidVersionName(t),n={name:i.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=s.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:i.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=s.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||s.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||s.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:i.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=s.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=s.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:i.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:i.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=s.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.PlayStation4,version:t}}}];t.default=o,e.exports=t.default},94:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=s.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:i.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=s.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:i.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:i.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.tv}}}];t.default=o,e.exports=t.default},95:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:i.ENGINE_MAP.Blink};var t=s.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:i.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:i.ENGINE_MAP.Trident},r=s.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:i.ENGINE_MAP.Presto},r=s.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:i.ENGINE_MAP.Gecko},r=s.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:i.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:i.ENGINE_MAP.WebKit},r=s.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=o,e.exports=t.default}})},7272:function(e,t){var r,n;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,r=function(e){"use strict";if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw new Error("This script should only be loaded in a browser extension.");if(globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id)e.exports=globalThis.browser;else{const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class n extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const s=(t,r)=>(...n)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||n.length<=1&&!1!==r.singleCallbackArg?t.resolve(n[0]):t.resolve(n)},i=e=>1==e?"argument":"arguments",o=(e,t,r)=>new Proxy(t,{apply:(t,n,s)=>r.call(n,e,...s)});let a=Function.call.bind(Object.prototype.hasOwnProperty);const u=(e,t={},r={})=>{let n=Object.create(null),c={has:(t,r)=>r in e||r in n,get(c,l,g){if(l in n)return n[l];if(!(l in e))return;let f=e[l];if("function"==typeof f)if("function"==typeof t[l])f=o(e,e[l],t[l]);else if(a(r,l)){let t=((e,t)=>function(r,...n){if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${i(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${i(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((i,o)=>{if(t.fallbackToNoCallback)try{r[e](...n,s({resolve:i,reject:o},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...n),t.fallbackToNoCallback=!1,t.noCallback=!0,i()}else t.noCallback?(r[e](...n),i()):r[e](...n,s({resolve:i,reject:o},t))}))})(l,r[l]);f=o(e,e[l],t)}else f=f.bind(e);else if("object"==typeof f&&null!==f&&(a(t,l)||a(r,l)))f=u(f,t[l],r[l]);else{if(!a(r,"*"))return Object.defineProperty(n,l,{configurable:!0,enumerable:!0,get:()=>e[l],set(t){e[l]=t}}),f;f=u(f,t[l],r["*"])}return n[l]=f,f},set:(t,r,s,i)=>(r in n?n[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(n,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(n,t)},l=Object.create(e);return new Proxy(l,c)},c=e=>({addListener(t,r,...n){t.addListener(e.get(r),...n)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),l=new n((e=>"function"!=typeof e?e:function(t){const r=u(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),g=new n((e=>"function"!=typeof e?e:function(t,r,n){let s,i,o=!1,a=new Promise((e=>{s=function(t){o=!0,e(t)}}));try{i=e(t,r,s)}catch(e){i=Promise.reject(e)}const u=!0!==i&&((c=i)&&"object"==typeof c&&"function"==typeof c.then);var c;if(!0!==i&&!u&&!o)return!1;return(u?i:a).then((e=>{n(e)}),(e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",n({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error("Failed to send onMessage rejected reply",e)})),!0})),f=({reject:r,resolve:n},s)=>{e.runtime.lastError?e.runtime.lastError.message===t?n():r(new Error(e.runtime.lastError.message)):s&&s.__mozWebExtensionPolyfillReject__?r(new Error(s.message)):n(s)},d=(e,t,r,...n)=>{if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${i(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${i(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((e,t)=>{const s=f.bind(null,{resolve:e,reject:t});n.push(s),r.sendMessage(...n)}))},m={devtools:{network:{onRequestFinished:c(l)}},runtime:{onMessage:c(g),onMessageExternal:c(g),sendMessage:d.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:d.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},p={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":p},services:{"*":p},websites:{"*":p}},u(e,m,r)};e.exports=r(chrome)}},void 0===(n=r.apply(t,[e]))||(e.exports=n)},2365:(e,t,r)=>{"use strict";var n=r(9200),s=r(7938),i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(s(e)+" is not a function")}},9677:(e,t,r)=>{"use strict";var n=r(100),s=String,i=TypeError;e.exports=function(e){if(n(e))return e;throw new i("Can't set "+s(e)+" as a prototype")}},602:(e,t,r)=>{"use strict";var n=r(2430),s=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new s("Incorrect invocation")}},4398:(e,t,r)=>{"use strict";var n=r(9131),s=String,i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(s(e)+" is not an object")}},6134:(e,t,r)=>{"use strict";var n=r(4360),s=r(8479),i=r(7457),o=function(e){return function(t,r,o){var a=n(t),u=i(a);if(0===u)return!e&&-1;var c,l=s(o,u);if(e&&r!=r){for(;u>l;)if((c=a[l++])!=c)return!0}else for(;u>l;l++)if((e||l in a)&&a[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:o(!0),indexOf:o(!1)}},5589:(e,t,r)=>{"use strict";var n=r(7133),s=n({}.toString),i=n("".slice);e.exports=function(e){return i(s(e),8,-1)}},3650:(e,t,r)=>{"use strict";var n=r(917),s=r(9200),i=r(5589),o=r(4702)("toStringTag"),a=Object,u="Arguments"===i(function(){return arguments}());e.exports=n?i:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=a(e),o))?r:u?i(t):"Object"===(n=i(t))&&s(t.callee)?"Arguments":n}},4085:(e,t,r)=>{"use strict";var n=r(9158),s=r(4540),i=r(2428),o=r(4446);e.exports=function(e,t,r){for(var a=s(t),u=o.f,c=i.f,l=0;l<a.length;l++){var g=a[l];n(e,g)||r&&n(r,g)||u(e,g,c(t,g))}}},5044:(e,t,r)=>{"use strict";var n=r(6857),s=r(4446),i=r(2007);e.exports=n?function(e,t,r){return s.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},2007:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},8521:(e,t,r)=>{"use strict";var n=r(9200),s=r(4446),i=r(4174),o=r(8466);e.exports=function(e,t,r,a){a||(a={});var u=a.enumerable,c=void 0!==a.name?a.name:t;if(n(r)&&i(r,c,a),a.global)u?e[t]=r:o(t,r);else{try{a.unsafe?e[t]&&(u=!0):delete e[t]}catch(e){}u?e[t]=r:s.f(e,t,{value:r,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},8466:(e,t,r)=>{"use strict";var n=r(7732),s=Object.defineProperty;e.exports=function(e,t){try{s(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},6857:(e,t,r)=>{"use strict";var n=r(942);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},1466:(e,t,r)=>{"use strict";var n=r(7732),s=r(9131),i=n.document,o=s(i)&&s(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},4131:e=>{"use strict";e.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},1681:e=>{"use strict";e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},4017:(e,t,r)=>{"use strict";var n,s,i=r(7732),o=r(1681),a=i.process,u=i.Deno,c=a&&a.versions||u&&u.version,l=c&&c.v8;l&&(s=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!s&&o&&(!(n=o.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=o.match(/Chrome\/(\d+)/))&&(s=+n[1]),e.exports=s},2030:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},5824:(e,t,r)=>{"use strict";var n=r(7133),s=Error,i=n("".replace),o=String(new s("zxcasd").stack),a=/\n\s*at [^:]*:[^\n]*/,u=a.test(o);e.exports=function(e,t){if(u&&"string"==typeof e&&!s.prepareStackTrace)for(;t--;)e=i(e,a,"");return e}},3353:(e,t,r)=>{"use strict";var n=r(7732),s=r(2428).f,i=r(5044),o=r(8521),a=r(8466),u=r(4085),c=r(7453);e.exports=function(e,t){var r,l,g,f,d,m=e.target,p=e.global,A=e.stat;if(r=p?n:A?n[m]||a(m,{}):n[m]&&n[m].prototype)for(l in t){if(f=t[l],g=e.dontCallGetSet?(d=s(r,l))&&d.value:r[l],!c(p?l:m+(A?".":"#")+l,e.forced)&&void 0!==g){if(typeof f==typeof g)continue;u(f,g)}(e.sham||g&&g.sham)&&i(f,"sham",!0),o(r,l,f,e)}}},942:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},7315:(e,t,r)=>{"use strict";var n=r(942);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},492:(e,t,r)=>{"use strict";var n=r(7315),s=Function.prototype.call;e.exports=n?s.bind(s):function(){return s.apply(s,arguments)}},7403:(e,t,r)=>{"use strict";var n=r(6857),s=r(9158),i=Function.prototype,o=n&&Object.getOwnPropertyDescriptor,a=s(i,"name"),u=a&&"something"===function(){}.name,c=a&&(!n||n&&o(i,"name").configurable);e.exports={EXISTS:a,PROPER:u,CONFIGURABLE:c}},9229:(e,t,r)=>{"use strict";var n=r(7133),s=r(2365);e.exports=function(e,t,r){try{return n(s(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},7133:(e,t,r)=>{"use strict";var n=r(7315),s=Function.prototype,i=s.call,o=n&&s.bind.bind(i,i);e.exports=n?o:function(e){return function(){return i.apply(e,arguments)}}},848:(e,t,r)=>{"use strict";var n=r(7732),s=r(9200);e.exports=function(e,t){return arguments.length<2?(r=n[e],s(r)?r:void 0):n[e]&&n[e][t];var r}},9325:(e,t,r)=>{"use strict";var n=r(2365),s=r(2178);e.exports=function(e,t){var r=e[t];return s(r)?void 0:n(r)}},7732:function(e,t,r){"use strict";var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9158:(e,t,r)=>{"use strict";var n=r(7133),s=r(9272),i=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(s(e),t)}},640:e=>{"use strict";e.exports={}},5842:(e,t,r)=>{"use strict";var n=r(6857),s=r(942),i=r(1466);e.exports=!n&&!s((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},8060:(e,t,r)=>{"use strict";var n=r(7133),s=r(942),i=r(5589),o=Object,a=n("".split);e.exports=s((function(){return!o("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?a(e,""):o(e)}:o},2210:(e,t,r)=>{"use strict";var n=r(9200),s=r(9131),i=r(8018);e.exports=function(e,t,r){var o,a;return i&&n(o=t.constructor)&&o!==r&&s(a=o.prototype)&&a!==r.prototype&&i(e,a),e}},7217:(e,t,r)=>{"use strict";var n=r(7133),s=r(9200),i=r(5210),o=n(Function.toString);s(i.inspectSource)||(i.inspectSource=function(e){return o(e)}),e.exports=i.inspectSource},1514:(e,t,r)=>{"use strict";var n,s,i,o=r(3125),a=r(7732),u=r(9131),c=r(5044),l=r(9158),g=r(5210),f=r(2316),d=r(640),m="Object already initialized",p=a.TypeError,A=a.WeakMap;if(o||g.state){var v=g.state||(g.state=new A);v.get=v.get,v.has=v.has,v.set=v.set,n=function(e,t){if(v.has(e))throw new p(m);return t.facade=e,v.set(e,t),t},s=function(e){return v.get(e)||{}},i=function(e){return v.has(e)}}else{var h=f("state");d[h]=!0,n=function(e,t){if(l(e,h))throw new p(m);return t.facade=e,c(e,h,t),t},s=function(e){return l(e,h)?e[h]:{}},i=function(e){return l(e,h)}}e.exports={set:n,get:s,has:i,enforce:function(e){return i(e)?s(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!u(t)||(r=s(t)).type!==e)throw new p("Incompatible receiver, "+e+" required");return r}}}},9200:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},7453:(e,t,r)=>{"use strict";var n=r(942),s=r(9200),i=/#|\.prototype\./,o=function(e,t){var r=u[a(e)];return r===l||r!==c&&(s(t)?n(t):!!t)},a=o.normalize=function(e){return String(e).replace(i,".").toLowerCase()},u=o.data={},c=o.NATIVE="N",l=o.POLYFILL="P";e.exports=o},2178:e=>{"use strict";e.exports=function(e){return null==e}},9131:(e,t,r)=>{"use strict";var n=r(9200);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},100:(e,t,r)=>{"use strict";var n=r(9131);e.exports=function(e){return n(e)||null===e}},1818:e=>{"use strict";e.exports=!1},460:(e,t,r)=>{"use strict";var n=r(848),s=r(9200),i=r(2430),o=r(6253),a=Object;e.exports=o?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return s(t)&&i(t.prototype,a(e))}},7457:(e,t,r)=>{"use strict";var n=r(2695);e.exports=function(e){return n(e.length)}},4174:(e,t,r)=>{"use strict";var n=r(7133),s=r(942),i=r(9200),o=r(9158),a=r(6857),u=r(7403).CONFIGURABLE,c=r(7217),l=r(1514),g=l.enforce,f=l.get,d=String,m=Object.defineProperty,p=n("".slice),A=n("".replace),v=n([].join),h=a&&!s((function(){return 8!==m((function(){}),"length",{value:8}).length})),b=String(String).split("String"),x=e.exports=function(e,t,r){"Symbol("===p(d(t),0,7)&&(t="["+A(d(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!o(e,"name")||u&&e.name!==t)&&(a?m(e,"name",{value:t,configurable:!0}):e.name=t),h&&r&&o(r,"arity")&&e.length!==r.arity&&m(e,"length",{value:r.arity});try{r&&o(r,"constructor")&&r.constructor?a&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=g(e);return o(n,"source")||(n.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=x((function(){return i(this)&&f(this).source||c(this)}),"toString")},8226:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},5334:(e,t,r)=>{"use strict";var n=r(7830);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:n(e)}},4446:(e,t,r)=>{"use strict";var n=r(6857),s=r(5842),i=r(335),o=r(4398),a=r(2548),u=TypeError,c=Object.defineProperty,l=Object.getOwnPropertyDescriptor,g="enumerable",f="configurable",d="writable";t.f=n?i?function(e,t,r){if(o(e),t=a(t),o(r),"function"==typeof e&&"prototype"===t&&"value"in r&&d in r&&!r[d]){var n=l(e,t);n&&n[d]&&(e[t]=r.value,r={configurable:f in r?r[f]:n[f],enumerable:g in r?r[g]:n[g],writable:!1})}return c(e,t,r)}:c:function(e,t,r){if(o(e),t=a(t),o(r),s)try{return c(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},2428:(e,t,r)=>{"use strict";var n=r(6857),s=r(492),i=r(6732),o=r(2007),a=r(4360),u=r(2548),c=r(9158),l=r(5842),g=Object.getOwnPropertyDescriptor;t.f=n?g:function(e,t){if(e=a(e),t=u(t),l)try{return g(e,t)}catch(e){}if(c(e,t))return o(!s(i.f,e,t),e[t])}},5809:(e,t,r)=>{"use strict";var n=r(8959),s=r(2030).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,s)}},1264:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},2430:(e,t,r)=>{"use strict";var n=r(7133);e.exports=n({}.isPrototypeOf)},8959:(e,t,r)=>{"use strict";var n=r(7133),s=r(9158),i=r(4360),o=r(6134).indexOf,a=r(640),u=n([].push);e.exports=function(e,t){var r,n=i(e),c=0,l=[];for(r in n)!s(a,r)&&s(n,r)&&u(l,r);for(;t.length>c;)s(n,r=t[c++])&&(~o(l,r)||u(l,r));return l}},6732:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!r.call({1:2},1);t.f=s?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},8018:(e,t,r)=>{"use strict";var n=r(9229),s=r(9131),i=r(3977),o=r(9677);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=n(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return i(r),o(n),s(r)?(t?e(r,n):r.__proto__=n,r):r}}():void 0)},1427:(e,t,r)=>{"use strict";var n=r(492),s=r(9200),i=r(9131),o=TypeError;e.exports=function(e,t){var r,a;if("string"===t&&s(r=e.toString)&&!i(a=n(r,e)))return a;if(s(r=e.valueOf)&&!i(a=n(r,e)))return a;if("string"!==t&&s(r=e.toString)&&!i(a=n(r,e)))return a;throw new o("Can't convert object to primitive value")}},4540:(e,t,r)=>{"use strict";var n=r(848),s=r(7133),i=r(5809),o=r(1264),a=r(4398),u=s([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=i.f(a(e)),r=o.f;return r?u(t,r(e)):t}},3977:(e,t,r)=>{"use strict";var n=r(2178),s=TypeError;e.exports=function(e){if(n(e))throw new s("Can't call method on "+e);return e}},2316:(e,t,r)=>{"use strict";var n=r(6014),s=r(685),i=n("keys");e.exports=function(e){return i[e]||(i[e]=s(e))}},5210:(e,t,r)=>{"use strict";var n=r(1818),s=r(7732),i=r(8466),o="__core-js_shared__",a=e.exports=s[o]||i(o,{});(a.versions||(a.versions=[])).push({version:"3.37.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"})},6014:(e,t,r)=>{"use strict";var n=r(5210);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},260:(e,t,r)=>{"use strict";var n=r(4017),s=r(942),i=r(7732).String;e.exports=!!Object.getOwnPropertySymbols&&!s((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8479:(e,t,r)=>{"use strict";var n=r(4932),s=Math.max,i=Math.min;e.exports=function(e,t){var r=n(e);return r<0?s(r+t,0):i(r,t)}},4360:(e,t,r)=>{"use strict";var n=r(8060),s=r(3977);e.exports=function(e){return n(s(e))}},4932:(e,t,r)=>{"use strict";var n=r(8226);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},2695:(e,t,r)=>{"use strict";var n=r(4932),s=Math.min;e.exports=function(e){var t=n(e);return t>0?s(t,9007199254740991):0}},9272:(e,t,r)=>{"use strict";var n=r(3977),s=Object;e.exports=function(e){return s(n(e))}},9422:(e,t,r)=>{"use strict";var n=r(492),s=r(9131),i=r(460),o=r(9325),a=r(1427),u=r(4702),c=TypeError,l=u("toPrimitive");e.exports=function(e,t){if(!s(e)||i(e))return e;var r,u=o(e,l);if(u){if(void 0===t&&(t="default"),r=n(u,e,t),!s(r)||i(r))return r;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},2548:(e,t,r)=>{"use strict";var n=r(9422),s=r(460);e.exports=function(e){var t=n(e,"string");return s(t)?t:t+""}},917:(e,t,r)=>{"use strict";var n={};n[r(4702)("toStringTag")]="z",e.exports="[object z]"===String(n)},7830:(e,t,r)=>{"use strict";var n=r(3650),s=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return s(e)}},7938:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},685:(e,t,r)=>{"use strict";var n=r(7133),s=0,i=Math.random(),o=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+o(++s+i,36)}},6253:(e,t,r)=>{"use strict";var n=r(260);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},335:(e,t,r)=>{"use strict";var n=r(6857),s=r(942);e.exports=n&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},3125:(e,t,r)=>{"use strict";var n=r(7732),s=r(9200),i=n.WeakMap;e.exports=s(i)&&/native code/.test(String(i))},4702:(e,t,r)=>{"use strict";var n=r(7732),s=r(6014),i=r(9158),o=r(685),a=r(260),u=r(6253),c=n.Symbol,l=s("wks"),g=u?c.for||c:c&&c.withoutSetter||o;e.exports=function(e){return i(l,e)||(l[e]=a&&i(c,e)?c[e]:g("Symbol."+e)),l[e]}},9362:(e,t,r)=>{"use strict";var n=r(3353),s=r(7732),i=r(848),o=r(2007),a=r(4446).f,u=r(9158),c=r(602),l=r(2210),g=r(5334),f=r(4131),d=r(5824),m=r(6857),p=r(1818),A="DOMException",v=i("Error"),h=i(A),b=function(){c(this,x);var e=arguments.length,t=g(e<1?void 0:arguments[0]),r=g(e<2?void 0:arguments[1],"Error"),n=new h(t,r),s=new v(t);return s.name=A,a(n,"stack",o(1,d(s.stack,1))),l(n,this,b),n},x=b.prototype=h.prototype,y="stack"in new v(A),w="stack"in new h(1,2),M=h&&m&&Object.getOwnPropertyDescriptor(s,A),S=!(!M||M.writable&&M.configurable),_=y&&!S&&!w;n({global:!0,constructor:!0,forced:p||_},{DOMException:_?b:h});var O=i(A),E=O.prototype;if(E.constructor!==O)for(var P in p||a(E,"constructor",o(1,O)),f)if(u(f,P)){var R=f[P],F=R.s;u(O,F)||a(O,F,o(6,R.c))}}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";r(9362),r(9033);var e=r(4665),t=r.n(e);function n(e){let t="";const r=new Uint8Array(e),n=r.byteLength;for(var s=0;s<n;s++)t+=String.fromCharCode(r[s]);return self.btoa(t)}async function s(e,r){const s=function(e){const t=self.atob(e),r=t.length,n=new Uint8Array(new ArrayBuffer(r));for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return n.buffer}(e),i=await async function(e,{trimStart:r=0,trimEnd:n=0}={}){const s=await async function(e){const t=new AudioContext,r=await t.decodeAudioData(e);t.close();const n=new OfflineAudioContext(1,16e3*r.duration,16e3),s=n.createBufferSource();return s.connect(n.destination),s.buffer=r,s.start(),n.startRendering()}(e),i=await async function({audioBuffer:e,start:t,end:r}){const n=e.sampleRate,s=e.numberOfChannels,i=n*t,o=n*r-i,a=new AudioContext,u=a.createBuffer(s,o,n);a.close();const c=new Float32Array(o);for(var l=0;l<s;l++)e.copyFromChannel(c,l,i),u.copyToChannel(c,l,0);return u}({audioBuffer:s,start:r,end:s.duration-n});return t()(i)}(s,r);o.postMessage({audioString:n(i)})}function i(e){"processAudio"===e.id&&s(e.audioString,e.audioOptions)}let o;r(7272),r(7272),r(7272).runtime.onConnect.addListener((function(e){"offscreen"===e.name&&(o=e,o.onMessage.addListener(i))}))})()})();