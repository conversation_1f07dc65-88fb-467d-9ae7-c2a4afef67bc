(()=>{var e,t,r={4665:e=>{function t(e,t,r){for(var n=0;n<r.length;n++)e.setUint8(t+n,r.charCodeAt(n))}e.exports=function(e,r){r=r||{};var n=e.numberOfChannels,i=e.sampleRate,s=r.float32?3:1,a=3===s?32:16;return function(e,r,n,i,s){var a=s/8,o=i*a,c=new ArrayBuffer(44+e.length*a),u=new DataView(c);return t(u,0,"RIFF"),u.setUint32(4,36+e.length*a,!0),t(u,8,"WAVE"),t(u,12,"fmt "),u.setUint32(16,16,!0),u.setUint16(20,r,!0),u.setUint16(22,i,!0),u.setUint32(24,n,!0),u.setUint32(28,n*o,!0),u.setUint16(32,o,!0),u.setUint16(34,s,!0),t(u,36,"data"),u.setUint32(40,e.length*a,!0),1===r?function(e,t,r){for(var n=0;n<r.length;n++,t+=2){var i=Math.max(-1,Math.min(1,r[n]));e.setInt16(t,i<0?32768*i:32767*i,!0)}}(u,44,e):function(e,t,r){for(var n=0;n<r.length;n++,t+=4)e.setFloat32(t,r[n],!0)}(u,44,e),c}(2===n?function(e,t){for(var r=e.length+t.length,n=new Float32Array(r),i=0,s=0;i<r;)n[i++]=e[s],n[i++]=t[s],s++;return n}(e.getChannelData(0),e.getChannelData(1)):e.getChannelData(0),s,i,n,a)}},9033:function(e){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=r(18),i=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var i=e.getVersionPrecision(t),s=e.getVersionPrecision(r),a=Math.max(i,s),o=0,c=e.map([t,r],(function(t){var r=a-e.getVersionPrecision(t),n=t+new Array(r+1).join(".0");return e.map(n.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(n&&(o=a-Math.min(i,s)),a-=1;a>=o;){if(c[0][a]>c[1][a])return 1;if(c[0][a]===c[1][a]){if(a===o)return 0;a-=1}else if(c[0][a]<c[1][a])return-1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var i=e[r];if(t(i,r))return i}},e.assign=function(e){for(var t,r,n=e,i=arguments.length,s=new Array(i>1?i-1:0),a=1;a<i;a++)s[a-1]=arguments[a];if(Object.assign)return Object.assign.apply(Object,[e].concat(s));var o=function(){var e=s[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){n[t]=e[t]}))};for(t=0,r=s.length;t<r;t+=1)o();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=i,e.exports=t.default},18:function(e,t,r){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(91))&&n.__esModule?n:{default:n},s=r(18);function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var o=function(){function e(){}var t,r;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new i.default(e,t)},e.parse=function(e){return new i.default(e).getResult()},t=e,r=[{key:"BROWSER_MAP",get:function(){return s.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return s.ENGINE_MAP}},{key:"OS_MAP",get:function(){return s.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return s.PLATFORMS_MAP}}],null&&a(t.prototype,null),r&&a(t,r),e}();t.default=o,e.exports=t.default},91:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=c(r(92)),i=c(r(93)),s=c(r(94)),a=c(r(95)),o=c(r(17));function c(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=o.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=o.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=o.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=o.default.find(a.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return o.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,i={},s=0;if(Object.keys(e).forEach((function(t){var a=e[t];"string"==typeof a?(i[t]=a,s+=1):"object"==typeof a&&(r[t]=a,n+=1)})),n>0){var a=Object.keys(r),c=o.default.find(a,(function(e){return t.isOS(e)}));if(c){var u=this.satisfies(r[c]);if(void 0!==u)return u}var l=o.default.find(a,(function(e){return t.isPlatform(e)}));if(l){var f=this.satisfies(r[l]);if(void 0!==f)return f}}if(s>0){var d=Object.keys(i),g=o.default.find(d,(function(e){return t.isBrowser(e,!0)}));if(void 0!==g)return this.compareVersion(i[g])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=o.default.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(o.default.compareVersions(i,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=u,e.exports=t.default},92:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=/version\/(\d+(\.?_?\d+)+)/i,a=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=i.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=i.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=i.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=i.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=i.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=i.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=i.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=i.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=i.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=i.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=i.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=i.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=i.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=i.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=i.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=i.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=i.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=i.default.getFirstMatch(s,e)||i.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=i.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=i.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=i.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=i.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=i.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=i.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=i.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=i.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=i.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=i.default.getFirstMatch(s,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:i.default.getFirstMatch(t,e),version:i.default.getSecondMatch(t,e)}}}];t.default=a,e.exports=t.default},93:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=r(18),a=[{test:[/Roku\/DVP/],describe:function(e){var t=i.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:s.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=i.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=i.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=i.default.getWindowsVersionName(t);return{name:s.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:s.OS_MAP.iOS},r=i.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=i.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=i.default.getMacOSVersionName(t),n={name:s.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=i.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:s.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=i.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=i.default.getAndroidVersionName(t),n={name:s.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=i.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:s.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=i.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||i.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||i.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:s.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=i.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=i.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:s.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:s.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=i.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:s.OS_MAP.PlayStation4,version:t}}}];t.default=a,e.exports=t.default},94:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=r(18),a=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=i.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:s.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:s.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=i.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:s.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:s.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:s.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:s.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:s.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:s.PLATFORMS_MAP.tv}}}];t.default=a,e.exports=t.default},95:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,i=(n=r(17))&&n.__esModule?n:{default:n},s=r(18),a=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:s.ENGINE_MAP.Blink};var t=i.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:s.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:s.ENGINE_MAP.Trident},r=i.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:s.ENGINE_MAP.Presto},r=i.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:s.ENGINE_MAP.Gecko},r=i.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:s.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:s.ENGINE_MAP.WebKit},r=i.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=a,e.exports=t.default}})},6984:function(e,t,r){var n;e.exports=(n=r(7548),r(1359),r(6475),r(391),r(6826),function(){var e=n,t=e.lib.BlockCipher,r=e.algo,i=[],s=[],a=[],o=[],c=[],u=[],l=[],f=[],d=[],g=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,n=0;for(t=0;t<256;t++){var m=n^n<<1^n<<2^n<<3^n<<4;m=m>>>8^255&m^99,i[r]=m,s[m]=r;var p=e[r],h=e[p],v=e[h],A=257*e[m]^16843008*m;a[r]=A<<24|A>>>8,o[r]=A<<16|A>>>16,c[r]=A<<8|A>>>24,u[r]=A,A=16843009*v^65537*h^257*p^16843008*r,l[m]=A<<24|A>>>8,f[m]=A<<16|A>>>16,d[m]=A<<8|A>>>24,g[m]=A,r?(r=p^e[e[e[v^p]]],n^=e[e[n]]):r=n=1}}();var m=[0,1,2,4,8,16,32,64,128,27,54],p=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,n=4*((this._nRounds=r+6)+1),s=this._keySchedule=[],a=0;a<n;a++)a<r?s[a]=t[a]:(u=s[a-1],a%r?r>6&&a%r==4&&(u=i[u>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u]):(u=i[(u=u<<8|u>>>24)>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u],u^=m[a/r|0]<<24),s[a]=s[a-r]^u);for(var o=this._invKeySchedule=[],c=0;c<n;c++){if(a=n-c,c%4)var u=s[a];else u=s[a-4];o[c]=c<4||a<=4?u:l[i[u>>>24]]^f[i[u>>>16&255]]^d[i[u>>>8&255]]^g[i[255&u]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,o,c,u,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,f,d,g,s),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,n,i,s,a,o){for(var c=this._nRounds,u=e[t]^r[0],l=e[t+1]^r[1],f=e[t+2]^r[2],d=e[t+3]^r[3],g=4,m=1;m<c;m++){var p=n[u>>>24]^i[l>>>16&255]^s[f>>>8&255]^a[255&d]^r[g++],h=n[l>>>24]^i[f>>>16&255]^s[d>>>8&255]^a[255&u]^r[g++],v=n[f>>>24]^i[d>>>16&255]^s[u>>>8&255]^a[255&l]^r[g++],A=n[d>>>24]^i[u>>>16&255]^s[l>>>8&255]^a[255&f]^r[g++];u=p,l=h,f=v,d=A}p=(o[u>>>24]<<24|o[l>>>16&255]<<16|o[f>>>8&255]<<8|o[255&d])^r[g++],h=(o[l>>>24]<<24|o[f>>>16&255]<<16|o[d>>>8&255]<<8|o[255&u])^r[g++],v=(o[f>>>24]<<24|o[d>>>16&255]<<16|o[u>>>8&255]<<8|o[255&l])^r[g++],A=(o[d>>>24]<<24|o[u>>>16&255]<<16|o[l>>>8&255]<<8|o[255&f])^r[g++],e[t]=p,e[t+1]=h,e[t+2]=v,e[t+3]=A},keySize:8});e.AES=t._createHelper(p)}(),n.AES)},6826:function(e,t,r){var n,i,s,a,o,c,u,l,f,d,g,m,p,h,v,A,y,w,b;e.exports=(n=r(7548),r(391),void(n.lib.Cipher||(i=n,s=i.lib,a=s.Base,o=s.WordArray,c=s.BufferedBlockAlgorithm,u=i.enc,u.Utf8,l=u.Base64,f=i.algo.EvpKDF,d=s.Cipher=c.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){c.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?b:y}return function(t){return{encrypt:function(r,n,i){return e(n).encrypt(t,r,n,i)},decrypt:function(r,n,i){return e(n).decrypt(t,r,n,i)}}}}()}),s.StreamCipher=d.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),g=i.mode={},m=s.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=g.CBC=function(){var e=m.extend();function t(e,t,r){var n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(var s=0;s<r;s++)e[t+s]^=n[s]}return e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize;t.call(this,e,r,i),n.encryptBlock(e,r),this._prevBlock=e.slice(r,r+i)}}),e.Decryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,s=e.slice(r,r+i);n.decryptBlock(e,r),t.call(this,e,r,i),this._prevBlock=s}}),e}(),h=(i.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[],a=0;a<n;a+=4)s.push(i);var c=o.create(s,n);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},s.BlockCipher=d.extend({cfg:d.cfg.extend({mode:p,padding:h}),reset:function(){var e;d.reset.call(this);var t=this.cfg,r=t.iv,n=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),v=s.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),A=(i.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?o.create([1398893684,1701076831]).concat(r).concat(t):t).toString(l)},parse:function(e){var t,r=l.parse(e),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=o.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),v.create({ciphertext:r,salt:t})}},y=s.SerializableCipher=a.extend({cfg:a.extend({format:A}),encrypt:function(e,t,r,n){n=this.cfg.extend(n);var i=e.createEncryptor(r,n),s=i.finalize(t),a=i.cfg;return v.create({ciphertext:s,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:n.format})},decrypt:function(e,t,r,n){return n=this.cfg.extend(n),t=this._parse(t,n.format),e.createDecryptor(r,n).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),w=(i.kdf={}).OpenSSL={execute:function(e,t,r,n,i){if(n||(n=o.random(8)),i)s=f.create({keySize:t+r,hasher:i}).compute(e,n);else var s=f.create({keySize:t+r}).compute(e,n);var a=o.create(s.words.slice(t),4*r);return s.sigBytes=4*t,v.create({key:s,iv:a,salt:n})}},b=s.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:w}),encrypt:function(e,t,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,e.keySize,e.ivSize,n.salt,n.hasher);n.iv=i.iv;var s=y.encrypt.call(this,e,t,i.key,n);return s.mixIn(i),s},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=n.kdf.execute(r,e.keySize,e.ivSize,t.salt,n.hasher);return n.iv=i.iv,y.decrypt.call(this,e,t,i.key,n)}}))))},7548:function(e,t,r){var n;e.exports=(n=n||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==r.g&&r.g.crypto&&(n=r.g.crypto),!n)try{n=r(477)}catch(e){}var i=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},s=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),a={},o=a.lib={},c=o.Base={extend:function(e){var t=s(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},u=o.WordArray=c.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||f).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var s=0;s<i;s++){var a=r[s>>>2]>>>24-s%4*8&255;t[n+s>>>2]|=a<<24-(n+s)%4*8}else for(var o=0;o<i;o+=4)t[n+o>>>2]=r[o>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=c.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(i());return new u.init(t,e)}}),l=a.enc={},f=l.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var s=t[i>>>2]>>>24-i%4*8&255;n.push((s>>>4).toString(16)),n.push((15&s).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new u.init(r,t/2)}},d=l.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var s=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(s))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new u.init(r,t)}},g=l.Utf8={stringify:function(e){try{return decodeURIComponent(escape(d.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return d.parse(unescape(encodeURIComponent(e)))}},m=o.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=g.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,i=n.words,s=n.sigBytes,a=this.blockSize,o=s/(4*a),c=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*a,l=e.min(4*c,s);if(c){for(var f=0;f<c;f+=a)this._doProcessBlock(i,f);r=i.splice(0,c),n.sigBytes-=l}return new u.init(r,l)},clone:function(){var e=c.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),p=(o.Hasher=m.extend({cfg:c.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){m.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new p.HMAC.init(e,r).finalize(t)}}}),a.algo={});return a}(Math),n)},1359:function(e,t,r){var n,i,s;e.exports=(n=r(7548),s=(i=n).lib.WordArray,i.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],s=0;s<r;s+=3)for(var a=(t[s>>>2]>>>24-s%4*8&255)<<16|(t[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|t[s+2>>>2]>>>24-(s+2)%4*8&255,o=0;o<4&&s+.75*o<r;o++)i.push(n.charAt(a>>>6*(3-o)&63));var c=n.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var i=0;i<r.length;i++)n[r.charCodeAt(i)]=i}var a=r.charAt(64);if(a){var o=e.indexOf(a);-1!==o&&(t=o)}return function(e,t,r){for(var n=[],i=0,a=0;a<t;a++)if(a%4){var o=r[e.charCodeAt(a-1)]<<a%4*2|r[e.charCodeAt(a)]>>>6-a%4*2;n[i>>>2]|=o<<24-i%4*8,i++}return s.create(n,i)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)},2871:function(e,t,r){e.exports=r(7548).enc.Utf8},391:function(e,t,r){var n,i,s,a,o,c,u,l;e.exports=(l=r(7548),r(118),r(6408),s=(i=(n=l).lib).Base,a=i.WordArray,c=(o=n.algo).MD5,u=o.EvpKDF=s.extend({cfg:s.extend({keySize:4,hasher:c,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,n=this.cfg,i=n.hasher.create(),s=a.create(),o=s.words,c=n.keySize,u=n.iterations;o.length<c;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(var l=1;l<u;l++)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*c,s}}),n.EvpKDF=function(e,t,r){return u.create(r).compute(e,t)},l.EvpKDF)},6408:function(e,t,r){var n,i,s;e.exports=(i=(n=r(7548)).lib.Base,s=n.enc.Utf8,void(n.algo.HMAC=i.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=s.parse(t));var r=e.blockSize,n=4*r;t.sigBytes>n&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),a=this._iKey=t.clone(),o=i.words,c=a.words,u=0;u<r;u++)o[u]^=1549556828,c[u]^=909522486;i.sigBytes=a.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})))},6475:function(e,t,r){var n;e.exports=(n=r(7548),function(e){var t=n,r=t.lib,i=r.WordArray,s=r.Hasher,a=t.algo,o=[];!function(){for(var t=0;t<64;t++)o[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=a.MD5=s.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var s=this._hash.words,a=e[t+0],c=e[t+1],g=e[t+2],m=e[t+3],p=e[t+4],h=e[t+5],v=e[t+6],A=e[t+7],y=e[t+8],w=e[t+9],b=e[t+10],S=e[t+11],_=e[t+12],x=e[t+13],M=e[t+14],O=e[t+15],E=s[0],P=s[1],R=s[2],k=s[3];E=u(E,P,R,k,a,7,o[0]),k=u(k,E,P,R,c,12,o[1]),R=u(R,k,E,P,g,17,o[2]),P=u(P,R,k,E,m,22,o[3]),E=u(E,P,R,k,p,7,o[4]),k=u(k,E,P,R,h,12,o[5]),R=u(R,k,E,P,v,17,o[6]),P=u(P,R,k,E,A,22,o[7]),E=u(E,P,R,k,y,7,o[8]),k=u(k,E,P,R,w,12,o[9]),R=u(R,k,E,P,b,17,o[10]),P=u(P,R,k,E,S,22,o[11]),E=u(E,P,R,k,_,7,o[12]),k=u(k,E,P,R,x,12,o[13]),R=u(R,k,E,P,M,17,o[14]),E=l(E,P=u(P,R,k,E,O,22,o[15]),R,k,c,5,o[16]),k=l(k,E,P,R,v,9,o[17]),R=l(R,k,E,P,S,14,o[18]),P=l(P,R,k,E,a,20,o[19]),E=l(E,P,R,k,h,5,o[20]),k=l(k,E,P,R,b,9,o[21]),R=l(R,k,E,P,O,14,o[22]),P=l(P,R,k,E,p,20,o[23]),E=l(E,P,R,k,w,5,o[24]),k=l(k,E,P,R,M,9,o[25]),R=l(R,k,E,P,m,14,o[26]),P=l(P,R,k,E,y,20,o[27]),E=l(E,P,R,k,x,5,o[28]),k=l(k,E,P,R,g,9,o[29]),R=l(R,k,E,P,A,14,o[30]),E=f(E,P=l(P,R,k,E,_,20,o[31]),R,k,h,4,o[32]),k=f(k,E,P,R,y,11,o[33]),R=f(R,k,E,P,S,16,o[34]),P=f(P,R,k,E,M,23,o[35]),E=f(E,P,R,k,c,4,o[36]),k=f(k,E,P,R,p,11,o[37]),R=f(R,k,E,P,A,16,o[38]),P=f(P,R,k,E,b,23,o[39]),E=f(E,P,R,k,x,4,o[40]),k=f(k,E,P,R,a,11,o[41]),R=f(R,k,E,P,m,16,o[42]),P=f(P,R,k,E,v,23,o[43]),E=f(E,P,R,k,w,4,o[44]),k=f(k,E,P,R,_,11,o[45]),R=f(R,k,E,P,O,16,o[46]),E=d(E,P=f(P,R,k,E,g,23,o[47]),R,k,a,6,o[48]),k=d(k,E,P,R,A,10,o[49]),R=d(R,k,E,P,M,15,o[50]),P=d(P,R,k,E,h,21,o[51]),E=d(E,P,R,k,_,6,o[52]),k=d(k,E,P,R,m,10,o[53]),R=d(R,k,E,P,b,15,o[54]),P=d(P,R,k,E,c,21,o[55]),E=d(E,P,R,k,y,6,o[56]),k=d(k,E,P,R,O,10,o[57]),R=d(R,k,E,P,v,15,o[58]),P=d(P,R,k,E,x,21,o[59]),E=d(E,P,R,k,p,6,o[60]),k=d(k,E,P,R,S,10,o[61]),R=d(R,k,E,P,g,15,o[62]),P=d(P,R,k,E,w,21,o[63]),s[0]=s[0]+E|0,s[1]=s[1]+P|0,s[2]=s[2]+R|0,s[3]=s[3]+k|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;r[i>>>5]|=128<<24-i%32;var s=e.floor(n/4294967296),a=n;r[15+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var o=this._hash,c=o.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return o},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,r,n,i,s,a){var o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t}function l(e,t,r,n,i,s,a){var o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t}function f(e,t,r,n,i,s,a){var o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t}function d(e,t,r,n,i,s,a){var o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t}t.MD5=s._createHelper(c),t.HmacMD5=s._createHmacHelper(c)}(Math),n.MD5)},118:function(e,t,r){var n,i,s,a,o,c,u,l;e.exports=(i=(n=l=r(7548)).lib,s=i.WordArray,a=i.Hasher,o=n.algo,c=[],u=o.SHA1=a.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],o=r[4],u=0;u<80;u++){if(u<16)c[u]=0|e[t+u];else{var l=c[u-3]^c[u-8]^c[u-14]^c[u-16];c[u]=l<<1|l>>>31}var f=(n<<5|n>>>27)+o+c[u];f+=u<20?1518500249+(i&s|~i&a):u<40?1859775393+(i^s^a):u<60?(i&s|i&a|s&a)-1894007588:(i^s^a)-899497514,o=a,a=s,s=i<<30|i>>>2,i=n,n=f}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),n.SHA1=a._createHelper(u),n.HmacSHA1=a._createHmacHelper(u),l.SHA1)},9764:function(e,t,r){var n;e.exports=(n=r(7548),function(e){var t=n,r=t.lib,i=r.WordArray,s=r.Hasher,a=t.algo,o=[],c=[];!function(){function t(t){for(var r=e.sqrt(t),n=2;n<=r;n++)if(!(t%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)t(n)&&(i<8&&(o[i]=r(e.pow(n,.5))),c[i]=r(e.pow(n,1/3)),i++),n++}();var u=[],l=a.SHA256=s.extend({_doReset:function(){this._hash=new i.init(o.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],o=r[4],l=r[5],f=r[6],d=r[7],g=0;g<64;g++){if(g<16)u[g]=0|e[t+g];else{var m=u[g-15],p=(m<<25|m>>>7)^(m<<14|m>>>18)^m>>>3,h=u[g-2],v=(h<<15|h>>>17)^(h<<13|h>>>19)^h>>>10;u[g]=p+u[g-7]+v+u[g-16]}var A=n&i^n&s^i&s,y=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),w=d+((o<<26|o>>>6)^(o<<21|o>>>11)^(o<<7|o>>>25))+(o&l^~o&f)+c[g]+u[g];d=f,f=l,l=o,o=a+w|0,a=s,s=i,i=n,n=w+(y+A)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0,r[5]=r[5]+l|0,r[6]=r[6]+f|0,r[7]=r[7]+d|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=e.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=s._createHelper(l),t.HmacSHA256=s._createHmacHelper(l)}(Math),n.SHA256)},7272:function(e,t){var r,n;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,r=function(e){"use strict";if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw new Error("This script should only be loaded in a browser extension.");if(globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id)e.exports=globalThis.browser;else{const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class n extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const i=(t,r)=>(...n)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||n.length<=1&&!1!==r.singleCallbackArg?t.resolve(n[0]):t.resolve(n)},s=e=>1==e?"argument":"arguments",a=(e,t,r)=>new Proxy(t,{apply:(t,n,i)=>r.call(n,e,...i)});let o=Function.call.bind(Object.prototype.hasOwnProperty);const c=(e,t={},r={})=>{let n=Object.create(null),u={has:(t,r)=>r in e||r in n,get(u,l,f){if(l in n)return n[l];if(!(l in e))return;let d=e[l];if("function"==typeof d)if("function"==typeof t[l])d=a(e,e[l],t[l]);else if(o(r,l)){let t=((e,t)=>function(r,...n){if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${s(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${s(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((s,a)=>{if(t.fallbackToNoCallback)try{r[e](...n,i({resolve:s,reject:a},t))}catch(i){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,i),r[e](...n),t.fallbackToNoCallback=!1,t.noCallback=!0,s()}else t.noCallback?(r[e](...n),s()):r[e](...n,i({resolve:s,reject:a},t))}))})(l,r[l]);d=a(e,e[l],t)}else d=d.bind(e);else if("object"==typeof d&&null!==d&&(o(t,l)||o(r,l)))d=c(d,t[l],r[l]);else{if(!o(r,"*"))return Object.defineProperty(n,l,{configurable:!0,enumerable:!0,get:()=>e[l],set(t){e[l]=t}}),d;d=c(d,t[l],r["*"])}return n[l]=d,d},set:(t,r,i,s)=>(r in n?n[r]=i:e[r]=i,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(n,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(n,t)},l=Object.create(e);return new Proxy(l,u)},u=e=>({addListener(t,r,...n){t.addListener(e.get(r),...n)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),l=new n((e=>"function"!=typeof e?e:function(t){const r=c(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),f=new n((e=>"function"!=typeof e?e:function(t,r,n){let i,s,a=!1,o=new Promise((e=>{i=function(t){a=!0,e(t)}}));try{s=e(t,r,i)}catch(e){s=Promise.reject(e)}const c=!0!==s&&((u=s)&&"object"==typeof u&&"function"==typeof u.then);var u;if(!0!==s&&!c&&!a)return!1;return(c?s:o).then((e=>{n(e)}),(e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",n({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error("Failed to send onMessage rejected reply",e)})),!0})),d=({reject:r,resolve:n},i)=>{e.runtime.lastError?e.runtime.lastError.message===t?n():r(new Error(e.runtime.lastError.message)):i&&i.__mozWebExtensionPolyfillReject__?r(new Error(i.message)):n(i)},g=(e,t,r,...n)=>{if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${s(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${s(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((e,t)=>{const i=d.bind(null,{resolve:e,reject:t});n.push(i),r.sendMessage(...n)}))},m={devtools:{network:{onRequestFinished:u(l)}},runtime:{onMessage:u(f),onMessageExternal:u(f),sendMessage:g.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:g.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},p={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":p},services:{"*":p},websites:{"*":p}},c(e,m,r)};e.exports=r(chrome)}},void 0===(n=r.apply(t,[e]))||(e.exports=n)},8231:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>s,revision:()=>a,upgrade:()=>o});var n=r(8618),i=r(7272);const s="Add theme support",a="20221211221603_add_theme_support";async function o(){const e={appTheme:"auto",showContribPage:!0,contribPageLastOpen:0,contribPageLastAutoOpen:0},{installTime:t}=await i.storage.local.get("installTime");return e.installTime=(0,n.I4)(t),e.storageVersion=a,i.storage.local.set(e)}},6797:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Update services",s="20221214080901_update_services";async function a(){const e={},{witSpeechApiKeys:t}=await n.storage.local.get("witSpeechApiKeys");return delete t.catalan,delete t.telugu,e.witSpeechApiKeys=t,await n.storage.local.remove("ibmSpeechApiLoc"),e.ibmSpeechApiUrl="",e.microsoftSpeechApiLoc="eastus",e.storageVersion=s,n.storage.local.set(e)}},3159:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add appVersion",s="20240514170322_add_appversion";async function a(){const e={appVersion:""};return e.storageVersion=s,n.storage.local.set(e)}},9005:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Revision description",s="DlgF14Chrh";async function a(){const e={},{speechService:t}=await n.storage.local.get("speechService");return"googleSpeechApiDemo"===t&&(e.speechService="witSpeechApiDemo"),e.storageVersion=s,n.storage.local.set(e)}},8730:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add navigateWithKeyboard",s="Lj3MYlSr4L";async function a(){const e={navigateWithKeyboard:!1};return e.storageVersion=s,n.storage.local.set(e)}},8988:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add IBM Watson Speech to Text API",s="ONiJBs00o";async function a(){const e={ibmSpeechApiLoc:"frankfurt",ibmSpeechApiKey:""};return e.storageVersion=s,n.storage.local.set(e)}},237:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add Microsoft Azure Speech to Text API",s="UidMDYaYA";async function a(){const e={microsoftSpeechApiLoc:"eastUs",microsoftSpeechApiKey:""};return e.storageVersion=s,n.storage.local.set(e)}},1858:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Initial version",s="UoT3kGyBH";async function a(){const e={speechService:"googleSpeechApiDemo",googleSpeechApiKey:"",installTime:(new Date).getTime(),useCount:0};return e.storageVersion=s,n.storage.local.set(e)}},6383:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add autoUpdateClientApp option",s="X3djS8vZC";async function a(){const e={autoUpdateClientApp:!0};return e.storageVersion=s,n.storage.local.set(e)}},6151:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add loadEnglishChallenge option",s="ZtLMLoh1ag";async function a(){const e={loadEnglishChallenge:!0};return e.storageVersion=s,n.storage.local.set(e)}},2987:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add Wit Speech API and tryEnglishSpeechModel option",s="nOedd0Txqd";async function a(){const e={witSpeechApiKeys:{},tryEnglishSpeechModel:!0};return e.storageVersion=s,n.storage.local.set(e)}},3580:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Add simulateUserInput option",s="t335iRDhZ8";async function a(){const e={simulateUserInput:!1};return e.storageVersion=s,n.storage.local.set(e)}},211:(e,t,r)=>{"use strict";r.r(t),r.d(t,{message:()=>i,revision:()=>s,upgrade:()=>a});var n=r(7272);const i="Initial version",s="20240514122825_initial_version";async function a(){const e={platformInfo:null};return e.storageVersion=s,n.storage.session.set(e)}},786:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u,Hu:()=>s,XL:()=>o});var n=r(4729),i=r(7272);async function s({area:e="local"}={}){try{return await i.storage[e].get(""),!0}catch(e){return!1}}const a={local:!1,session:!1,sync:!1};async function o({area:e="local"}={}){if(a[e])return!0;{const{storageVersion:t}=await i.storage[e].get("storageVersion");if(t&&t===n.QB[e])return a[e]=!0,!0}return!1}async function c({area:e="local"}={}){if(!a[e])return new Promise(((t,r)=>{let n;const i=async function(){await o({area:e})?(self.clearTimeout(s),t()):n?r(new Error(`Storage (${e}) is not ready`)):self.setTimeout(i,30)},s=self.setTimeout((function(){n=!0}),6e4);i()}))}const u={get:async function(e=null,{area:t="local"}={}){return await c({area:t}),i.storage[t].get(e)},set:async function(e,{area:t="local"}={}){return await c({area:t}),i.storage[t].set(e)},remove:async function(e,{area:t="local"}={}){return await c({area:t}),i.storage[t].remove(e)},clear:async function({area:e="local"}={}){return await c({area:e}),i.storage[e].clear()}}},8618:(e,t,r)=>{"use strict";r.d(t,{$u:()=>f,Dy:()=>P,HG:()=>d,I4:()=>w,Jh:()=>R,MQ:()=>O,Ms:()=>x,X5:()=>M,Xr:()=>g,Yi:()=>_,aI:()=>m,e6:()=>p,g0:()=>E,hw:()=>S,kL:()=>h,q4:()=>l,uo:()=>y,wT:()=>b}),r(9362);var n=r(9033),i=r.n(n),s=r(4665),a=r.n(s),o=r(786),c=r(4729),u=r(7272);function l(e,t){return u.i18n.getMessage(e,t)}function f({files:e=null,css:t=null,tabId:r=null,frameIds:n=[0],allFrames:i=!1,origin:s="USER"}){if(c.tn){const a={target:{tabId:r,allFrames:i}};return i||(a.target.frameIds=n),e?a.files=e:a.css=t,"safari"!==c.Dm&&(a.origin=s),u.scripting.insertCSS(a)}{const t={frameId:n[0]};return e?t.file=e[0]:t.code=code,u.tabs.insertCSS(r,t)}}async function d({files:e=null,func:t=null,args:r=null,tabId:n=null,frameIds:i=[0],allFrames:s=!1,world:a="ISOLATED",injectImmediately:o=!0,unwrapResults:l=!0,code:f=""}){if(c.tn){const f={target:{tabId:n,allFrames:s},world:a};s||(f.target.frameIds=i),e?f.files=e:(f.func=t,r&&(f.args=r)),"safari"!==c.Dm&&(f.injectImmediately=o);const d=await u.scripting.executeScript(f);return l?d.map((e=>e.result)):d}{const t={frameId:i[0]};return e?t.file=e[0]:t.code=f,o&&(t.runAt="document_start"),u.tabs.executeScript(n,t)}}async function g({tabId:e,frameId:t=0}={}){try{return await d({func:()=>!0,code:"true;",tabId:e,frameIds:[t]}),!0}catch(e){}}async function m({url:e="",index:t=null,active:r=!0,openerTabId:n=null,getTab:i=!1}={}){const s={url:e,active:r};null!==t&&(s.index=t),null!==n&&(s.openerTabId=n);let a=await u.tabs.create(s);if(i){if("samsung"===c.Dm){let t=1;for(;t<=500&&(!a||a.url!==e);)[a]=await u.tabs.query({lastFocusedWindow:!0,url:e}),await k(20),t+=1}return a}}async function p(){const[e]=await u.tabs.query({lastFocusedWindow:!0,active:!0});return e}async function h({tab:e,tabId:t=null}={}){if(e||null===t||(e=await u.tabs.get(t).catch((e=>null))),e&&e.id!==u.tabs.TAB_ID_NONE)return!0}let v,A;async function y(){if(!function(){const e=c.tn?u.runtime.getURL("/src/background/script.js"):u.runtime.getURL("/src/background/index.html");return self.location.href===e}())return u.runtime.sendMessage({id:"getPlatform"});let{os:e,arch:t}=await async function(){if(v)return v;if(c.tn)({platformInfo:v}=await o.Ay.get("platformInfo",{area:"session"}));else try{v=JSON.parse(window.sessionStorage.getItem("platformInfo"))}catch(e){}if(!v){let e,t;if("samsung"===c.Dm?(e="android",t=""):"safari"===c.Dm?({os:e,arch:t}=await u.runtime.sendNativeMessage("application.id",{id:"getPlatformInfo"})):({os:e,arch:t}=await u.runtime.getPlatformInfo()),v={os:e,arch:t},c.tn)await o.Ay.set({platformInfo:v},{area:"session"});else try{window.sessionStorage.setItem("platformInfo",JSON.stringify(v))}catch(e){}}return v}();"win"===e?e="windows":"mac"===e&&(e="macos"),["x86-32","i386"].includes(t)?t="386":["x86-64","x86_64"].includes(t)?t="amd64":t.startsWith("arm")&&(t="arm");const r="windows"===e,n="macos"===e,i="linux"===e,s="android"===e,a="ios"===e,l="ipados"===e,f=["android","ios","ipados"].includes(e),d="chrome"===c.Dm,g=["chrome","edge"].includes(c.Dm)&&/\sedg(?:e|a|ios)?\//i.test(navigator.userAgent),m="firefox"===c.Dm,p=["chrome","opera"].includes(c.Dm)&&/\sopr\//i.test(navigator.userAgent),h="safari"===c.Dm,A="samsung"===c.Dm;return{os:e,arch:t,targetEnv:c.Dm,isWindows:r,isMacos:n,isLinux:i,isAndroid:s,isIos:a,isIpados:l,isMobile:f,isChrome:d,isEdge:g,isFirefox:m,isOpera:p,isSafari:h,isSamsung:A}}function w(e){return e||(e=Date.now()),e-e%864e5}function b(){try{const{hostname:e}=new URL(u.runtime.getURL("/src/background/script.js"));return e}catch(e){}return null}function S(e,t){return Math.floor(Math.random()*(t-e+1))+e}function _(e){let t="";const r=new Uint8Array(e),n=r.byteLength;for(var i=0;i<n;i++)t+=String.fromCharCode(r[i]);return self.btoa(t)}function x(e){const t=self.atob(e),r=t.length,n=new Uint8Array(new ArrayBuffer(r));for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return n.buffer}async function M(){let e,t;try{({name:e,version:t}=await u.runtime.getBrowserInfo())}catch(e){}return e||({name:e,version:t}=i().getParser(self.navigator.userAgent).getBrowser()),e=e.toLowerCase(),{name:e,version:t}}async function O(e,{trimStart:t=0,trimEnd:r=0}={}){const n=await async function(e){const t=new AudioContext,r=await t.decodeAudioData(e);t.close();const n=new OfflineAudioContext(1,16e3*r.duration,16e3),i=n.createBufferSource();return i.connect(n.destination),i.buffer=r,i.start(),n.startRendering()}(e),i=await async function({audioBuffer:e,start:t,end:r}){const n=e.sampleRate,i=e.numberOfChannels,s=n*t,a=n*r-s,o=new AudioContext,c=o.createBuffer(i,a,n);o.close();const u=new Float32Array(a);for(var l=0;l<i;l++)e.copyFromChannel(u,l,s),c.copyToChannel(u,l,0);return c}({audioBuffer:n,start:t,end:n.duration-r});return a()(i)}async function E({url:e,reasons:t,justification:r}={}){const n=u.runtime.getURL(e);(await u.runtime.getContexts({contextTypes:["OFFSCREEN_DOCUMENT"],documentUrls:[n]})).length||(A?await A:(A=u.offscreen.createDocument({url:e,reasons:t,justification:r}),await A,A=null))}function P(e){return new Promise(((t,r)=>{const n=self.setTimeout((function(){i.disconnect(),self.clearTimeout(n),r()}),2e4),i=u.runtime.connect({name:"offscreen"});i.postMessage(e),i.onMessage.addListener((function(e){i.disconnect(),t(e)}))}))}function R(e,t){if(e=`${e}Run`,!self[e])return self[e]=!0,!t||t()}function k(e){return new Promise((t=>self.setTimeout(t,e)))}},4729:(e,t,r)=>{"use strict";r.d(t,{CU:()=>a,Dm:()=>n,Pc:()=>o,QB:()=>s,ZY:()=>i,tn:()=>c});const n="chrome",i=!0,s={local:"20240514170322_add_appversion",session:"20240514122825_initial_version"},a="3.1.0",o="0.3.0",c=!0},7249:(e,t,r)=>{var n={"./local/20221211221603_add_theme_support.js":8231,"./local/20221214080901_update_services.js":6797,"./local/20240514170322_add_appversion.js":3159,"./local/DlgF14Chrh.js":9005,"./local/Lj3MYlSr4L.js":8730,"./local/ONiJBs00o.js":8988,"./local/UidMDYaYA.js":237,"./local/UoT3kGyBH.js":1858,"./local/X3djS8vZC.js":6383,"./local/ZtLMLoh1ag.js":6151,"./local/nOedd0Txqd.js":2987,"./local/t335iRDhZ8.js":3580,"./session/20240514122825_initial_version.js":211};function i(e){return s(e).then(r)}function s(e){return Promise.resolve().then((()=>{if(!r.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}))}i.keys=()=>Object.keys(n),i.resolve=s,i.id=7249,e.exports=i},477:()=>{},2365:(e,t,r)=>{"use strict";var n=r(9200),i=r(7938),s=TypeError;e.exports=function(e){if(n(e))return e;throw new s(i(e)+" is not a function")}},9677:(e,t,r)=>{"use strict";var n=r(100),i=String,s=TypeError;e.exports=function(e){if(n(e))return e;throw new s("Can't set "+i(e)+" as a prototype")}},602:(e,t,r)=>{"use strict";var n=r(2430),i=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new i("Incorrect invocation")}},4398:(e,t,r)=>{"use strict";var n=r(9131),i=String,s=TypeError;e.exports=function(e){if(n(e))return e;throw new s(i(e)+" is not an object")}},6134:(e,t,r)=>{"use strict";var n=r(4360),i=r(8479),s=r(7457),a=function(e){return function(t,r,a){var o=n(t),c=s(o);if(0===c)return!e&&-1;var u,l=i(a,c);if(e&&r!=r){for(;c>l;)if((u=o[l++])!=u)return!0}else for(;c>l;l++)if((e||l in o)&&o[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},5589:(e,t,r)=>{"use strict";var n=r(7133),i=n({}.toString),s=n("".slice);e.exports=function(e){return s(i(e),8,-1)}},3650:(e,t,r)=>{"use strict";var n=r(917),i=r(9200),s=r(5589),a=r(4702)("toStringTag"),o=Object,c="Arguments"===s(function(){return arguments}());e.exports=n?s:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=o(e),a))?r:c?s(t):"Object"===(n=s(t))&&i(t.callee)?"Arguments":n}},4085:(e,t,r)=>{"use strict";var n=r(9158),i=r(4540),s=r(2428),a=r(4446);e.exports=function(e,t,r){for(var o=i(t),c=a.f,u=s.f,l=0;l<o.length;l++){var f=o[l];n(e,f)||r&&n(r,f)||c(e,f,u(t,f))}}},5044:(e,t,r)=>{"use strict";var n=r(6857),i=r(4446),s=r(2007);e.exports=n?function(e,t,r){return i.f(e,t,s(1,r))}:function(e,t,r){return e[t]=r,e}},2007:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},8521:(e,t,r)=>{"use strict";var n=r(9200),i=r(4446),s=r(4174),a=r(8466);e.exports=function(e,t,r,o){o||(o={});var c=o.enumerable,u=void 0!==o.name?o.name:t;if(n(r)&&s(r,u,o),o.global)c?e[t]=r:a(t,r);else{try{o.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=r:i.f(e,t,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return e}},8466:(e,t,r)=>{"use strict";var n=r(7732),i=Object.defineProperty;e.exports=function(e,t){try{i(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},6857:(e,t,r)=>{"use strict";var n=r(942);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},1466:(e,t,r)=>{"use strict";var n=r(7732),i=r(9131),s=n.document,a=i(s)&&i(s.createElement);e.exports=function(e){return a?s.createElement(e):{}}},4131:e=>{"use strict";e.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},1681:e=>{"use strict";e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},4017:(e,t,r)=>{"use strict";var n,i,s=r(7732),a=r(1681),o=s.process,c=s.Deno,u=o&&o.versions||c&&c.version,l=u&&u.v8;l&&(i=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!i&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(i=+n[1]),e.exports=i},2030:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},5824:(e,t,r)=>{"use strict";var n=r(7133),i=Error,s=n("".replace),a=String(new i("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,c=o.test(a);e.exports=function(e,t){if(c&&"string"==typeof e&&!i.prepareStackTrace)for(;t--;)e=s(e,o,"");return e}},3353:(e,t,r)=>{"use strict";var n=r(7732),i=r(2428).f,s=r(5044),a=r(8521),o=r(8466),c=r(4085),u=r(7453);e.exports=function(e,t){var r,l,f,d,g,m=e.target,p=e.global,h=e.stat;if(r=p?n:h?n[m]||o(m,{}):n[m]&&n[m].prototype)for(l in t){if(d=t[l],f=e.dontCallGetSet?(g=i(r,l))&&g.value:r[l],!u(p?l:m+(h?".":"#")+l,e.forced)&&void 0!==f){if(typeof d==typeof f)continue;c(d,f)}(e.sham||f&&f.sham)&&s(d,"sham",!0),a(r,l,d,e)}}},942:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},7315:(e,t,r)=>{"use strict";var n=r(942);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},492:(e,t,r)=>{"use strict";var n=r(7315),i=Function.prototype.call;e.exports=n?i.bind(i):function(){return i.apply(i,arguments)}},7403:(e,t,r)=>{"use strict";var n=r(6857),i=r(9158),s=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,o=i(s,"name"),c=o&&"something"===function(){}.name,u=o&&(!n||n&&a(s,"name").configurable);e.exports={EXISTS:o,PROPER:c,CONFIGURABLE:u}},9229:(e,t,r)=>{"use strict";var n=r(7133),i=r(2365);e.exports=function(e,t,r){try{return n(i(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},7133:(e,t,r)=>{"use strict";var n=r(7315),i=Function.prototype,s=i.call,a=n&&i.bind.bind(s,s);e.exports=n?a:function(e){return function(){return s.apply(e,arguments)}}},848:(e,t,r)=>{"use strict";var n=r(7732),i=r(9200);e.exports=function(e,t){return arguments.length<2?(r=n[e],i(r)?r:void 0):n[e]&&n[e][t];var r}},9325:(e,t,r)=>{"use strict";var n=r(2365),i=r(2178);e.exports=function(e,t){var r=e[t];return i(r)?void 0:n(r)}},7732:function(e,t,r){"use strict";var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9158:(e,t,r)=>{"use strict";var n=r(7133),i=r(9272),s=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return s(i(e),t)}},640:e=>{"use strict";e.exports={}},5842:(e,t,r)=>{"use strict";var n=r(6857),i=r(942),s=r(1466);e.exports=!n&&!i((function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a}))},8060:(e,t,r)=>{"use strict";var n=r(7133),i=r(942),s=r(5589),a=Object,o=n("".split);e.exports=i((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===s(e)?o(e,""):a(e)}:a},2210:(e,t,r)=>{"use strict";var n=r(9200),i=r(9131),s=r(8018);e.exports=function(e,t,r){var a,o;return s&&n(a=t.constructor)&&a!==r&&i(o=a.prototype)&&o!==r.prototype&&s(e,o),e}},7217:(e,t,r)=>{"use strict";var n=r(7133),i=r(9200),s=r(5210),a=n(Function.toString);i(s.inspectSource)||(s.inspectSource=function(e){return a(e)}),e.exports=s.inspectSource},1514:(e,t,r)=>{"use strict";var n,i,s,a=r(3125),o=r(7732),c=r(9131),u=r(5044),l=r(9158),f=r(5210),d=r(2316),g=r(640),m="Object already initialized",p=o.TypeError,h=o.WeakMap;if(a||f.state){var v=f.state||(f.state=new h);v.get=v.get,v.has=v.has,v.set=v.set,n=function(e,t){if(v.has(e))throw new p(m);return t.facade=e,v.set(e,t),t},i=function(e){return v.get(e)||{}},s=function(e){return v.has(e)}}else{var A=d("state");g[A]=!0,n=function(e,t){if(l(e,A))throw new p(m);return t.facade=e,u(e,A,t),t},i=function(e){return l(e,A)?e[A]:{}},s=function(e){return l(e,A)}}e.exports={set:n,get:i,has:s,enforce:function(e){return s(e)?i(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!c(t)||(r=i(t)).type!==e)throw new p("Incompatible receiver, "+e+" required");return r}}}},9200:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},7453:(e,t,r)=>{"use strict";var n=r(942),i=r(9200),s=/#|\.prototype\./,a=function(e,t){var r=c[o(e)];return r===l||r!==u&&(i(t)?n(t):!!t)},o=a.normalize=function(e){return String(e).replace(s,".").toLowerCase()},c=a.data={},u=a.NATIVE="N",l=a.POLYFILL="P";e.exports=a},2178:e=>{"use strict";e.exports=function(e){return null==e}},9131:(e,t,r)=>{"use strict";var n=r(9200);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},100:(e,t,r)=>{"use strict";var n=r(9131);e.exports=function(e){return n(e)||null===e}},1818:e=>{"use strict";e.exports=!1},460:(e,t,r)=>{"use strict";var n=r(848),i=r(9200),s=r(2430),a=r(6253),o=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return i(t)&&s(t.prototype,o(e))}},7457:(e,t,r)=>{"use strict";var n=r(2695);e.exports=function(e){return n(e.length)}},4174:(e,t,r)=>{"use strict";var n=r(7133),i=r(942),s=r(9200),a=r(9158),o=r(6857),c=r(7403).CONFIGURABLE,u=r(7217),l=r(1514),f=l.enforce,d=l.get,g=String,m=Object.defineProperty,p=n("".slice),h=n("".replace),v=n([].join),A=o&&!i((function(){return 8!==m((function(){}),"length",{value:8}).length})),y=String(String).split("String"),w=e.exports=function(e,t,r){"Symbol("===p(g(t),0,7)&&(t="["+h(g(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!a(e,"name")||c&&e.name!==t)&&(o?m(e,"name",{value:t,configurable:!0}):e.name=t),A&&r&&a(r,"arity")&&e.length!==r.arity&&m(e,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?o&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=f(e);return a(n,"source")||(n.source=v(y,"string"==typeof t?t:"")),e};Function.prototype.toString=w((function(){return s(this)&&d(this).source||u(this)}),"toString")},8226:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},5334:(e,t,r)=>{"use strict";var n=r(7830);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:n(e)}},4446:(e,t,r)=>{"use strict";var n=r(6857),i=r(5842),s=r(335),a=r(4398),o=r(2548),c=TypeError,u=Object.defineProperty,l=Object.getOwnPropertyDescriptor,f="enumerable",d="configurable",g="writable";t.f=n?s?function(e,t,r){if(a(e),t=o(t),a(r),"function"==typeof e&&"prototype"===t&&"value"in r&&g in r&&!r[g]){var n=l(e,t);n&&n[g]&&(e[t]=r.value,r={configurable:d in r?r[d]:n[d],enumerable:f in r?r[f]:n[f],writable:!1})}return u(e,t,r)}:u:function(e,t,r){if(a(e),t=o(t),a(r),i)try{return u(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new c("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},2428:(e,t,r)=>{"use strict";var n=r(6857),i=r(492),s=r(6732),a=r(2007),o=r(4360),c=r(2548),u=r(9158),l=r(5842),f=Object.getOwnPropertyDescriptor;t.f=n?f:function(e,t){if(e=o(e),t=c(t),l)try{return f(e,t)}catch(e){}if(u(e,t))return a(!i(s.f,e,t),e[t])}},5809:(e,t,r)=>{"use strict";var n=r(8959),i=r(2030).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,i)}},1264:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},2430:(e,t,r)=>{"use strict";var n=r(7133);e.exports=n({}.isPrototypeOf)},8959:(e,t,r)=>{"use strict";var n=r(7133),i=r(9158),s=r(4360),a=r(6134).indexOf,o=r(640),c=n([].push);e.exports=function(e,t){var r,n=s(e),u=0,l=[];for(r in n)!i(o,r)&&i(n,r)&&c(l,r);for(;t.length>u;)i(n,r=t[u++])&&(~a(l,r)||c(l,r));return l}},6732:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,i=n&&!r.call({1:2},1);t.f=i?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},8018:(e,t,r)=>{"use strict";var n=r(9229),i=r(9131),s=r(3977),a=r(9677);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=n(Object.prototype,"__proto__","set"))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return s(r),a(n),i(r)?(t?e(r,n):r.__proto__=n,r):r}}():void 0)},1427:(e,t,r)=>{"use strict";var n=r(492),i=r(9200),s=r(9131),a=TypeError;e.exports=function(e,t){var r,o;if("string"===t&&i(r=e.toString)&&!s(o=n(r,e)))return o;if(i(r=e.valueOf)&&!s(o=n(r,e)))return o;if("string"!==t&&i(r=e.toString)&&!s(o=n(r,e)))return o;throw new a("Can't convert object to primitive value")}},4540:(e,t,r)=>{"use strict";var n=r(848),i=r(7133),s=r(5809),a=r(1264),o=r(4398),c=i([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=s.f(o(e)),r=a.f;return r?c(t,r(e)):t}},3977:(e,t,r)=>{"use strict";var n=r(2178),i=TypeError;e.exports=function(e){if(n(e))throw new i("Can't call method on "+e);return e}},2316:(e,t,r)=>{"use strict";var n=r(6014),i=r(685),s=n("keys");e.exports=function(e){return s[e]||(s[e]=i(e))}},5210:(e,t,r)=>{"use strict";var n=r(1818),i=r(7732),s=r(8466),a="__core-js_shared__",o=e.exports=i[a]||s(a,{});(o.versions||(o.versions=[])).push({version:"3.37.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"})},6014:(e,t,r)=>{"use strict";var n=r(5210);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},260:(e,t,r)=>{"use strict";var n=r(4017),i=r(942),s=r(7732).String;e.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!s(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},8479:(e,t,r)=>{"use strict";var n=r(4932),i=Math.max,s=Math.min;e.exports=function(e,t){var r=n(e);return r<0?i(r+t,0):s(r,t)}},4360:(e,t,r)=>{"use strict";var n=r(8060),i=r(3977);e.exports=function(e){return n(i(e))}},4932:(e,t,r)=>{"use strict";var n=r(8226);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},2695:(e,t,r)=>{"use strict";var n=r(4932),i=Math.min;e.exports=function(e){var t=n(e);return t>0?i(t,9007199254740991):0}},9272:(e,t,r)=>{"use strict";var n=r(3977),i=Object;e.exports=function(e){return i(n(e))}},9422:(e,t,r)=>{"use strict";var n=r(492),i=r(9131),s=r(460),a=r(9325),o=r(1427),c=r(4702),u=TypeError,l=c("toPrimitive");e.exports=function(e,t){if(!i(e)||s(e))return e;var r,c=a(e,l);if(c){if(void 0===t&&(t="default"),r=n(c,e,t),!i(r)||s(r))return r;throw new u("Can't convert object to primitive value")}return void 0===t&&(t="number"),o(e,t)}},2548:(e,t,r)=>{"use strict";var n=r(9422),i=r(460);e.exports=function(e){var t=n(e,"string");return i(t)?t:t+""}},917:(e,t,r)=>{"use strict";var n={};n[r(4702)("toStringTag")]="z",e.exports="[object z]"===String(n)},7830:(e,t,r)=>{"use strict";var n=r(3650),i=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return i(e)}},7938:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},685:(e,t,r)=>{"use strict";var n=r(7133),i=0,s=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++i+s,36)}},6253:(e,t,r)=>{"use strict";var n=r(260);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},335:(e,t,r)=>{"use strict";var n=r(6857),i=r(942);e.exports=n&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},3125:(e,t,r)=>{"use strict";var n=r(7732),i=r(9200),s=n.WeakMap;e.exports=i(s)&&/native code/.test(String(s))},4702:(e,t,r)=>{"use strict";var n=r(7732),i=r(6014),s=r(9158),a=r(685),o=r(260),c=r(6253),u=n.Symbol,l=i("wks"),f=c?u.for||u:u&&u.withoutSetter||a;e.exports=function(e){return s(l,e)||(l[e]=o&&s(u,e)?u[e]:f("Symbol."+e)),l[e]}},9362:(e,t,r)=>{"use strict";var n=r(3353),i=r(7732),s=r(848),a=r(2007),o=r(4446).f,c=r(9158),u=r(602),l=r(2210),f=r(5334),d=r(4131),g=r(5824),m=r(6857),p=r(1818),h="DOMException",v=s("Error"),A=s(h),y=function(){u(this,w);var e=arguments.length,t=f(e<1?void 0:arguments[0]),r=f(e<2?void 0:arguments[1],"Error"),n=new A(t,r),i=new v(t);return i.name=h,o(n,"stack",a(1,g(i.stack,1))),l(n,this,y),n},w=y.prototype=A.prototype,b="stack"in new v(h),S="stack"in new A(1,2),_=A&&m&&Object.getOwnPropertyDescriptor(i,h),x=!(!_||_.writable&&_.configurable),M=b&&!x&&!S;n({global:!0,constructor:!0,forced:p||M},{DOMException:M?y:A});var O=s(h),E=O.prototype;if(E.constructor!==O)for(var P in p||o(E,"constructor",a(1,O)),d)if(c(d,P)){var R=d[P],k=R.s;c(O,k)||o(O,k,a(6,R.c))}},8977:e=>{"use strict";e.exports=JSON.parse('{"revisions":{"local":["UoT3kGyBH","ONiJBs00o","UidMDYaYA","nOedd0Txqd","ZtLMLoh1ag","t335iRDhZ8","X3djS8vZC","DlgF14Chrh","Lj3MYlSr4L","20221211221603_add_theme_support","20221214080901_update_services","20240514170322_add_appversion"],"session":["20240514122825_initial_version"]}}')}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return r[e].call(s.exports,s,s.exports,i),s.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(r,n){if(1&n&&(r=this(r)),8&n)return r;if("object"==typeof r&&r){if(4&n&&r.__esModule)return r;if(16&n&&"function"==typeof r.then)return r}var s=Object.create(null);i.r(s);var a={};e=e||[null,t({}),t([]),t(t)];for(var o=2&n&&r;"object"==typeof o&&!~e.indexOf(o);o=t(o))Object.getOwnPropertyNames(o).forEach((e=>a[e]=()=>r[e]));return a.default=()=>r,i.d(s,a),s},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";i(9362);var e=i(6984),t=i.n(e),r=i(9764),n=i.n(r),s=i(2871),a=i.n(s);const o=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0},c="object"==typeof global&&global&&global.Object===Object&&global;var u="object"==typeof self&&self&&self.Object===Object&&self;const l=(c||u||Function("return this")()).Symbol;var f=Object.prototype,d=f.hasOwnProperty,g=f.toString,m=l?l.toStringTag:void 0;var p=Object.prototype.toString;var h=l?l.toStringTag:void 0;const v=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":h&&h in Object(e)?function(e){var t=d.call(e,m),r=e[m];try{e[m]=void 0;var n=!0}catch(e){}var i=g.call(e);return n&&(t?e[m]=r:delete e[m]),i}(e):function(e){return p.call(e)}(e)},A=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},y=function(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}(e.length)&&!function(e){if(!A(e))return!1;var t=v(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}(e)};var w=/^(?:0|[1-9]\d*)$/;const b=function(e,t,r){if(!A(r))return!1;var n=typeof t;return!!("number"==n?y(r)&&function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&w.test(e))&&e>-1&&e%1==0&&e<t}(t,r.length):"string"==n&&t in r)&&function(e,t){return e===t||e!=e&&t!=t}(r[t],e)};var S=/\s/;var _=/^\s+/;const x=function(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&S.test(e.charAt(t)););return t}(e)+1).replace(_,""):e};var M=/^[-+]0x[0-9a-f]+$/i,O=/^0b[01]+$/i,E=/^0o[0-7]+$/i,P=parseInt;const R=function(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return null!=e&&"object"==typeof e}(e)&&"[object Symbol]"==v(e)}(e))return NaN;if(A(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=A(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=x(e);var r=O.test(e);return r||E.test(e)?P(e.slice(2),r?2:8):M.test(e)?NaN:+e};var k=1/0;const T=function(e){var t=function(e){return e?(e=R(e))===k||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}(e),r=t%1;return t==t?r?t-r:t:0},I=function(e,t,r){var n=null==e?0:e.length;return n?(r&&"number"!=typeof r&&b(e,t,r)?(t=0,r=n):(t=null==t?0:T(t),r=void 0===r?n:T(r)),function(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var s=Array(i);++n<i;)s[n]=e[n+t];return s}(e,t,r)):[]},B=function(e){return e!=e};var C=Math.max;const F=function(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=null==r?0:T(r);return i<0&&(i=C(n+i,0)),function(e,t,r){return t==t?function(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}(e,t,r):function(e,t,r,n){for(var i=e.length,s=r+(n?1:-1);n?s--:++s<i;)if(t(e[s],s,e))return s;return-1}(e,B,r)}(e,t,i)};var N=i(786),L=i(7272);async function j({area:e="local",data:t=null,silent:r=!1}={}){const n={getAvailableRevisions:async({area:e}={})=>(await Promise.resolve().then(i.t.bind(i,8977,19))).revisions[e],getCurrentRevision:async({area:e}={})=>(await L.storage[e].get("storageVersion")).storageVersion,getRevision:async({area:e,revision:t}={})=>i(7249)(`./${e}/${t}.js`)};return"local"===e&&await async function(){if(await(0,N.Hu)({area:"sync"})){const{storageVersion:e}=await L.storage.sync.get("storageVersion");if(e&&e.length<14){const{storageVersion:e}=await L.storage.local.get("storageVersion");if(!e||e.length<14){const e=await L.storage.sync.get(null);await L.storage.local.clear(),await L.storage.local.set(e),await L.storage.sync.clear()}}}}(),async function(e,{area:t="local",data:r=null,silent:n=!1}={}){return async function(e,{area:t="local",data:r=null,silent:n=!1}={}){const i=await e.getAvailableRevisions({area:t}),s=await e.getCurrentRevision({area:t}),a=o(i);if(s===a)return;const c=I(i,F(i,s)+1,F(i,a)+1);n||console.log(`Migrating storage (${t}): ${s} => ${a}`);for(const i of c){const s=await e.getRevision({area:t,revision:i});n||console.log(`Applying revision (${t}): ${s.revision} - ${s.message}`),await s.upgrade(r)}}(e,{area:t,data:r,silent:n})}(n,{area:e,data:t,silent:r})}const D={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let U;const z=new Uint8Array(16);function H(){if(!U&&(U="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!U))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return U(z)}const V=[];for(let e=0;e<256;++e)V.push((e+256).toString(16).slice(1));const W=function(e,t,r){if(D.randomUUID&&!t&&!e)return D.randomUUID();const n=(e=e||{}).random||(e.rng||H)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return V[e[t+0]]+V[e[t+1]]+V[e[t+2]]+V[e[t+3]]+"-"+V[e[t+4]]+V[e[t+5]]+"-"+V[e[t+6]]+V[e[t+7]]+"-"+V[e[t+8]]+V[e[t+9]]+"-"+V[e[t+10]]+V[e[t+11]]+V[e[t+12]]+V[e[t+13]]+V[e[t+14]]+V[e[t+15]]}(n)};var q=i(8618);const $="^https://(?:www.)?(?:google.com|recaptcha.net)/recaptcha/(?:api2|enterprise)/(?:anchor|bframe).*",K=/^https:\/\/(?:www\.)?(?:google\.com|recaptcha\.net)\/recaptcha\/(?:api2|enterprise)\/bframe.*/,G={ar:"ar-SA",af:"af-ZA",am:"am-ET",hy:"hy-AM",az:"az-AZ",eu:"eu-ES",bn:"bn-BD",bg:"bg-BG",ca:"ca-ES","zh-HK":"cmn-Hans-HK","zh-CN":"cmn-Hans-CN","zh-TW":"cmn-Hant-TW",hr:"hr-HR",cs:"cs-CZ",da:"da-DK",nl:"nl-NL","en-GB":"en-GB",en:"en-US",et:"et-EE",fil:"fil-PH",fi:"fi-FI",fr:"fr-FR","fr-CA":"fr-CA",gl:"gl-ES",ka:"ka-GE",de:"de-DE","de-AT":"de-DE","de-CH":"de-DE",el:"el-GR",gu:"gu-IN",iw:"he-IL",hi:"hi-IN",hu:"hu-HU",is:"is-IS",id:"id-ID",it:"it-IT",ja:"ja-JP",kn:"kn-IN",ko:"ko-KR",lo:"lo-LA",lv:"lv-LV",lt:"lt-LT",ms:"ms-MY",ml:"ml-IN",mr:"mr-IN",mn:"mn-MN",no:"nb-NO",fa:"fa-IR",pl:"pl-PL",pt:"pt-PT","pt-BR":"pt-BR","pt-PT":"pt-PT",ro:"ro-RO",ru:"ru-RU",sr:"sr-RS",si:"si-LK",sk:"sk-SK",sl:"sl-SI",es:"es-ES","es-419":"es-MX",sw:"sw-TZ",sv:"sv-SE",ta:"ta-IN",te:"te-IN",th:"th-TH",tr:"tr-TR",uk:"uk-UA",ur:"ur-IN",vi:"vi-VN",zu:"zu-ZA"},Z={ar:"ar-MS_Telephony",af:"",am:"",hy:"",az:"",eu:"",bn:"",bg:"",ca:"","zh-HK":"","zh-CN":"zh-CN_Telephony","zh-TW":"zh-CN_Telephony",hr:"",cs:"cs-CZ_Telephony",da:"",nl:"nl-NL_Multimedia","en-GB":"en-GB_Multimedia",en:"en-US_Multimedia",et:"",fil:"",fi:"",fr:"fr-FR_Multimedia","fr-CA":"fr-CA_Multimedia",gl:"",ka:"",de:"de-DE_Multimedia","de-AT":"de-DE_Multimedia","de-CH":"de-DE_Multimedia",el:"",gu:"",iw:"",hi:"hi-IN_Telephony",hu:"",is:"",id:"",it:"it-IT_Multimedia",ja:"ja-JP_Multimedia",kn:"",ko:"ko-KR_Multimedia",lo:"",lv:"",lt:"",ms:"",ml:"",mr:"",mn:"",no:"",fa:"",pl:"",pt:"pt-BR_Multimedia","pt-BR":"pt-BR_Multimedia","pt-PT":"pt-BR_Multimedia",ro:"",ru:"",sr:"",si:"",sk:"",sl:"",es:"es-ES_Multimedia","es-419":"es-LA_Telephony",sw:"",sv:"sv-SE_Telephony",ta:"",te:"",th:"",tr:"",uk:"",ur:"",vi:"",zu:""},X={ar:"ar-EG",af:"af-ZA",am:"am-ET",hy:"hy-AM",az:"az-AZ",eu:"eu-ES",bn:"bn-IN",bg:"bg-BG",ca:"ca-ES","zh-HK":"zh-HK","zh-CN":"zh-CN","zh-TW":"zh-TW",hr:"hr-HR",cs:"cs-CZ",da:"da-DK",nl:"nl-NL","en-GB":"en-GB",en:"en-US",et:"et-EE",fil:"fil-PH",fi:"fi-FI",fr:"fr-FR","fr-CA":"fr-CA",gl:"gl-ES",ka:"ka-GE",de:"de-DE","de-AT":"de-AT","de-CH":"de-CH",el:"el-GR",gu:"gu-IN",iw:"he-IL",hi:"hi-IN",hu:"hu-HU",is:"is-IS",id:"id-ID",it:"it-IT",ja:"ja-JP",kn:"kn-IN",ko:"ko-KR",lo:"lo-LA",lv:"lv-LV",lt:"lt-LT",ms:"ms-MY",ml:"ml-IN",mr:"mr-IN",mn:"mn-MN",no:"nb-NO",fa:"fa-IR",pl:"pl-PL",pt:"pt-PT","pt-BR":"pt-BR","pt-PT":"pt-PT",ro:"ro-RO",ru:"ru-RU",sr:"sr-RS",si:"si-LK",sk:"sk-SK",sl:"sl-SI",es:"es-ES","es-419":"es-MX",sw:"sw-TZ",sv:"sv-SE",ta:"ta-IN",te:"te-IN",th:"th-TH",tr:"tr-TR",uk:"uk-UA",ur:"ur-IN",vi:"vi-VN",zu:"zu-ZA"},Y={ar:"arabic",af:"",am:"",hy:"",az:"",eu:"",bn:"bengali",bg:"",ca:"","zh-HK":"","zh-CN":"chinese","zh-TW":"chinese",hr:"",cs:"",da:"",nl:"dutch","en-GB":"english",en:"english",et:"",fil:"",fi:"finnish",fr:"french","fr-CA":"french",gl:"",ka:"",de:"german","de-AT":"german","de-CH":"german",el:"",gu:"",iw:"",hi:"hindi",hu:"",is:"",id:"indonesian",it:"italian",ja:"japanese",kn:"kannada",ko:"korean",lo:"",lv:"",lt:"",ms:"malay",ml:"malayalam",mr:"marathi",mn:"",no:"",fa:"",pl:"polish",pt:"portuguese","pt-BR":"portuguese","pt-PT":"portuguese",ro:"",ru:"russian",sr:"",si:"sinhala",sk:"",sl:"",es:"spanish","es-419":"spanish",sw:"",sv:"swedish",ta:"tamil",te:"",th:"thai",tr:"turkish",uk:"",ur:"urdu",vi:"vietnamese",zu:""};var Q=i(4729),J=i(7272);async function ee({message:e,messageId:t,title:r,type:n="info",timeout:i=0}={}){if(r||(r=(0,q.q4)("extensionName")),t&&(e=(0,q.q4)(t)),"safari"===Q.Dm)return J.runtime.sendNativeMessage("application.id",{id:"notification",message:e});{const t=await J.notifications.create(`bc-notification-${n}`,{type:"basic",title:r,message:e,iconUrl:"/src/assets/icons/app/icon-64.png"});return i&&self.setTimeout((()=>{J.notifications.clear(t)}),i),t}}async function te({url:e="",setOpenerTab:t=!0,getTab:r=!1,activeTab:n=null}={}){n||(n=await(0,q.e6)());const i={url:e,index:n.index+1,active:!0,getTab:r};return t&&(i.openerTabId=await async function({tab:e,tabId:t=null}={}){return e||null===t||(e=await J.tabs.get(t).catch((e=>null))),await(0,q.kL)({tab:e})&&!(await(0,q.uo)()).isMobile?e.id:null}({tab:n})),(0,q.aI)(i)}let re,ne=0;async function ie({action:e="auto",activeTab:t=null,showContribPage:r=!0}={}){if(await async function({valueChange:e=1,maxUseCount:t=1/0,minInterval:r=0}={}){if(Date.now()-ne>=r){ne=Date.now();const{useCount:r}=await N.Ay.get("useCount");r<t?await N.Ay.set({useCount:r+e}):r>t&&await N.Ay.set({useCount:t})}}({valueChange:1,maxUseCount:1e3}),r)return async function({minUseCount:e=0,minInstallDays:t=0,minLastOpenDays:r=0,minLastAutoOpenDays:n=0,action:i="auto",activeTab:s=null}={}){if(Q.ZY){const a=await N.Ay.get(["showContribPage","useCount","installTime","contribPageLastOpen","contribPageLastAutoOpen"]),o=(0,q.I4)();if(a.showContribPage&&a.useCount>=e&&o-a.installTime>=864e5*t&&o-a.contribPageLastOpen>=864e5*r&&o-a.contribPageLastAutoOpen>=864e5*n)return await N.Ay.set({contribPageLastOpen:o,contribPageLastAutoOpen:o}),async function({action:e="",updateStats:t=!0,getTab:r=!1,activeTab:n=null}={}){t&&await N.Ay.set({contribPageLastOpen:(0,q.I4)()});let i=J.runtime.getURL("/src/contribute/index.html");return e&&(i=`${i}?action=${e}`),te({url:i,getTab:r,activeTab:n})}({action:i,updateStats:!1,activeTab:s,getTab:!0})}}({minUseCount:10,minInstallDays:14,minLastOpenDays:14,minLastAutoOpenDays:365,activeTab:t,action:e})}async function se({getTab:e=!1,activeTab:t=null}={}){return te({url:J.runtime.getURL("/src/options/index.html"),getTab:e,activeTab:t})}var ae=i(7272);let oe,ce;function ue(e){let t=-1;if(window!==window.top){const e=window.parent.frames;for(let r=0;r<e.length;r++)if(e[r]===window){t=r;break}}const r=window.frames[e];for(const e of document.querySelectorAll("iframe"))if(e.contentWindow===r){let{left:r,top:n}=e.getBoundingClientRect();const i=window.devicePixelRatio;return{x:r*i,y:n*i,currentIndex:t}}}function le(){const e=function(e){"resetCaptcha"===e.id&&(t(),function(e){const t=document.createElement("script");t.onload=function(t){t.target.remove(),document.dispatchEvent(new CustomEvent("___resetCaptcha",{detail:e}))},t.src=chrome.runtime.getURL("/src/scripts/reset.js"),document.documentElement.appendChild(t)}(e.challengeUrl))},t=function(){window.clearTimeout(r),chrome.runtime.onMessage.removeListener(e)},r=window.setTimeout(t,1e4);chrome.runtime.onMessage.addListener(e)}function fe(e){const t=new URL(e.url);if("en"!==t.searchParams.get("hl"))return t.searchParams.set("hl","en"),{redirectUrl:t.toString()}}async function de(){const{loadEnglishChallenge:e,simulateUserInput:t}=await N.Ay.get(["loadEnglishChallenge","simulateUserInput"]);Q.tn?e||t?await ae.declarativeNetRequest.updateSessionRules({removeRuleIds:[1],addRules:[{id:1,action:{type:"redirect",redirect:{transform:{queryTransform:{addOrReplaceParams:[{key:"hl",value:"en"}]}}}},condition:{regexFilter:$,resourceTypes:["sub_frame"]}}]}):await ae.declarativeNetRequest.updateSessionRules({removeRuleIds:[1]}):e||t?ae.webRequest.onBeforeRequest.hasListener(fe)||ae.webRequest.onBeforeRequest.addListener(fe,{urls:["https://google.com/recaptcha/api2/anchor*","https://google.com/recaptcha/api2/bframe*","https://www.google.com/recaptcha/api2/anchor*","https://www.google.com/recaptcha/api2/bframe*","https://google.com/recaptcha/enterprise/anchor*","https://google.com/recaptcha/enterprise/bframe*","https://www.google.com/recaptcha/enterprise/anchor*","https://www.google.com/recaptcha/enterprise/bframe*","https://recaptcha.net/recaptcha/api2/anchor*","https://recaptcha.net/recaptcha/api2/bframe*","https://www.recaptcha.net/recaptcha/api2/anchor*","https://www.recaptcha.net/recaptcha/api2/bframe*","https://recaptcha.net/recaptcha/enterprise/anchor*","https://recaptcha.net/recaptcha/enterprise/bframe*","https://www.recaptcha.net/recaptcha/enterprise/anchor*","https://www.recaptcha.net/recaptcha/enterprise/bframe*"],types:["sub_frame"]},["blocking"]):ae.webRequest.onBeforeRequest.hasListener(fe)&&ae.webRequest.onBeforeRequest.removeListener(fe)}function ge(e){const t=e.requestHeaders;if(t.some((e=>"origin"===e.name.toLowerCase()&&e.value===self.location.origin)))for(const e of t){const r=e.name.toLowerCase();"origin"!==r&&"referer"!==r||t.splice(t.indexOf(e),1)}return{requestHeaders:t}}async function me(e,r){if("witSpeechApiDemo"!==e){const{witSpeechApiKeys:e}=await N.Ay.get("witSpeechApiKeys");return e[r]}{const e=(await async function(){if(Q.tn){const{secrets:e}=await N.Ay.get("secrets",{area:"session"});if(e)return e}else if(ce)return ce;let e;try{const r=await(await fetch("/secrets.txt")).text(),i=n()(await(await fetch("/src/background/script.js")).text()+await(await fetch("/src/base/script.js")).text()).toString();e=JSON.parse(t().decrypt(r,i).toString(a()))}catch(t){const{speechService:r}=await N.Ay.get("speechService");"witSpeechApiDemo"===r&&await N.Ay.set({speechService:"witSpeechApi"}),e={}}finally{Q.tn?await N.Ay.set({secrets:e},{area:"session"}):ce=e}return e}()).witApiKeys;if(e){const t=e[r];return Array.isArray(t)?t[(0,q.hw)(1,t.length)-1]:t}}}async function pe(e,t){const r={},n=await fetch("https://api.wit.ai/speech?v=20240304",{method:"POST",headers:{Authorization:"Bearer "+e},body:new Blob([t],{type:"audio/wav"}),credentials:"omit"});if(200!==n.status){if(429!==n.status)throw new Error(`API response: ${n.status}, ${await n.text()}`);r.errorId="error_apiQuotaExceeded",r.errorTimeout=6e3}else{const e=JSON.parse((await n.text()).split("\r\n").at(-1)).text;e&&(r.text=e.trim())}return r}async function he(e,t,r,n){if("firefox"===Q.Dm){const i=await fetch("https://iam.cloud.ibm.com/identity/token",{method:"POST",body:new URLSearchParams({grant_type:"urn:ibm:params:oauth:grant-type:apikey",apikey:t}),credentials:"omit"});if(200!==i.status)throw new Error(`API response: ${i.status}, ${await i.text()}`);const{access_token:s}=await i.json(),a=e.replace(/^https(.*)/,"wss$1"),o=new WebSocket(`${a}/v1/recognize?access_token=${s}&model=${n}&x-watson-learning-opt-out=true`);return await new Promise(((e,t)=>{const n=self.setTimeout((function(){o.close(),t(new Error("API timeout"))}),3e4);function i({result:r,error:i}={}){self.clearTimeout(n),i?t(i):e(r)}o.onopen=function(e){o.send(JSON.stringify({action:"start","content-type":"audio/wav",profanity_filter:!1})),o.send(new Blob([r])),o.send(JSON.stringify({action:"stop"}))},o.onmessage=function(e){const t=JSON.parse(e.data).results;t&&(o.close(),i({result:t[0]?.alternatives[0].transcript.trim()}))},o.onclose=function(e){1e3!==e.code&&i({error:new Error(`API response: ${e.code}`)})},o.onerror=function(e){i({error:new Error(`API response: ${e.code}`)})}}))}{const i=await fetch(`${e}/v1/recognize?model=${n}&profanity_filter=false`,{method:"POST",headers:{Authorization:"Basic "+self.btoa("apikey:"+t),"X-Watson-Learning-Opt-Out":"true",Priority:"1"},body:new Blob([r],{type:"audio/wav"}),credentials:"omit"});if(200!==i.status)throw new Error(`API response: ${i.status}, ${await i.text()}`);const s=(await i.json()).results;if(s&&s.length)return s[0].alternatives[0].transcript.trim()}}async function ve(e,t,r,n){const i=await fetch(`https://${e}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1?language=${n}&format=detailed&profanity=raw`,{method:"POST",headers:{"Ocp-Apim-Subscription-Key":t,"Content-type":"audio/wav; codec=audio/pcm; samplerate=16000"},body:new Blob([r],{type:"audio/wav"}),credentials:"omit"});if(200!==i.status)throw new Error(`API response: ${i.status}, ${await i.text()}`);const s=(await i.json()).NBest;if(s)return s[0].Lexical.trim()}async function Ae(e,t){var r;if(t.url!==self.location.href)if("samsung"===Q.Dm&&await(0,q.kL)({tab:t.tab})&&(t.tab=await ae.tabs.get(t.tab.id)),"notification"===e.id)ee({message:e.message,messageId:e.messageId,title:e.title,type:e.type,timeout:e.timeout});else if("captchaSolved"===e.id)await ie();else if("transcribeAudio"===e.id){const t=await async function(){const e=[2];if(Q.tn)return await ae.declarativeNetRequest.updateSessionRules({removeRuleIds:e,addRules:[{id:e[0],action:{type:"modifyHeaders",requestHeaders:[{operation:"remove",header:"Origin"},{operation:"remove",header:"Referer"}]},condition:{requestDomains:["google.com","recaptcha.net","api.wit.ai","speech.googleapis.com","iam.cloud.ibm.com","speech-to-text.watson.cloud.ibm.com","stt.speech.microsoft.com"],initiatorDomains:[(0,q.wT)()],resourceTypes:["websocket","xmlhttprequest"]}}]}),e;if(!ae.webRequest.onBeforeSendHeaders.hasListener(ge)){const e=["https://google.com/*","https://www.google.com/*","https://recaptcha.net/*","https://www.recaptcha.net/*","https://api.wit.ai/*","https://speech.googleapis.com/*","*://*.speech-to-text.watson.cloud.ibm.com/*","https://iam.cloud.ibm.com/*","https://*.stt.speech.microsoft.com/*"],t=["blocking","requestHeaders"];"firefox"!==Q.Dm&&Object.values(ae.webRequest.OnBeforeSendHeadersOptions).includes("extraHeaders")&&t.push("extraHeaders"),ae.webRequest.onBeforeSendHeaders.addListener(ge,{urls:e,types:["websocket","xmlhttprequest"]},t)}}();try{return await async function(e,t){const r=await(await fetch(e,{credentials:"omit"})).arrayBuffer(),n={trimStart:1.5,trimEnd:1.5};let i,s;if(Q.tn&&!["firefox","safari"].includes(Q.Dm)){await(0,q.g0)({url:"/src/offscreen/index.html",reasons:["USER_MEDIA"],justification:"process audio"});const{audioString:e}=await(0,q.Dy)({id:"processAudio",audioString:(0,q.Yi)(r),audioOptions:n});await ae.offscreen.closeDocument(),i=(0,q.Ms)(e)}else i=await(0,q.MQ)(r,n);const{speechService:a,tryEnglishSpeechModel:o}=await N.Ay.get(["speechService","tryEnglishSpeechModel"]);if(["witSpeechApiDemo","witSpeechApi"].includes(a)){const e=Y[t]||"english",r=await me(a,e);if(!r)return void ee({messageId:"error_missingApiKey"});const n=await pe(r,i);if(n.errorId)return void ee({messageId:n.errorId,timeout:n.errorTimeout});if(s=n.text,!s&&"english"!==e&&o){const e=await me(a,"english");if(!e)return void ee({messageId:"error_missingApiKey"});const t=await pe(e,i);if(t.errorId)return void ee({messageId:t.errorId,timeout:t.errorTimeout});s=t.text}}else if("googleSpeechApi"===a){const{googleSpeechApiKey:e}=await N.Ay.get("googleSpeechApiKey");if(!e)return void ee({messageId:"error_missingApiKey"});const r=G[t]||"en-US";s=await async function(e,t,r,n){const i={audio:{content:(0,q.Yi)(t)},config:{encoding:"LINEAR16",languageCode:r,model:"video",sampleRateHertz:16e3}};!["en-US","en-GB"].includes(r)&&n&&(i.config.model="default",i.config.alternativeLanguageCodes=["en-US"]);const s=await fetch(`https://speech.googleapis.com/v1p1beta1/speech:recognize?key=${e}`,{method:"POST",body:JSON.stringify(i),credentials:"omit"});if(200!==s.status)throw new Error(`API response: ${s.status}, ${await s.text()}`);const a=(await s.json()).results;if(a)return a[0].alternatives[0].transcript.trim()}(e,i,r,o)}else if("ibmSpeechApi"===a){const{ibmSpeechApiUrl:e,ibmSpeechApiKey:r}=await N.Ay.get(["ibmSpeechApiUrl","ibmSpeechApiKey"]);if(!e)return void ee({messageId:"error_missingApiUrl"});if(!r)return void ee({messageId:"error_missingApiKey"});const n=Z[t]||"en-US_Multimedia";s=await he(e,r,i,n),s||["en-US_Multimedia","en-GB_Multimedia"].includes(n)||!o||(s=await he(e,r,i,"en-US_Multimedia"))}else if("microsoftSpeechApi"===a){const{microsoftSpeechApiLoc:e,microsoftSpeechApiKey:r}=await N.Ay.get(["microsoftSpeechApiLoc","microsoftSpeechApiKey"]);if(!r)return void ee({messageId:"error_missingApiKey"});const n=X[t]||"en-US";s=await ve(e,r,i,n),s||["en-US","en-GB"].includes(n)||!o||(s=await ve(e,r,i,"en-US"))}if(s)return s;["witSpeechApiDemo","witSpeechApi"].includes(a)?ee({messageId:"error_captchaNotSolvedWitai",timeout:2e4}):ee({messageId:"error_captchaNotSolved",timeout:6e3})}(e.audioUrl,e.lang)}finally{await async function({ruleIds:e=null}={}){Q.tn?await ae.declarativeNetRequest.updateSessionRules({removeRuleIds:e}):ae.webRequest.onBeforeSendHeaders.hasListener(ge)&&ae.webRequest.onBeforeSendHeaders.removeListener(ge)}({ruleIds:t})}}else if("resetCaptcha"===e.id)await async function(e,t,r){t=(await ae.webNavigation.getFrame({tabId:e,frameId:t})).parentFrameId,await(0,q.Xr)({tabId:e,frameId:t})?(await(0,q.HG)({func:le,code:`(${le.toString()})()`,tabId:e,frameIds:[t]}),await ae.tabs.sendMessage(e,{id:"resetCaptcha",challengeUrl:r},{frameId:t})):await ee({messageId:"error_scriptsNotAllowed"})}(t.tab.id,t.frameId,e.challengeUrl);else{if("getFramePos"===e.id)return async function(e,t,r){let n=0,i=0;for(;-1!==(t=(await ae.webNavigation.getFrame({tabId:e,frameId:t})).parentFrameId);){const[s]=await(0,q.HG)({func:ue,args:[r],code:`(${ue.toString()})(${r})`,tabId:e,frameIds:[t]});r=s.currentIndex,n+=s.x,i+=s.y}return{x:n,y:i}}(t.tab.id,t.frameId,e.frameIndex);if("getOsScale"===e.id){let e=await ae.tabs.getZoom(t.tab.id);const[[n,i]]=await(0,q.HG)({func:()=>[window.devicePixelRatio,window.innerWidth],code:"[window.devicePixelRatio, window.innerWidth];",tabId:t.tab.id});if("firefox"===Q.Dm){const t=(await(r=await ae.tabs.captureVisibleTab({format:"jpeg",quality:10}),new Promise((e=>{const t=new Image;t.onload=()=>{e(t)},t.onerror=()=>{e()},t.onabort=()=>{e()},t.src=r})))).naturalWidth;Math.abs(t/i-n*e)<.005&&(e=1)}return n/e}if("startClientApp"===e.id)oe=ae.runtime.connectNative("org.buster.client");else if("stopClientApp"===e.id)oe&&oe.disconnect();else{if("messageClientApp"===e.id){const t={apiVersion:Q.Pc,...e.message};return function(e,t,{timeout:r=1e4}={}){return new Promise(((n,i)=>{const s=W();t.id=s;const a=function(e){e.id===s&&(c(),n(e))},o=function(){c(),i("No response from native app")},c=function(){self.clearTimeout(u),e.onMessage.removeListener(a),e.onDisconnect.removeListener(o)},u=self.setTimeout((function(){o()}),r);e.onMessage.addListener(a),e.onDisconnect.addListener(o),e.postMessage(t)}))}(oe,t)}if("openOptions"===e.id)await se();else{if("getPlatform"===e.id)return(0,q.uo)();if("getBrowser"===e.id)return(0,q.X5)();"optionChange"===e.id?await async function(){await de()}():"clientAppInstall"===e.id&&await async function(){await N.Ay.set({simulateUserInput:!0}),await ae.runtime.sendMessage({id:"reloadOptionsPage"}).catch((()=>null))}()}}}}async function ye(e){await se()}async function we({event:e=""}={}){const t=await async function({event:e=""}={}){re||(re=async function(){const e={install:!1,update:!1,session:!1,setupInstance:!1,setupSession:!1},{storageVersion:t,appVersion:r}=await J.storage.local.get(["storageVersion","appVersion"]);return t||(e.install=!0),t===Q.QB.local&&r===Q.CU||(e.update=!0),Q.tn&&await async function(){const e=J.extension.inIncognitoContext,t=e?"privateSession":"session",r=(await J.storage.session.get(t))[t];if(r||await J.storage.session.set({[t]:!0}),e)try{if(!await self.caches.has(t))return await self.caches.open(t),!0}catch(e){return!0}if(!r)return!0}()&&(e.session=!0),(e.install||e.update)&&(e.setupInstance=!0),!e.session&&Q.tn||(e.setupSession=!0),e}(),re.events=[]),e&&re.events.push(e);const t=await re;return re.events.includes("install")&&(t.setupInstance=!0),re.events.includes("startup")&&(t.setupSession=!0),t}({event:e});t.setupInstance&&await(0,q.Jh)("setupInstance",(async()=>{if(await(0,N.XL)()||await j(),["chrome","edge","opera","samsung"].includes(Q.Dm)&&await async function({activeTab:e=!1}={}){const t=[];if(e){const e=await(0,q.e6)();e&&t.push(e)}else t.push(...await J.tabs.query({url:["http://*/*","https://*/*"],windowType:"normal"}));for(const e of t){const t=e.id,r=await J.webNavigation.getAllFrames({tabId:t});for(const e of r){const r=e.frameId;r&&K.test(e.url)&&((0,q.$u)({files:["/src/base/style.css"],tabId:t,frameIds:[r]}),(0,q.HG)({files:["/src/base/script.js"],tabId:t,frameIds:[r],injectImmediately:!1}))}}}(),t.install){const e=await ae.tabs.query({url:"http://127.0.0.1/buster/setup?session=*",windowType:"normal"});for(const t of e)await ae.tabs.reload(t.id)}t.update&&await async function(){await N.Ay.set({appVersion:Q.CU})}()})),t.setupSession&&await(0,q.Jh)("setupSession",(async()=>{Q.tn&&!await(0,N.XL)({area:"session"})&&await j({area:"session",silent:!0}),await de()}))}Q.tn?ae.action.onClicked.addListener(ye):ae.browserAction.onClicked.addListener(ye),ae.runtime.onMessage.addListener((function(e,t,r){return function(e,t){return"safari"===Q.Dm?(e.then((function(e){void 0===e&&(e=null),t(e)})),!0):e}(Ae(e,t),r)})),ae.runtime.onInstalled.addListener((async function(e){["install","update"].includes(e.reason)&&await we({event:"install"})})),ae.runtime.onStartup.addListener((async function(){await we({event:"startup"})})),we()})()})();