(()=>{var x=chrome;var I="https://api.nopecha.com",d="https://www.nopecha.com",U="https://developers.nopecha.com",Ie={doc:{url:U,automation:{url:`${U}/guides/extension_advanced/#automation-build`}},api:{url:I,recognition:{url:`${I}/recognition`},status:{url:`${I}/status`}},www:{url:d,annoucement:{url:`${d}/json/announcement.json`},demo:{url:`${d}/captcha`,recaptcha:{url:`${d}/captcha/recaptcha`},funcaptcha:{url:`${d}/captcha/funcaptcha`},awscaptcha:{url:`${d}/captcha/awscaptcha`},textcaptcha:{url:`${d}/captcha/textcaptcha`},turnstile:{url:`${d}/captcha/turnstile`},perimeterx:{url:`${d}/captcha/perimeterx`},geetest:{url:`${d}/captcha/geetest`},lemincaptcha:{url:`${d}/captcha/lemincaptcha`}},manage:{url:`${d}/manage`},pricing:{url:`${d}/pricing`},setup:{url:`${d}/setup`}},discord:{url:`${d}/discord`},github:{url:`${d}/github`,release:{url:`${d}/github/release`}}};function N(e){let n=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(t=>t.charCodeAt(0));return z(n)}var W=new Uint32Array(256);for(let e=256;e--;){let n=e;for(let t=8;t--;)n=n&1?3988292384^n>>>1:n>>>1;W[e]=n}function z(e){let n=-1;for(let t of e)n=n>>>8^W[n&255^t];return(n^-1)>>>0}async function f(e,n){let t=""+[+new Date,performance.now(),Math.random()],[o,a]=await new Promise(r=>{x.runtime.sendMessage([t,e,...n],r)});if(o===N(t))return a}function F(){let e;return n=>e||(e=n().finally(()=>e=void 0),e)}var he=F(),_;function X(){return he(async()=>(_||(_=await f("settings::get",[])),_))}function Y(e){_&&(_={..._,...e},K(_))}function B(){return _}function l(e){return new Promise(n=>setTimeout(n,e))}var Q=[];function E(e,n){e.timedout=!1,Q.push(e);let t,o=setInterval(async()=>{await j(e,B())||(clearTimeout(t),clearInterval(o))},400);n&&(t=setTimeout(()=>clearInterval(o),n),e.timedout=!0)}async function j(e,n){if(e.timedout)return!1;let t=e.condition(n);if(t===e.running())return!1;if(!t&&e.running())return e.quit(),!1;if(t&&!e.running()){for(;!e.ready();)await l(200);return e.start(),!1}}function K(e){Q.forEach(n=>j(n,e))}function V(){x.runtime.connect({name:"stream"}).onMessage.addListener(n=>{n.event==="settingsUpdate"&&Y(n.settings)})}function R(e){if(document.readyState!=="loading")setTimeout(e,0);else{let n;n=()=>{removeEventListener("DOMContentLoaded",n),e()},addEventListener("DOMContentLoaded",n)}}var _e=(e,n,t)=>new MouseEvent(e,{bubbles:!0,cancelable:!0,view:window,detail:1,screenX:n,screenY:t,clientX:n,clientY:t,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:null});function G(e,{events:n=null,x:t=null,y:o=null}={}){let a=document.querySelector(e);if(!a)return;if(t===null||o===null){let i=a.getBoundingClientRect();t=i.left+i.width/2,o=i.top+i.height/2}let r=n??["mouseover","mousedown","mouseup","click"];for(let i of r)a.dispatchEvent(_e(i,t,o))}var $,H=!1;function J(){return!!document.querySelector(".geetest_btn_click")}function Z(){H=!0;let e=()=>{if(!document.querySelector(".geetest_captcha")?.classList.contains("geetest_nextReady"))return;document.querySelector(".geetest_btn_click").attributes["aria-label"]?.value==="Click to verify"&&ve()};$=new MutationObserver(e),$.observe(document.querySelector(".geetest_captcha"),{subtree:!0,childList:!0}),e()}function ee(){$.disconnect(),H=!1}function te(){return H}async function ve(){await l(400),G(".geetest_btn_click")}function S(e,n){let t=document.createElement("canvas");return t.width=e,t.height=n,t}function ne(e){return e.toDataURL("image/jpeg").replace(/data:image\/[a-z]+;base64,/g,"")}function we(e){try{e.getContext("2d").getImageData(0,0,1,1)}catch{return!0}return!1}async function L(e,n,t=1e4){if(!n&&!e.complete&&!await new Promise(i=>{let c=setTimeout(()=>{i(!1)},t);e.addEventListener("load",()=>{clearTimeout(c),i(!0)})}))return;let o=S(e.naturalWidth||n?.clientWidth,e.naturalHeight||n?.clientHeight);return o.getContext("2d").drawImage(e,0,0),!we(o)&&o}async function oe(e){let t=getComputedStyle(e).backgroundImage;if(!t||t==="none")if("src"in e&&e.src)t=`url("${e.src}")`;else return;if("computedStyleMap"in e&&!/url\(["']https?:\/\//.test(t)){let s=e.computedStyleMap().get("background-image");if(s instanceof CSSImageValue){let g=await L(s,e);if(g)return g}}let o=/"(.+)"/.exec(t);if(!o)return;t=o[1];let a=document.createElement("a");if(a.href=t,new URL(a.href).origin===document.location.origin){let s=new Image;s.crossOrigin="anonymous",s.src=t;let g=await L(s);if(g)return g}let r=await f("fetch::asData",[t,{}]),i=new Image;i.crossOrigin="anonymous",i.src=r.data;let c=await L(i);if(c)return c}function ae(e,n="",t=""){let o=e.getContext("2d").getImageData(0,0,e.width,e.height),a=S(e.width,e.height),r=a.getContext("2d");r.putImageData(o,0,0);let i=75,c=75,s=e.width,g=e.height,m=s/g,u=s,p=g;u>i&&(u=i,p=u/m),p>c&&(p=c,u=p*m);function D(b,T,C,O){let h=10,y=O,k,v;if(b.font=`${y}px Arial`,v=b.measureText(T).width,v<=C)return`${y}px Arial`;for(;y-h>.1;)k=(y+h)/2,b.font=`${k}px Arial`,v=b.measureText(T).width,v>C?y=k:h=k;for(;v<C&&h<O;)h+=.1,b.font=`${h.toFixed(1)}px Arial`,v=b.measureText(T).width;return v>C&&(h-=.1),`${h.toFixed(1)}px Arial`}r.font=D(r,t,s,200),r.textBaseline="top";let w=r.measureText(t),fe=w.width,be=w.actualBoundingBoxAscent+w.actualBoundingBoxDescent;r.fillStyle="white",r.fillRect(0,0,fe,be),r.fillStyle="black",r.fillText(t,0,0);let q=a.toDataURL(),A=new Image;A.onload=function(){let b=[`background: url(${q}) no-repeat;`,`padding: ${p}px ${u}px;`,"background-size: contain;","font-size: 1px;"].join(" ");console.log(`%c ${n}`,b)},A.src=q}function xe(e,n,t,o){let a=(o*n+t)*4;return[e[a],e[a+1],e[a+2]]}function ye(e,n){return e.every(t=>t<=n)}function Ce(e,n){return e.every(t=>t>=n)}function ie(e,n=0,t=230,o=.99){let a=e.getContext("2d"),r=a.canvas.width,i=a.canvas.height;if(r===0||i===0)return!0;let c=a.getImageData(0,0,r,i).data,s=0;for(let m=0;m<i;m++)for(let u=0;u<r;u++){let p=xe(c,r,u,m);(ye(p,n)||Ce(p,t))&&s++}return s/(r*i)>o}function re(e,n=500,t=500){let o=e.getContext("2d"),a=o.canvas.width,r=o.canvas.height,i=o.getImageData(0,0,a,r),c=i.width,s=i.height,g=c/s,m=c,u=s;if(m>n&&(m=n,u=Math.floor(m/g)),u>t&&(u=t,m=Math.floor(u*g)),m===c&&u===s)return e;let p=S(c,s);p.getContext("2d").putImageData(i,0,0);let w=S(m,u);return w.getContext("2d").drawImage(p,0,0,c,s,0,0,m,u),w}async function se(e,n){let t={v:x.runtime.getManifest().version,key:ke(e)};return t.url=await f("tab::getURL",[]),t}function ke(e){return!e.keys||!e.keys.length?e.key:e.keys[Math.floor(Math.random()*e.keys.length)]}var P,M=!1;function de(){let e=document.querySelector(".geetest_bg");return!!(e&&e.style.display!=="none"&&e.style["background-image"]!=="none")}function ge(){M=!0,P=new MutationObserver(e=>{e[0].addedNodes.length>0&&ue()}),P.observe(document.querySelector(".geetest_box_wrap"),{childList:!0}),document.querySelector(".geetest_bg")&&ue()}function me(){P.disconnect(),M=!1}function pe(){return M}var ce=!1;async function ue(){if(ce)return;ce=!0;let e;for(;M;){if(document.querySelector(".geetest_box").style.display==="none"){await l(1e3);continue}let t=B(),o=Date.now().valueOf(),a=document.querySelector(".geetest_text_tips")?.textContent;if(!a){e=void 0,await l(1e3);continue}let r=512,i=document.querySelector(".geetest_bg"),c=await Be(r,i);if(!c){e=void 0,await l(1e3);continue}let s=`${a}:${c}`;if(s===e){await l(1e3);continue}e=s;let g=await f("api::recognition",[{type:"hcaptcha_area_select",task:a,image_data:[c],...await se(t)}]);if(!g||"error"in g){document.querySelector(".geetest_refresh")?.click(),await l(3e3);continue}let m=new Date().valueOf();if(t.geetest_solve_delay){let p=t.geetest_solve_delay_time-m+o;p>0&&await l(p)}let u=g.data.x;if(u){let p=parseInt(u);await Me(p),await l(5e3)}else document.querySelector(".geetest_refresh")?.click(),await l(3e3)}}async function Be(e,n){let t=await oe(n);if(t&&(ae(t,"[@nope/geetest]",`${t.width}x${t.height}`),!ie(t)))return e!==null&&(t=re(t,e,e)),ne(t)}function Se(e){for(;e=e.parentElement;)if(e.classList.contains("geetest_box"))return e;console.error("no .geetest_box found")}async function le(e,n,t,o=50){let a=n.left!==void 0?n:n.getBoundingClientRect(),r=t.left!==void 0?t:t.getBoundingClientRect(),[i,c]=[(r.left-a.left)/o,(r.top-a.top)/o];for(let s=0;s<o;s++)e.dispatchEvent(new MouseEvent("mousemove",{clientX:i*s+a.left,clientY:c*s+a.top})),await l(10);await l(50)}async function Me(e){let n=document.querySelector(".geetest_btn"),t=n.getBoundingClientRect(),o=n.parentElement.getBoundingClientRect(),a=.95,r=o.width*a*e/100,i=Se(n);await le(i,{left:500,top:500},t),n.dispatchEvent(new MouseEvent("mousemove",{clientX:t.left+30,clientY:t.top+30})),await l(50),n.dispatchEvent(new MouseEvent("mousedown",{clientX:t.left+30,clientY:t.top+30})),await l(50),await le(n,t,{left:t.left+r,top:t.top}),await l(50);let c=new MouseEvent("mouseup",{clientX:t.left+r,clientY:t.top});n.dispatchEvent(c),i.dispatchEvent(c)}async function Te(){V(),await X(),await f("tab::registerDetectedCaptcha",["geetest"]);let n=new URLSearchParams(location.hash.substring(1)).get("host");E({name:"geetest/auto-open",condition:t=>t.enabled&&t.geetest_auto_open&&!t.disabled_hosts.includes(n),ready:J,start:Z,quit:ee,running:te}),E({name:"geetest/auto-solve",condition:t=>t.enabled&&t.geetest_auto_solve&&!t.disabled_hosts.includes(n),ready:de,start:ge,quit:me,running:pe})}R(Te);})();
