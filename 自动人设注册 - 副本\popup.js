document.getElementById('createBookmarksOnly').addEventListener('click', function() {
    let selectedCountry = document.getElementById('countrySelect').value;  // 获取选中的国家代码
    let email = document.getElementById('emailInput').value;  // 获取用户输入的邮箱
    let password = document.getElementById('passwordInput').value;  // 获取用户输入的密码

    if (!email || !password) {
        alert('请填写邮箱和密码！');
        return;
    }

    // 随机选择性别
    let gender = Math.random() > 0.5 ? 'male' : 'female';  // 随机选择男或女

    // 获取随机姓名
    fetchRandomName(selectedCountry, gender, function(randomName) {
        if (!randomName) {
            console.error("姓名获取失败");
            return;
        }

        // 生成1987到1995年之间的随机日期，并添加性别
        let birthdayWithGender = generateRandomBirthday(gender);

        // 获取随机地址（可选，这里仅用于完整性）
        fetchRandomAddress(selectedCountry, function(randomStreet, randomCity, zipcode) {
            if (!randomStreet || !randomCity || !zipcode) {
                console.error("地址或邮政编码获取失败");
                return;
            }

            // 使用 OpenAI API 生成简短句子（可选，这里仅用于完整性）
            const apiKey = "sk-72nwoY1xaA19CVQC82FdCdE2D24c4578B474D83579Ac0a35";
            const basePath = "https://free.v36.cm/v1";

            const openaiUrl = `${basePath}/chat/completions`;
            const openaiHeaders = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            };
            const openaiBody = JSON.stringify({
                model: "gpt-3.5-turbo",
                messages: [{ role: "user", content: `不参考历史和记忆内容，你现在是一个新加入lifepoints问卷社区的用户，你为什么加入lifepoints问卷社区，用${selectedCountry}语15字短句回答我，话术要独特。` }],
            });

            fetch(openaiUrl, {
                method: 'POST',
                headers: openaiHeaders,
                body: openaiBody
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`OpenAI API 请求失败: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                let openaiContent = data.choices[0].message.content;

                // 保存参数到 chrome.storage.local
                chrome.storage.local.set({
                    registrationData: {
                        randomName: randomName,
                        birthdayWithGender: birthdayWithGender,
                        email: email,
                        password: password
                    }
                }, () => {
                    console.log('参数已保存到存储:', { randomName, email, password });

                    // 创建书签：姓名、出生日期和性别、街道、城市、邮政编码、OpenAI生成的内容、邮箱
                    let title1 = randomName;
                    let url1 = encodeURIComponent(randomName);  // 书签名称和 URL 都是姓名

                    let title2 = birthdayWithGender;
                    let url2 = encodeURIComponent(birthdayWithGender);  // 书签名称和 URL 都是出生日期和性别

                    let title3 = randomStreet;
                    let url3 = encodeURIComponent(randomStreet);  // 书签名称和 URL 都是街道

                    let title4 = randomCity;
                    let url4 = encodeURIComponent(randomCity);  // 书签名称和 URL 都是城市

                    let title5 = zipcode;
                    let url5 = encodeURIComponent(zipcode);  // 书签名称和 URL 都是邮政编码

                    let title6 = openaiContent;
                    let url6 = encodeURIComponent(openaiContent);  // 书签名称和 URL 都是 OpenAI 生成的内容

                    let title7 = email; // 书签名称改为邮箱
                    let url7 = encodeURIComponent(`邮箱: ${email}, 密码: ${password}`); // 书签 URL 包含邮箱和密码

                    // 查找文件夹"资料"是否存在
                    chrome.bookmarks.search({ title: "资料" }, function(result) {
                        if (result.length === 0) {
                            // 文件夹"资料"不存在，创建文件夹并指定父级为书签栏（parentId = "1"）并设置索引为0
                            chrome.bookmarks.create({
                                'title': "资料",
                                'parentId': '1',  // 将文件夹放在书签栏上
                                'index': 0        // 设置文件夹为书签栏中的第一项
                            }, function(newFolder) {
                                // 文件夹创建成功后，生成书签并放入该文件夹
                                createBookmarkInFolder(newFolder.id, title1, url1, true); // 姓名书签分成两行
                                createBookmarkInFolder(newFolder.id, title2, url2, false);
                                createBookmarkInFolder(newFolder.id, title3, url3, false); // 街道书签
                                createBookmarkInFolder(newFolder.id, title4, url4, false); // 城市书签
                                createBookmarkInFolder(newFolder.id, title5, url5, false); // 邮政编码书签
                                createBookmarkInFolder(newFolder.id, title6, url6, false); // OpenAI 生成的内容书签
                                createBookmarkInFolder(newFolder.id, title7, url7, false, email, password); // 邮箱和密码书签

                                console.log('已生成书签（仅生成书签）');
                            });
                        } else {
                            // 文件夹"资料"已存在，直接在该文件夹内生成书签
                            createBookmarkInFolder(result[0].id, title1, url1, true); // 姓名书签分成两行
                            createBookmarkInFolder(result[0].id, title2, url2, false);
                            createBookmarkInFolder(result[0].id, title3, url3, false); // 街道书签
                            createBookmarkInFolder(result[0].id, title4, url4, false); // 城市书签
                            createBookmarkInFolder(result[0].id, title5, url5, false); // 邮政编码书签
                            createBookmarkInFolder(result[0].id, title6, url6, false); // OpenAI 生成的内容书签
                            createBookmarkInFolder(result[0].id, title7, url7, false, email, password); // 邮箱和密码书签

                            console.log('已生成书签（仅生成书签）');
                        }
                    });
                });
            })
            .catch(error => {
                console.error("OpenAI API 请求失败:", error);
                // 即使 OpenAI API 失败，也保存参数并生成书签
                chrome.storage.local.set({
                    registrationData: {
                        randomName: randomName,
                        birthdayWithGender: birthdayWithGender,
                        email: email,
                        password: password
                    }
                }, () => {
                    console.log('参数已保存到存储（OpenAI 失败）:', { randomName, email, password });
                    chrome.bookmarks.search({ title: "资料" }, function(result) {
                        if (result.length === 0) {
                            chrome.bookmarks.create({
                                'title': "资料",
                                'parentId': '1',  // 将文件夹放在书签栏上
                                'index': 0        // 设置文件夹为书签栏中的第一项
                            }, function(newFolder) {
                                let title1 = randomName;
                                let url1 = encodeURIComponent(randomName);  // 书签名称和 URL 都是姓名

                                let title2 = birthdayWithGender;
                                let url2 = encodeURIComponent(birthdayWithGender);  // 书签名称和 URL 都是出生日期和性别

                                let title3 = randomStreet;
                                let url3 = encodeURIComponent(randomStreet);  // 书签名称和 URL 都是街道

                                let title4 = randomCity;
                                let url4 = encodeURIComponent(randomCity);  // 书签名称和 URL 都是城市

                                let title5 = zipcode;
                                let url5 = encodeURIComponent(zipcode);  // 书签名称和 URL 都是邮政编码

                                let title6 = "OpenAI 内容生成失败";
                                let url6 = encodeURIComponent("OpenAI 内容生成失败");  // OpenAI 生成的内容书签（失败时使用默认值）

                                let title7 = email; // 书签名称改为邮箱
                                let url7 = encodeURIComponent(`邮箱: ${email}, 密码: ${password}`); // 书签 URL 包含邮箱和密码

                                createBookmarkInFolder(newFolder.id, title1, url1, true); // 姓名书签分成两行
                                createBookmarkInFolder(newFolder.id, title2, url2, false);
                                createBookmarkInFolder(newFolder.id, title3, url3, false); // 街道书签
                                createBookmarkInFolder(newFolder.id, title4, url4, false); // 城市书签
                                createBookmarkInFolder(newFolder.id, title5, url5, false); // 邮政编码书签
                                createBookmarkInFolder(newFolder.id, title6, url6, false); // OpenAI 生成的内容书签
                                createBookmarkInFolder(newFolder.id, title7, url7, false, email, password); // 邮箱和密码书签

                                console.log('已生成书签（仅生成书签，OpenAI 失败）');
                            });
                        } else {
                            let title1 = randomName;
                            let url1 = encodeURIComponent(randomName);  // 书签名称和 URL 都是姓名

                            let title2 = birthdayWithGender;
                            let url2 = encodeURIComponent(birthdayWithGender);  // 书签名称和 URL 都是出生日期和性别

                            let title3 = randomStreet;
                            let url3 = encodeURIComponent(randomStreet);  // 书签名称和 URL 都是街道

                            let title4 = randomCity;
                            let url4 = encodeURIComponent(randomCity);  // 书签名称和 URL 都是城市

                            let title5 = zipcode;
                            let url5 = encodeURIComponent(zipcode);  // 书签名称和 URL 都是邮政编码

                            let title6 = "OpenAI 内容生成失败";
                            let url6 = encodeURIComponent("OpenAI 内容生成失败");  // OpenAI 生成的内容书签（失败时使用默认值）

                            let title7 = email; // 书签名称改为邮箱
                            let url7 = encodeURIComponent(`邮箱: ${email}, 密码: ${password}`); // 书签 URL 包含邮箱和密码

                            createBookmarkInFolder(result[0].id, title1, url1, true); // 姓名书签分成两行
                            createBookmarkInFolder(result[0].id, title2, url2, false);
                            createBookmarkInFolder(result[0].id, title3, url3, false); // 街道书签
                            createBookmarkInFolder(result[0].id, title4, url4, false); // 城市书签
                            createBookmarkInFolder(result[0].id, title5, url5, false); // 邮政编码书签
                            createBookmarkInFolder(result[0].id, title6, url6, false); // OpenAI 生成的内容书签
                            createBookmarkInFolder(result[0].id, title7, url7, false, email, password); // 邮箱和密码书签

                            console.log('已生成书签（仅生成书签，OpenAI 失败）');
                        }
                    });
                });
            });
        });
    });
});


// 获取随机地址和邮政编码（调用外部 API，根据选择的国家）
function fetchRandomAddress(country, callback) {
    // 首先获取真实的城市和邮编
    fetch('https://ipinfo.io/json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`IP查询失败: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 保存 ipinfo 返回的城市和邮政编码
            const city = data.city || '未知城市';
            const zipcode = data.postal || '000000';
            
            // 然后使用 Faker API 生成对应国家的街道名
            return fetch(`https://fakerapi.it/api/v2/addresses?_quantity=1&_locale=${country}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Faker API请求失败: ${response.status}`);
                    }
                    return response.json();
                })
                .then(fakerData => {
                    if (fakerData.data && fakerData.data.length > 0) {
                        const street = fakerData.data[0].street || '未知街道';
                        // 生成1-999之间的随机门牌号
                        const houseNumber = Math.floor(Math.random() * 999) + 1;
                        
                        // 根据国家调整门牌号的格式
                        let finalStreet;
                        if (country === 'ja_JP') {
                            finalStreet = `${street} ${houseNumber}号`;
                        } else if (country === 'zh_HK') {
                            finalStreet = `${street}${houseNumber}號`;
                        } else {
                            finalStreet = `${street} ${houseNumber}`;
                        }
                        
                        // 返回组合后的地址信息，使用 ipinfo 的城市和邮编
                        callback(finalStreet, city, zipcode);
                    } else {
                        throw new Error('Faker API返回数据格式错误');
                    }
                });
        })
        .catch(error => {
            console.error('获取地址时出错:', error);
            // 失败时回退到随机生成街道和邮编，同样添加随机门牌号
            const houseNumber = Math.floor(Math.random() * 999) + 1;
            callback(`随机街道${Math.floor(Math.random()*100)} ${houseNumber}`, '默认城市', '00000' + Math.floor(Math.random()*10));
        });
}

// 获取随机姓名（调用外部 API，根据选择的国家和性别）
function fetchRandomName(country, gender, callback) {
    const url = `https://fakerapi.it/api/v2/users?_quantity=1&_locale=${country}&_gender=${gender}`; // 使用动态性别
    console.log("请求的 URL:", url); // 打印请求的 URL，检查是否正确

    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`API 请求失败: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("API 返回的数据:", data);  // 打印返回的数据，帮助调试
            if (data.data && data.data.length > 0) {
                const firstName = data.data[0].firstname;  // 获取生成的名
                const lastName = data.data[0].lastname;    // 获取生成的姓
                if (firstName && lastName) {
                    const fullName = `${firstName} ${lastName}`;  // 拼接名和姓
                    callback(fullName);
                } else {
                    console.error('API 返回数据中没有有效的姓名字段');
                    callback(null);  // 如果返回数据不正确，返回 null
                }
            } else {
                console.error('API 返回的数据不正确，数据为空或格式错误');
                callback(null);  // 如果返回数据不正确，返回 null
            }
        })
        .catch(error => {
            console.error('获取姓名时出错:', error);
            callback(null);  // 如果有错误，返回 null
        });
}

// 生成1990到1995年之间的随机日期，并添加性别
function generateRandomBirthday(gender) {
    let minYear = 1990;
    let maxYear = 1995;

    // 随机选择一个出生年份
    let year = Math.floor(Math.random() * (maxYear - minYear + 1)) + minYear;

    // 随机选择生日月份和日期
    let month = Math.floor(Math.random() * 12) + 1; // 1-12月
    let day = Math.floor(Math.random() * 28) + 1;   // 1-28日

    // 返回格式化的日期和性别：YYYY-MM-DD 性别
    return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')} ${gender === 'male' ? '男' : '女'}`;
}

// 在指定文件夹中创建书签的函数
function createBookmarkInFolder(folderId, title, url, isNameBookmark, email = null, password = null) {
    let htmlContent;
    if (isNameBookmark) {
        // 如果是姓名书签，分成两行显示
        const firstName = title.split(' ')[0];
        const lastName = title.split(' ')[1];

        htmlContent = `
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${firstName}</h1>
                    <h1>${lastName}</h1>
                    <button onclick="copyToClipboard('${firstName}')">复制名字</button>
                    <button onclick="copyToClipboard('${lastName}')">复制姓氏</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    console.log('已复制到剪贴板');
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                console.log('已复制到剪贴板');
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            // 添加到页面
                            document.body.appendChild(notification);

                            // 设置样式
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            // 显示通知
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            // 隐藏通知
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;
    } else if (email && password) {
        // 如果是合并的邮箱和密码书签
        htmlContent = `
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${title}</h1>
                    <p>邮箱: ${email}</p>
                    <p>密码: ${password}</p>
                    <button onclick="copyToClipboard('${email}')">复制邮箱</button>
                    <button onclick="copyToClipboard('${password}')">复制密码</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    console.log('已复制到剪贴板');
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                console.log('已复制到剪贴板');
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            // 添加到页面
                            document.body.appendChild(notification);

                            // 设置样式
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            // 显示通知
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            // 隐藏通知
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;
    } else {
        // 其他书签，显示标题和 URL
        let displayTitle = title;
        let copyText = title;

        if (title.includes('男') || title.includes('女')) {
            // 如果标题包含"男"或"女"，假设是生日和性别格式
            const [date, gender] = title.split(' ');
            const [year, month, day] = date.split('-');
            const genderCode = gender === '男' ? '2006' : '2007';
            copyText = `${day}${month}${year}`;
        }

        htmlContent = `
            <html>
                <head><title>${url}</title></head>
                <body>
                    <h1>${displayTitle}</h1>
                    <button onclick="copyToClipboard('${copyText}')">复制</button>
                    <script>
                        function copyToClipboard(text) {
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(text).then(() => {
                                    console.log('标题已复制到剪贴板');
                                    showCopyNotification('已复制到剪贴板');
                                }).catch(err => {
                                    console.error('复制失败: ', err);
                                    showCopyNotification('复制失败: ' + err);
                                });
                            } else {
                                // Fallback to document.execCommand
                                const textarea = document.createElement('textarea');
                                textarea.value = text;
                                document.body.appendChild(textarea);
                                textarea.select();
                                document.execCommand('copy');
                                document.body.removeChild(textarea);
                                console.log('已复制到剪贴板');
                                showCopyNotification('已复制到剪贴板');
                            }
                        }

                        function showCopyNotification(message) {
                            // 创建通知元素
                            const notification = document.createElement('div');
                            notification.className = 'copy-notification';
                            notification.textContent = message;

                            // 添加到页面
                            document.body.appendChild(notification);

                            // 设置样式
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#4CAF50';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 20px';
                            notification.style.borderRadius = '5px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.opacity = '0';
                            notification.style.transition = 'opacity 0.5s';

                            // 显示通知
                            setTimeout(() => {
                                notification.style.opacity = '1';
                            }, 10);

                            // 隐藏通知
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        }
                    </script>
                </body>
            </html>`;
    }

    chrome.bookmarks.create({
        'parentId': folderId,  // 使用文件夹 ID
        'title': title,
        'url': `data:text/html;charset=UTF-8,${encodeURIComponent(htmlContent)}`
    });
}