(()=>{var y=chrome;var L="https://api.nopecha.com",m="https://www.nopecha.com",W="https://developers.nopecha.com",Me={doc:{url:W,automation:{url:`${W}/guides/extension_advanced/#automation-build`}},api:{url:L,recognition:{url:`${L}/recognition`},status:{url:`${L}/status`}},www:{url:m,annoucement:{url:`${m}/json/announcement.json`},demo:{url:`${m}/captcha`,recaptcha:{url:`${m}/captcha/recaptcha`},funcaptcha:{url:`${m}/captcha/funcaptcha`},awscaptcha:{url:`${m}/captcha/awscaptcha`},textcaptcha:{url:`${m}/captcha/textcaptcha`},turnstile:{url:`${m}/captcha/turnstile`},perimeterx:{url:`${m}/captcha/perimeterx`},geetest:{url:`${m}/captcha/geetest`},lemincaptcha:{url:`${m}/captcha/lemincaptcha`}},manage:{url:`${m}/manage`},pricing:{url:`${m}/pricing`},setup:{url:`${m}/setup`}},discord:{url:`${m}/discord`},github:{url:`${m}/github`,release:{url:`${m}/github/release`}}};function F(e){let n=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(t=>t.charCodeAt(0));return X(n)}var z=new Uint32Array(256);for(let e=256;e--;){let n=e;for(let t=8;t--;)n=n&1?3988292384^n>>>1:n>>>1;z[e]=n}function X(e){let n=-1;for(let t of e)n=n>>>8^z[n&255^t];return(n^-1)>>>0}async function h(e,n){let t=""+[+new Date,performance.now(),Math.random()],[a,c]=await new Promise(i=>{y.runtime.sendMessage([t,e,...n],i)});if(a===F(t))return c}function Y(){let e;return n=>e||(e=n().finally(()=>e=void 0),e)}var he=Y(),_;function K(){return he(async()=>(_||(_=await h("settings::get",[])),_))}function Q(e){_&&(_={..._,...e},j(_))}function $(){return _}function p(e){return new Promise(n=>setTimeout(n,e))}var V=[];function P(e,n){e.timedout=!1,V.push(e);let t,a=setInterval(async()=>{await G(e,$())||(clearTimeout(t),clearInterval(a))},400);n&&(t=setTimeout(()=>clearInterval(a),n),e.timedout=!0)}async function G(e,n){if(e.timedout)return!1;let t=e.condition(n);if(t===e.running())return!1;if(!t&&e.running())return e.quit(),!1;if(t&&!e.running()){for(;!e.ready();)await p(200);return e.start(),!1}}function j(e){V.forEach(n=>G(n,e))}function J(){y.runtime.connect({name:"stream"}).onMessage.addListener(n=>{n.event==="settingsUpdate"&&Q(n.settings)})}function q(e){if(document.readyState!=="loading")setTimeout(e,0);else{let n;n=()=>{removeEventListener("DOMContentLoaded",n),e()},addEventListener("DOMContentLoaded",n)}}var be=(e,n,t)=>new MouseEvent(e,{bubbles:!0,cancelable:!0,view:window,detail:1,screenX:n,screenY:t,clientX:n,clientY:t,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:null});function Z(e,{events:n=null,x:t=null,y:a=null}={}){let c=document.querySelector(e);if(!c)return;if(t===null||a===null){let o=c.getBoundingClientRect();t=o.left+o.width/2,a=o.top+o.height/2}let i=n??["mouseover","mousedown","mouseup","click"];for(let o of i)c.dispatchEvent(be(o,t,a))}var A,D=!1;function ee(){return!!document.querySelector('input[name="lemin_challenge_id"]')}function te(){D=!0;let e=()=>{document.querySelector(".lemin-captcha-input-box").classList.contains("pending")||ve()};A=new MutationObserver(e),A.observe(document.querySelector(".lemin-captcha-input-box"),{attributes:!0,attributeFilter:["class"]}),e()}function ne(){A.disconnect(),D=!1}function oe(){return D}async function ve(){await p(400),Z('span[class="checkmark"]')}function I(e,n){let t=document.createElement("canvas");return t.width=e,t.height=n,t}function ae(e){return e.toDataURL("image/jpeg").replace(/data:image\/[a-z]+;base64,/g,"")}function _e(e){try{e.getContext("2d").getImageData(0,0,1,1)}catch{return!0}return!1}async function O(e,n,t=1e4){if(!n&&!e.complete&&!await new Promise(o=>{let s=setTimeout(()=>{o(!1)},t);e.addEventListener("load",()=>{clearTimeout(s),o(!0)})}))return;let a=I(e.naturalWidth||n?.clientWidth,e.naturalHeight||n?.clientHeight);return a.getContext("2d").drawImage(e,0,0),!_e(a)&&a}async function ie(e){let t=getComputedStyle(e).backgroundImage;if(!t||t==="none")if("src"in e&&e.src)t=`url("${e.src}")`;else return;if("computedStyleMap"in e&&!/url\(["']https?:\/\//.test(t)){let r=e.computedStyleMap().get("background-image");if(r instanceof CSSImageValue){let d=await O(r,e);if(d)return d}}let a=/"(.+)"/.exec(t);if(!a)return;t=a[1];let c=document.createElement("a");if(c.href=t,new URL(c.href).origin===document.location.origin){let r=new Image;r.crossOrigin="anonymous",r.src=t;let d=await O(r);if(d)return d}let i=await h("fetch::asData",[t,{}]),o=new Image;o.crossOrigin="anonymous",o.src=i.data;let s=await O(o);if(s)return s}function re(e,n="",t=""){let a=e.getContext("2d").getImageData(0,0,e.width,e.height),c=I(e.width,e.height),i=c.getContext("2d");i.putImageData(a,0,0);let o=75,s=75,r=e.width,d=e.height,l=r/d,u=r,g=d;u>o&&(u=o,g=u/l),g>s&&(g=s,u=g*l);function C(b,H,k,N){let v=10,B=N,M,w;if(b.font=`${B}px Arial`,w=b.measureText(H).width,w<=k)return`${B}px Arial`;for(;B-v>.1;)M=(B+v)/2,b.font=`${M}px Arial`,w=b.measureText(H).width,w>k?B=M:v=M;for(;w<k&&v<N;)v+=.1,b.font=`${v.toFixed(1)}px Arial`,w=b.measureText(H).width;return w>k&&(v-=.1),`${v.toFixed(1)}px Arial`}i.font=C(i,t,r,200),i.textBaseline="top";let f=i.measureText(t),R=f.width,E=f.actualBoundingBoxAscent+f.actualBoundingBoxDescent;i.fillStyle="white",i.fillRect(0,0,R,E),i.fillStyle="black",i.fillText(t,0,0);let x=c.toDataURL(),S=new Image;S.onload=function(){let b=[`background: url(${x}) no-repeat;`,`padding: ${g}px ${u}px;`,"background-size: contain;","font-size: 1px;"].join(" ");console.log(`%c ${n}`,b)},S.src=x}function we(e,n,t,a){let c=(a*n+t)*4;return[e[c],e[c+1],e[c+2]]}function xe(e,n){return e.every(t=>t<=n)}function ye(e,n){return e.every(t=>t>=n)}function ce(e,n=0,t=230,a=.99){let c=e.getContext("2d"),i=c.canvas.width,o=c.canvas.height;if(i===0||o===0)return!0;let s=c.getImageData(0,0,i,o).data,r=0;for(let l=0;l<o;l++)for(let u=0;u<i;u++){let g=we(s,i,u,l);(xe(g,n)||ye(g,t))&&r++}return r/(i*o)>a}function se(e,n=500,t=500){let a=e.getContext("2d"),c=a.canvas.width,i=a.canvas.height,o=a.getImageData(0,0,c,i),s=o.width,r=o.height,d=s/r,l=s,u=r;if(l>n&&(l=n,u=Math.floor(l/d)),u>t&&(u=t,l=Math.floor(u*d)),l===s&&u===r)return e;let g=I(s,r);g.getContext("2d").putImageData(o,0,0);let f=I(l,u);return f.getContext("2d").drawImage(g,0,0,s,r,0,0,l,u),f}async function ue(e,n){let t={v:y.runtime.getManifest().version,key:Ce(e)};return t.url=await h("tab::getURL",[]),t}function Ce(e){return!e.keys||!e.keys.length?e.key:e.keys[Math.floor(Math.random()*e.keys.length)]}var U,T=!1;function me(){return!!document.querySelector(".lemin-captcha-popup")}function ge(){T=!0,U=new MutationObserver(e=>{e[0].addedNodes.length>0&&de()}),U.observe(document.querySelector(".lemin-captcha-popup"),{childList:!0}),document.querySelector(".lemin-captcha-popup")&&de()}function pe(){U.disconnect(),T=!1}function fe(){return T}var le=!1;async function de(){if(le)return;le=!0;let e;for(;T;){let n=$(),t=Date.now().valueOf(),a=document.querySelector("#lemin-captcha-caption")?.textContent;if(!a){e=void 0,await p(1e3);continue}let c=512,i=document.querySelector(".lemin-captcha-popup").querySelectorAll('img[name*="image"]')?.[0],o=await Se(c,i);if(!o){e=void 0,await p(1e3);continue}let s=`${a}:${o}`;if(s===e){await p(1e3);continue}e=s;let r=await h("api::recognition",[{type:"hcaptcha_area_select",task:a,image_data:[o],...await ue(n)}]);if(!r||"error"in r){document.querySelector(".lemincaptcha_refresh")?.click(),await p(3e3);continue}let d=new Date().valueOf();if(n.lemincaptcha_solve_delay){let u=n.lemincaptcha_solve_delay_time-d+t;u>0&&await p(u)}let l=r.data;l?(await Be({x:parseInt(l.x),y:parseInt(l.y)}),await p(5e3),document.querySelector(".verify-button")?.click()):(document.querySelector(".lemincaptcha_refresh")?.click(),await p(3e3))}}async function Se(e,n){let t=await ie(n);if(t&&(re(t,"[@nope/lemincaptcha]",`${t.width}x${t.height}`),!ce(t)))return e!==null&&(t=se(t,e,e)),ae(t)}function Be({x:e,y:n,duration:t=1e3,framerate:a=60}={}){return new Promise(c=>{let i=document.querySelectorAll('img[name*="image"]'),o=i?.[0]?.parentElement,s=i?.[1]?.parentElement;if(!s||!o)return console.error("Draggable or $target element not found");let r=o.getBoundingClientRect(),d=s.getBoundingClientRect(),l=d.left+window.scrollX,u=d.top+window.scrollY,g=r.left+window.scrollX+r.width*e/100,C=r.top+window.scrollY+r.height*n/100,f=t/1e3*a,R=(g-l)/f,E=(C-u)/f,x=0;function S(){x++<=f&&(s.dispatchEvent(new MouseEvent("mousemove",{clientX:l+R*x,clientY:u+E*x,bubbles:!0})),requestAnimationFrame(S))}s.dispatchEvent(new MouseEvent("mousedown",{clientX:l,clientY:u,bubbles:!0})),requestAnimationFrame(S),setTimeout(()=>{s.dispatchEvent(new MouseEvent("mouseup",{clientX:g,clientY:C,bubbles:!0}))},t),setTimeout(()=>{c(!0)},t+100)})}async function ke(){J(),await K(),await h("tab::registerDetectedCaptcha",["lemincaptcha"]);let n=new URLSearchParams(location.hash.substring(1)).get("host");P({name:"lemincaptcha/auto-open",condition:t=>t.enabled&&t.lemincaptcha_auto_open&&!t.disabled_hosts.includes(n),ready:ee,start:te,quit:ne,running:oe}),P({name:"lemincaptcha/auto-solve",condition:t=>t.enabled&&t.lemincaptcha_auto_solve&&!t.disabled_hosts.includes(n),ready:me,start:ge,quit:pe,running:fe})}q(ke);})();
