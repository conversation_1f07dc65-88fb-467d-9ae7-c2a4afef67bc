(function() {
    // 动态注入脚本到页面上下文
    function injectScript(fn) {
        var script = document.createElement('script');
        script.textContent = '(' + fn.toString() + ')();';
        document.documentElement.appendChild(script);
        script.parentNode.removeChild(script);
    }
    // 劫持逻辑
    function hookRequest() {
        // 拦截fetch
        const origFetch = window.fetch;
        window.fetch = function(input, init) {
            if (init && init.body) {
                try {
                    // 处理字符串类型body
                    if (typeof init.body === "string" && init.body.includes("fingerprint")) {
                        let bodyObj = JSON.parse(init.body);
                        bodyObj.fingerprint = "";
                        init.body = JSON.stringify(bodyObj);
                    }
                    // 处理对象类型body
                    if (typeof init.body === "object" && init.body !== null) {
                        if (init.body instanceof FormData) {
                            if (init.body.has && init.body.has("fingerprint")) {
                                init.body.set("fingerprint", "");
                            }
                        } else if (Object.prototype.hasOwnProperty.call(init.body, "fingerprint")) {
                            init.body.fingerprint = "";
                        }
                    }
                } catch(e) {}
            }
            return origFetch.apply(this, arguments);
        };
        // 拦截XMLHttpRequest
        const origOpen = XMLHttpRequest.prototype.open;
        const origSend = XMLHttpRequest.prototype.send;
        XMLHttpRequest.prototype.open = function(method, url) {
            this._url = url;
            return origOpen.apply(this, arguments);
        };
        XMLHttpRequest.prototype.send = function(body) {
            try {
                if (body) {
                    // 处理字符串类型body
                    if (typeof body === "string" && body.includes("fingerprint")) {
                        let bodyObj = JSON.parse(body);
                        bodyObj.fingerprint = "";
                        body = JSON.stringify(bodyObj);
                    }
                    // 处理对象类型body
                    if (typeof body === "object" && body !== null) {
                        if (body instanceof FormData) {
                            if (body.has && body.has("fingerprint")) {
                                body.set("fingerprint", "");
                            }
                        } else if (Object.prototype.hasOwnProperty.call(body, "fingerprint")) {
                            body.fingerprint = "";
                        }
                    }
                }
            } catch(e) {}
            return origSend.call(this, body);
        };
    }
    injectScript(hookRequest);
})();