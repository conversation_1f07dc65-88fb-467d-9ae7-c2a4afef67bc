div,p {
  box-sizing: border-box;
 }
.immersive-translate-resizable::-webkit-scrollbar{
  display:none;
}
.immersive-translate-resizable:focus::-webkit-scrollbar,.immersive-translate-resizable:active::-webkit-scrollbar {
  display: block;
}
.immersive-translate-resizable {
    scrollbar-width: none; /* Firefox 64 */
    -ms-overflow-style: none;
  box-sizing: border-box;
  outline-width: thin;
  resize: none;
}
.immersive-translate-resizable:focus {
    resize: both;
}
/* hover effect: border  */
/* .immersive-translate-resizable:hover { */
  /* border: 1px solid blue; */
    /* outline: thin dashed #0969da; */

/* } */
.immersive-translate-resizable:active,.immersive-translate-resizable:focus {
  /* border: 1px solid blue; */
    /* outline: thin solid red; */
    scrollbar-width:thin ; /* Firefox 64 */
    -ms-overflow-style: thin;


}

.immersive-translate-draggable-box {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 20px;
  height: 20px;
  z-index: 1000000000;
  /* background-color:blue; */
}

.immersive-translate-draggable-box:hover {
  cursor: move;
}

.immersive-translate-close-box {
  cursor: pointer;
    z-index: 100000;

  right: 1px;
  top: 1px;
  position: absolute;
}

.close-icon {
  visibility: hidden;
  position: relative;
  width: 16px;
  height: 16px;

}
.immersive-translate-close-box:hover .close-icon {
  visibility: visible;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 2px;
  background: rgb(225,81,65);
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}




.translate-pending {
  background-image: url(images/loading.svg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 16px 16px;
  transition-property: background-size;
  transition-delay: var(--loading-icon-delay);
  opacity: 0.2;
}

.translate-retry {
  cursor: pointer;
}

.translate-retry::after {
  content: "🔄";
  position: absolute;
  top: 50%;
  right: -14px;
  display: block;
  transform: translate(100%, -50%);
}
.immersive-translate-text-layer p {
  line-height: 1.3;
  margin-bottom: 0.5rem;
  transform-origin: left top;
}
@media all and (max-width: 560px) {
  .immersive-translate-text-layer p {
    margin-bottom: 0rem;
  }
}
.immersive-translate-text-layer {
  opacity: 1 !important;
}
.immersive-translate-text-layer ::-moz-selection {
  background: #bfc5fc !important;
}
.immersive-translate-text-layer ::selection {
  background: #bfc5fc !important;
}
.pdfViewer.scrollHorizontal,
.spread {
  white-space: normal;
  /* white-space: nowrap; */
}

.immersive-translate-text-layer :is(span, br) {
  background-color: white;
  padding: 2px 1px;
}

.immersive-translate-text-layer .markedContent {
  position: unset !important;
}

.immersive-translate-annotation-layer .linkAnnotation {
  border: unset !important;
}