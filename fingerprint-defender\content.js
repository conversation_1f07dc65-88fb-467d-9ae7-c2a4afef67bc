function injectScript(fn) {
  const script = document.createElement('script');
  script.textContent = '(' + fn.toString() + ')();';
  document.documentElement.appendChild(script);
  script.remove();
}

injectScript(function() {
  // 伪造 User-Agent
  try {
    Object.defineProperty(navigator, 'userAgent', {
      get: () => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      configurable: true
    });
  } catch (e) {}

  // 伪造语言
  try {
    Object.defineProperty(navigator, 'language', {
      get: () => "en-US",
      configurable: true
    });
    Object.defineProperty(navigator, 'languages', {
      get: () => ["en-US", "en"],
      configurable: true
    });
  } catch (e) {}

  // 伪造屏幕分辨率
  try {
    Object.defineProperty(window.screen, 'width', { get: () => 1920, configurable: true });
    Object.defineProperty(window.screen, 'height', { get: () => 1080, configurable: true });
  } catch (e) {}

  // 伪造插件列表（返回空数组更安全）
  try {
    Object.defineProperty(navigator, 'plugins', {
      get: () => {
        const fakePluginArray = {
          length: 0,
          item: () => null,
          namedItem: () => null
        };
        return fakePluginArray;
      },
      configurable: true
    });
  } catch (e) {}

  // 伪造 WebGL 指纹
  try {
    const getParameterProxy = new Proxy(WebGLRenderingContext.prototype.getParameter, {
      apply: function(target, ctx, args) {
        if (args[0] === 37445) return "Intel Inc.";
        if (args[0] === 37446) return "Intel Iris OpenGL Engine";
        return Reflect.apply(target, ctx, args);
      }
    });
    WebGLRenderingContext.prototype.getParameter = getParameterProxy;
  } catch (e) {}

  // 伪造 Canvas 指纹
  try {
    const toDataURLProxy = new Proxy(HTMLCanvasElement.prototype.toDataURL, {
      apply: function(target, ctx, args) {
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA";
      }
    });
    HTMLCanvasElement.prototype.toDataURL = toDataURLProxy;
  } catch (e) {}

  // 屏蔽 WebRTC
  try {
    window.RTCPeerConnection = undefined;
    window.webkitRTCPeerConnection = undefined;
  } catch (e) {}
});
