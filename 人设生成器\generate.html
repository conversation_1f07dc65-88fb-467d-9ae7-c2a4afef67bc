<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LifePoints Registration Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f9f9f9;
            margin: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }
        .container {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            margin-top: 50px;
        }
        select, input, button {
            margin: 10px 0;
            padding: 12px;
            width: 100%;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        h2 {
            color: #333;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>LifePoints人设书签生成器</h2>
        <select id="countrySelect">
            <option value="en_US">美国 (US)</option>
            <option value="en_GB">英国 (UK)</option>
            <option value="fr_FR">法国 (FR)</option>
            <option value="de_DE">德国 (DE)</option>
            <option value="ja_JP">日本 (JP)</option>
            <option value="en_IE">爱尔兰 (IE)</option>
            <!-- 其他国家选项 -->
        </select>
        <input type="email" id="emailInput" placeholder="输入邮箱" required>
        <input type="password" id="passwordInput" placeholder="输入密码" required>
        <button id="createBookmarksOnly">生成资料</button>
    </div>
    <script src="popup.js"></script>
</body>
</html>