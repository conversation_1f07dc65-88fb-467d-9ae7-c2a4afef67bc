import { isElementVisible } from './utils.js';

// 模拟人类交互
const createHumanEvent = (type, element) => {
  const rect = element.getBoundingClientRect();
  const offsetX = rect.width * (0.3 + Math.random() * 0.4);
  const offsetY = rect.height * (0.3 + Math.random() * 0.4);
  return new MouseEvent(type, {
    view: window,
    bubbles: true,
    cancelable: true,
    clientX: rect.left + offsetX,
    clientY: rect.top + offsetY,
    movementX: (Math.random() - 0.5) * 2,
    movementY: (Math.random() - 0.5) * 2
  });
};

const humanDelay = (base = 500) => {
  const jitter = base * 0.3 * (Math.random() - 0.5); 
  return new Promise(resolve => setTimeout(resolve, base * (0.6 + Math.random() * 0.4) + jitter));
};

function generateBezierControlPoints(start, end) {
  const randomFactor1 = 0.15 + Math.random() * 0.25;
  const randomFactor2 = 0.2 + Math.random() * 0.3;
  
  const cp1 = {
    x: start.x + (end.x - start.x) * randomFactor1,
    y: start.y + (end.y - start.y) * randomFactor2,
  };
  const cp2 = {
    x: start.x + (end.x - start.x) * randomFactor2,
    y: start.y + (end.y - start.y) * randomFactor1,
  };
  return { cp1, cp2 };
}

async function enhancedMouseMove(targetElement) {
  let lastX, lastY;
  const start = {
    x: window.innerWidth * Math.random(),
    y: window.innerHeight * Math.random()
  };
  const endRect = targetElement.getBoundingClientRect();
  const end = {
    x: endRect.left + endRect.width * (0.3 + Math.random() * 0.4),
    y: endRect.top + endRect.height * (0.3 + Math.random() * 0.4)
  };
  const { cp1, cp2 } = generateBezierControlPoints(start, end);
  const steps = 15 + Math.floor(Math.random() * 10);
  for (let t = 0; t <= 1; t += 1 / steps) {
    const x = (1 - t) ** 3 * start.x + 3 * (1 - t) ** 2 * t * cp1.x + 3 * (1 - t) * t ** 2 * cp2.x + t ** 3 * end.x;
    const y = (1 - t) ** 3 * start.y + 3 * (1 - t) ** 2 * t * cp1.y + 3 * (1 - t) * t ** 2 * cp2.y + t ** 3 * end.y;
    const tremor = { x: (Math.random() - 0.5) * 2, y: (Math.random() - 0.5) * 2 };
    const velocity = Math.sin(t * Math.PI) * (0.8 + Math.random() * 0.2);
    await humanDelay(30 * velocity);
    document.dispatchEvent(new MouseEvent('mousemove', {
      clientX: x + tremor.x,
      clientY: y + tremor.y,
      movementX: (x - (lastX || x)) * (0.9 + Math.random() * 0.2),
      movementY: (y - (lastY || y)) * (0.9 + Math.random() * 0.2)
    }));
    [lastX, lastY] = [x, y];
  }
}

async function enhancedHumanClick(element, fastMode = false) {
  if (fastMode) {
    if (element.type === 'radio' || element.type === 'checkbox') {
      element.checked = !element.checked;
    }
    element.click();
    element.dispatchEvent(new Event('change', { bubbles: true }));
    return;
  }
  const originalAriaDisabled = element.getAttribute('aria-disabled');
  
  // 新增人类行为特征
  const originalCursor = document.body.style.cursor;
  document.body.style.cursor = 'pointer';  // 模拟光标变化
  await humanDelay(20 + Math.random() * 30);  // 初始反应时间

  // 更自然的鼠标移动轨迹
  await enhancedMouseMove(element);
  
  // 添加多次微移动模拟手部颤动
  for(let i = 0; i < 3; i++) {
    element.dispatchEvent(createHumanEvent('mousemove', element));
    await humanDelay(10 + Math.random() * 15);
  }

  // 模拟点击前的手指抖动
  element.dispatchEvent(createHumanEvent('mouseenter', element));
  await humanDelay(30 + Math.random() * 50);
  element.dispatchEvent(createHumanEvent('mouseover', element));
  
  // 更真实的点击序列
  const mousedownEvent = createHumanEvent('mousedown', element);
  element.dispatchEvent(mousedownEvent);
  await humanDelay(80 + Math.random() * 120);  // 按压持续时间
  
  // 添加点击后的微位置偏移
  const offsetRect = element.getBoundingClientRect();
  const postClickEvent = new MouseEvent('mousemove', {
    clientX: offsetRect.left + offsetRect.width * (0.35 + Math.random() * 0.3),
    clientY: offsetRect.top + offsetRect.height * (0.35 + Math.random() * 0.3)
  });
  document.dispatchEvent(postClickEvent);

  // 更智能的元素状态处理
  if (element.tagName === 'INPUT') {
    const originalState = element.checked;
    element.checked = !originalState;  // 先切换状态
    await humanDelay(15 + Math.random() * 25);
    element.checked = originalState;  // 再恢复状态
    await humanDelay(15 + Math.random() * 25);
  }
  
  // 添加辅助事件提升真实性
  element.dispatchEvent(new FocusEvent('focus'));
  element.click();  // 实际触发点击
  await humanDelay(50 + Math.random() * 100);
  element.dispatchEvent(new FocusEvent('blur'));

  // 恢复原始状态时添加随机延迟
  if (element.hasAttribute('disabled')) {
    await humanDelay(20 + Math.random() * 30);
    element.setAttribute('aria-disabled', originalAriaDisabled);
  }
  
  document.body.style.cursor = originalCursor;  // 恢复光标
  await humanDelay(100 + Math.random() * 200);  // 操作后停顿
}

// 获取可点击元素
function getClickableElement(input, fastMode = false) {
  let label = input.closest('label');
  if (label && isElementVisible(label)) {
    return label;
  }
  const parent = input.parentElement;
  if (parent && isElementVisible(parent) && !parent.hasAttribute('disabled')) {
    return parent;
  }
  label = document.querySelector(`label[for="${input.id}"]`);
  if (label && isElementVisible(label)) {
    return label;
  }
  const inputType = input.type;
  const allSameType = document.querySelectorAll(`input[type="${inputType}"]`);
  const currentIndex = Array.from(allSameType).findIndex(el => el === input);
  const specialElements = Array.from(document.querySelectorAll(
    '[style*="cursor: pointer"][dir="ltr"],' +
    '[style*="cursor:pointer"][dir="ltr"]'
  )).filter(el => isElementVisible(el));
  if (currentIndex !== -1 && specialElements.length > 0) {
    if (allSameType.length > specialElements.length) {
      const specialElementIndex = currentIndex % specialElements.length;
      let validElementIndex = 0;
      let count = 0;
      while (count < currentIndex + 1) {
        if (isElementVisible(allSameType[validElementIndex])) {
          count++;
        }
        validElementIndex++;
      }
      console.log(`多余的radio/checkbox，分配特殊元素 [${specialElementIndex}]`);
      return specialElements[specialElementIndex];
    }
    console.log(`按索引匹配特殊元素 [${currentIndex}]`);
    return specialElements[currentIndex];
  }
  console.log(`未找到有效的可点击元素 for ${input.id}`);
  return null;
}

export {
  createHumanEvent,
  humanDelay,
  enhancedMouseMove,
  enhancedHumanClick,
  getClickableElement
};