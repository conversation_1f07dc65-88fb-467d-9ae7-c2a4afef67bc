<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<!-- saved from url=(0038)https://sa.ktrmr.com/mrIWeb/mrIWeb.srf -->
<html lang="en-AU" class=" fullscreen requestanimationframe raf csscalc supports cssfilters flexwrap csspositionsticky csstransforms3d csstransitions placeholder matchmedia"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta http-equiv="Content-Language" content="en-AU"><meta http-equiv="Expires" content="-1"><meta http-equiv="Pragma" content="no-cache"><meta name="Robots" content="noindex, nofollow"><meta name="Generator" content="Interviewer Server HTML Player 1.0.23..106"><meta name="Template" content="HtmlTemplates/LAF.htm">
		<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests; object-src &#39;none&#39;; base-uri &#39;none&#39;; form-action https: &#39;self&#39;; font-src https: &#39;self&#39;; img-src https: &#39;self&#39; data: blob: android-webview-video-poster:; media-src https: &#39;self&#39; data: blob:; worker-src &#39;self&#39; blob:; script-src https://multimedia.kantaroperations.com https://*.ktrmr.cn https://*.insightexpressai.com https://*.jwpcdn.com https://*.googleapis.com https://*.affectiva.com https://*.millwardbrown.com &#39;self&#39;">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="robots" content="noindex, nofollow, noarchive"><meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0"><title>-</title>
		<script class="imgCache" type="application/json">{"imageCacheBaseString":"/SPSSMR/ImageCache/ImageCache.aspx?Project=MediaReact23V4&amp;File=en-AU/"}</script>
		<style>.loader{position:fixed;width:100%;height:100%;background:#000 center center no-repeat;opacity:.7;z-index:1000;text-align:center}.lds-ellipsis{display:inline-block;position:relative;width:80px;height:80px;top:50%;-ms-transform:translateY(-50%);transform:translateY(-50%)}.lds-ellipsis div{position:absolute;top:33px;width:13px;height:13px;border-radius:50%;animation-timing-function:cubic-bezier(0,1,1,0)}.lds-ellipsis div:nth-child(1){left:8px;animation:lds-ellipsis1 .6s infinite}.lds-ellipsis div:nth-child(2){left:8px;animation:lds-ellipsis2 .6s infinite}.lds-ellipsis div:nth-child(3){left:32px;animation:lds-ellipsis2 .6s infinite}.lds-ellipsis div:nth-child(4){left:56px;animation:lds-ellipsis3 .6s infinite}@keyframes lds-ellipsis1{0%{transform:scale(0)}100%{transform:scale(1)}}@keyframes lds-ellipsis3{0%{transform:scale(1)}100%{transform:scale(0)}}@keyframes lds-ellipsis2{0%{transform:translate(0,0)}100%{transform:translate(24px,0)}}</style>
		<link href="./-_files/bootstrap.min.css" type="text/css" rel="stylesheet"><link href="./-_files/layout.css" type="text/css" rel="stylesheet"><link href="./-_files/favicon.ico" type="text/css" rel="shortcut icon"><link href="./-_files/default.css" type="text/css" rel="stylesheet"><script src="./-_files/vendors~main.js.download" type="text/javascript"></script><script src="./-_files/app_dev.js.download" type="text/javascript"></script><script src="./-_files/jquery.min.js.download" type="text/javascript"></script><script src="./-_files/popper.min.js.download" type="text/javascript"></script><script src="./-_files/bootstrap.min.js.download" type="text/javascript"></script><script src="./-_files/SurveyEngine.js.download" type="text/javascript"></script><script src="./-_files/layout.js.download" type="text/javascript"></script><style type="text/css" data-glamor=""></style><style>[id^='container_'] {min-height: 0rem !important;}.question-component {min-height: 0rem;}</style><script>
  console.log("start patch Notification");

  const OldNotify = window.Notification;
  const newNotify = function (title, opt) {
    const instance = new OldNotify(title, opt);
    try {
      window.postMessage({action: "notification", data: {}});
    } catch (e) {}
    return instance;
  };
  if (OldNotify.prototype) {
    newNotify.prototype = Object.create(OldNotify.prototype);
    newNotify.prototype.constructor = newNotify;
  }
  newNotify.requestPermission = OldNotify.requestPermission.bind(OldNotify);
  newNotify.toString = OldNotify.toString.bind(OldNotify);
  Object.defineProperty(newNotify, 'permission', {
    get: () => OldNotify.permission,
  });
  Object.defineProperty(newNotify, 'maxActions', {
    get: () => OldNotify.maxActions,
  });
  Object.defineProperty(newNotify, 'name', {
    get: () => OldNotify.name,
  });

  window.Notification = newNotify;
  </script><style>label{margin-bottom:0rem !important;}</style></head><body class="survey-page noselect page-loaded"><div></div>
		<span class="mrBannerText" style=""><script class="projInfo" type="application/json">{"projectName":"mediareact23v4","isDebug":"0","id":"LFP01_9172fb90-7870-c74d-12fb-ce59b88f7814","serial":"18316","isTest":"false","cursorInOpens":"0","projectVersion":"1","comp":"Incomplete","deviceType":"Smartphonetouch","sampleSource":"LFP01"}</script><script class="themeInfo" type="application/json">{"themeName" : "default", "themeVersion" : "3.0", "template" : "V4", "RTL" : "False", "cdndomain" : "GlobalCDN"}</script></span>
		<div></div>
		<span class="mrBannerText" style=""><script class="SEJson" type="application/json"><!-- 
{"load":"initSurveyEngine();",
"qJSON":[{"QuestionName":"slice","QuestionFullName":"Q009_IMAG1[{_12}].slice","CustomProps": {"metaType":"rowpicker","answertype":"text","row$all$center":true,"grid$all$width":100,"row$all$titlesize":11,"exextralargewidth":100,"exextrasmallwidth":100,"exsmallwidth":100,"exmediumwidth":100,"exlargewidth":100,"ReportType":"IsShell"},"min":"1","max":"6"}],
"manifestLoc":""
}//--></script></span>
		<div></div>
		<span class="mrBannerText" style=""><script src="./-_files/jquery.min(1).js.download"><!--JS--></script><script src="./-_files/load_dependencies.js.download"><!--JS--></script></span>
	
	
		<form name="mrForm" id="mrForm" action="https://sa.ktrmr.com/mrIWeb/mrIWeb.srf" method="post"><input type="hidden" name="I.Engine" value="engine15"><input type="hidden" name="I.Project" value="MediaReact23V4"><input type="hidden" name="I.Session" value="s1bwa4bf5jwevk6yr54pdxlczxgagaaa"><input type="hidden" name="I.SavePoint" value="Q009_IMAG1[{_12}]"><input type="hidden" name="I.Renderer" value="HTMLPlayer"><span pastedetection="true"></span>
			<div class="loader" style="display: none;"><div class="lds-ellipsis"><div></div><div></div><div></div><div></div></div></div>
			<var id="sharedContent" src="https://multimedia.kantaroperations.com/8016f8b/origin.tns-global.com/solutions/iatools/prod/"><!--x--></var><div class="container" style="">
				<div class="row" id="questions" style=""><div class="questionContainer" questionname="_12">
					<div></div>
					<div class="col-sm-12 question-text no-question-text theme-standard-bg-color2 theme-standard-font-color1" style="margin-bottom: 1px; display: none;"><span class="mrQuestionText" style=""></span></div>
					<div class="questionContainer" questionname="_12.slice" style="display: none;">
						<div class="col-sm-12 question-text theme-standard-bg-color2 theme-standard-font-color1"><div><!--x--></div><div></div><span class="mrQuestionText" style="">Which of these statements do you think apply to <b>Events sponsored by a brand</b>.<br>
							<span class="mrInstruct"><i>Select all that apply.</i></span></span><br></div>
						
						
						
						<div class="col-sm-12 question-component"><div class="mrInstruct questionTextBottomPadding"><!--x--></div><div></div><span style="">
							<span class="mrQuestionTable" style="display:block;">
								<div class="flex-row row"><div class="col-md-4 col-sm-6 col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.0" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_C__4p" id="_Q0_C0" class="mrMultiple form-control form-check-input" style="" value="__4p" openendid="" iscode="" otherid="" isexclusive="false" questionname="_12.slice">
										<label for="_Q0_C0" class="active">
											<span class="mrMultipleText" style="">Are good quality</span>
										</label>
									
								</span></div><div class="col-md-4 col-sm-6 col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.1" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_C__6p" id="_Q0_C1" class="mrMultiple form-control form-check-input" style="" value="__6p" openendid="" iscode="" otherid="" isexclusive="false" questionname="_12.slice">
										<label for="_Q0_C1" class="active">
											<span class="mrMultipleText" style="">Capture my attention</span>
										</label>
									
								</span></div><div class="col-md-4 col-sm-6 col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.2" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_C__2p" id="_Q0_C2" class="mrMultiple form-control form-check-input" style="" value="__2p" openendid="" iscode="" otherid="" isexclusive="false" questionname="_12.slice">
										<label for="_Q0_C2" class="active">
											<span class="mrMultipleText" style="">Are relevant and useful to me</span>
										</label>
									
								</span></div><div class="col-md-4 col-sm-6 col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.3" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_C__3p" id="_Q0_C3" class="mrMultiple form-control form-check-input" style="" value="__3p" openendid="" iscode="" otherid="" isexclusive="false" questionname="_12.slice">
										<label for="_Q0_C3" class="active">
											<span class="mrMultipleText" style="">Are fun and entertaining</span>
										</label>
									
								</span></div><div class="col-md-4 col-sm-6 col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.4" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_C__1p" id="_Q0_C4" class="mrMultiple form-control form-check-input" style="" value="__1p" openendid="" iscode="" otherid="" isexclusive="false" questionname="_12.slice">
										<label for="_Q0_C4" class="active">
											<span class="mrMultipleText" style="">Are trustworthy</span>
										</label>
									
								</span></div><div class="col-md-4 col-sm-6 col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.5" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_C__5p" id="_Q0_C5" class="mrMultiple form-control form-check-input" style="" value="__5p" openendid="" iscode="" otherid="" isexclusive="false" questionname="_12.slice">
										<label for="_Q0_C5" class="active">
											<span class="mrMultipleText" style="">Are innovative</span>
										</label>
									
								</span></div><div class="col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.6" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_Cdkp_D" id="_Q0_C6" class="mrMultiple form-control form-check-input" style="" value="dkp_D" openendid="" iscode="" otherid="" isexclusive="true" questionname="_12.slice">
										<label for="_Q0_C6" class="active">
											<span class="mrMultipleText" style="">Don't know</span>
										</label>
									
								</span></div><div class="col-xs-12 col-border waves-effect waves-light"><span id="Cell.0.7" style="" tabindex="0">
									<div></div>
									<input type="checkbox" name="_Q__12_Qslice_Cnap_D" id="_Q0_C7" class="mrMultiple form-control form-check-input" style="" value="nap_D" openendid="" iscode="" otherid="" isexclusive="true" questionname="_12.slice">
										<label for="_Q0_C7" class="active">
											<span class="mrMultipleText" style="">None of the above</span>
										</label>
									
								</span></div></div>
								
								
								
								
								
								
								
							</span>
						</span></div>
						
						
					</div><div id="qc_Q009_IMAG1._12.slice" class="qcContainer"><div class="col-sm-12 question-text theme-standard-bg-color2 theme-standard-font-color1"><div><!--x--></div><div></div><span class="mrQuestionText" style="">Which of these statements do you think apply to <b>Events sponsored by a brand</b>.<br>
							<span class="mrInstruct"><i>Select all that apply.</i></span></span><br></div></div><div id="container__12.slice" class="col-sm-12"></div><div id="container_slice" class="col-sm-12"><div data-test="main-contain" class="_rowpicker" dir="ltr" style="box-sizing: border-box; line-height: normal; white-space: normal; position: relative; display: block; width: 100%; margin: 0px;"><div class="__flexgrid_row" dir="ltr" style="box-sizing: border-box; position: relative; width: 100%; padding: 15px; -webkit-box-pack: start; justify-content: flex-start; -webkit-box-align: stretch; align-items: stretch; vertical-align: top; height: auto; display: flex; flex-wrap: wrap;"><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Are good quality</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Capture my attention</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(228, 231, 248);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Are relevant and useful to me</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(228, 231, 248);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Are fun and entertaining</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Are trustworthy</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Are innovative</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">Don't know</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: unset; transform: none;"><div style="width: 100%; display: inherit;"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent; text-align: center;"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: auto; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; max-width: 100%; text-align: center;"><label style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left;"><span style="display: block; transition: color 250ms; width: inherit; font-size: 11px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%; text-align: center;"><span class="" style="">None of the above</span></span></label></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;"><div></div></div></div></div></div></div></div></div><div><span></span></div></div></div>
				</div></div>
				<div id="surveyButtons" class="row footer1 z-depth-1" style="display: flex;">
					<input type="submit" name="_NNext" class="mrNext theme-bg-color theme-standard-font-color3 hoverable" style="background-image: url(&quot;https://multimedia.kantaroperations.com/8016f8b/origin.tns-global.com/solutions/iatools/prod/LAF/Themes/default/3.0/images/Next.png&quot;);" value="" alt="Next">
				</div>
				<div class="row footer2 z-depth-1">
					<div class="tempMenuButtons text-xs-left">
						<div class="dropup">
							<a class="menuButton dropdown-toggle theme-standard-font-color1" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-bars fa-2x hoverable"></i></a>
							<div class="dropdown-menu dropdown-menu-left" aria-labelledby="dropdownMenu1">
								<a class="tempHelp dropdown-item waves-effect waves-light theme-standard-font-color1 hoverable" title="Help"><i class="fa fa-question fa-2x"></i></a>
								<a class="tempPrivacy dropdown-item waves-effect waves-light theme-standard-font-color1 hoverable" title="Privacy"><i class="fa fa-lock fa-2x"></i></a>
								<div id="menuButtons" style="display:none">
							</div>
						</div>
					</div>
					<div class="logo text-xs-center">
					</div>
				</div>
			</div>
			<div id="infoPanel" style="display:none">
			</div>
		

</div></form><div id="secureAutoAnswerPanel" data-auto-answer-ui="true"><template shadowrootmode="closed"><div style="position: fixed; top: 50%; right: 0px; transform: translateY(-50%); padding: 8px; border-radius: 8px 0px 0px 8px; box-shadow: none; z-index: 99999; display: flex; flex-direction: column; gap: 6px; transition: height 0.3s; width: auto; background: transparent;"><label data-auto-answer-ui="true" style="display: flex; align-items: center; font-size: 12px; color: rgb(51, 51, 51); margin-bottom: 4px; cursor: pointer;"><input type="checkbox" id="autoAnswerToggle" data-auto-answer-ui="true" style="display: none;"><span class="switch-slider" data-auto-answer-ui="true" style="position: relative; display: inline-block; width: 40px; height: 20px; background-color: rgb(204, 204, 204); border-radius: 10px; transition: background-color 0.3s; cursor: pointer;"><span class="switch-knob" data-auto-answer-ui="true" style="position: absolute; top: 2px; left: 2px; width: 16px; height: 16px; background-color: white; border-radius: 50%; transition: transform 0.3s; box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 3px; transform: translateX(0px);"></span></span></label><button data-auto-answer-ui="true" style="padding: 0px; background: rgb(0, 255, 0); color: white; border: none; border-radius: 50%; cursor: pointer; font-size: 0px; transition: 0.2s; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-bolt">
                    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
                </svg>
            </button></div></template></div><div id="topBanner" style="position: fixed; bottom: 8px; right: 8px; background: transparent; padding: 8px 16px; border-radius: 8px; box-shadow: none; z-index: 99999; display: flex; justify-content: flex-end; align-items: center; gap: 16px; font-size: 14px; color: rgb(51, 51, 51);"><div id="timerDisplay" style="font-weight: bold;">13408142
06:04</div><div id="surveyIdDisplay" style="font-weight: bold;"></div></div><div id="testPanel" style="display: none;"><div id="innerTest" class="theme-bg-color">Test - v1</div></div><div class="modal fade" id="testModal" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button><h4 class="modal-title" id="myModalLabel">Interview Details</h4></div><div class="modal-body" id="modalBody"><table id="projectDetails" class="testTable table table-bordered table-responsive" style="display: table;"><caption>Respondent Details</caption><thead class="theme-bg-color"><tr><th>Object</th><th>Value</th></tr></thead><tr><td>Project</td><td>mediareact23v4 v1</td></tr><tr><td>Server</td><td>sa.ktrmr.com</td></tr><tr><td>Serial</td><td>18316</td></tr><tr><td>PID</td><td>LFP01_9172fb90-7870-c74d-12fb-ce59b88f7814</td></tr><tr><td>comp</td><td>{Incomplete}</td></tr><tr><td>Debug</td><td>0</td></tr><tr><td>Question</td><td>Q009_IMAG1[{_12}]</td></tr></table></div></div></div></div></body></html>