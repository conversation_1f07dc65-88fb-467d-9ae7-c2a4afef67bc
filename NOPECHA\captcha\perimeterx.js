(()=>{var u=chrome;var l="https://api.nopecha.com",o="https://www.nopecha.com",b="https://developers.nopecha.com",L={doc:{url:b,automation:{url:`${b}/guides/extension_advanced/#automation-build`}},api:{url:l,recognition:{url:`${l}/recognition`},status:{url:`${l}/status`}},www:{url:o,annoucement:{url:`${o}/json/announcement.json`},demo:{url:`${o}/captcha`,recaptcha:{url:`${o}/captcha/recaptcha`},funcaptcha:{url:`${o}/captcha/funcaptcha`},awscaptcha:{url:`${o}/captcha/awscaptcha`},textcaptcha:{url:`${o}/captcha/textcaptcha`},turnstile:{url:`${o}/captcha/turnstile`},perimeterx:{url:`${o}/captcha/perimeterx`},geetest:{url:`${o}/captcha/geetest`},lemincaptcha:{url:`${o}/captcha/lemincaptcha`}},manage:{url:`${o}/manage`},pricing:{url:`${o}/pricing`},setup:{url:`${o}/setup`}},discord:{url:`${o}/discord`},github:{url:`${o}/github`,release:{url:`${o}/github/release`}}};function h(e){let t=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(n=>n.charCodeAt(0));return v(t)}var _=new Uint32Array(256);for(let e=256;e--;){let t=e;for(let n=8;n--;)t=t&1?3988292384^t>>>1:t>>>1;_[e]=t}function v(e){let t=-1;for(let n of e)t=t>>>8^_[t&255^n];return(t^-1)>>>0}async function s(e,t){let n=""+[+new Date,performance.now(),Math.random()],[i,f]=await new Promise(T=>{u.runtime.sendMessage([n,e,...t],T)});if(i===h(n))return f}function w(){let e;return t=>e||(e=t().finally(()=>e=void 0),e)}var k=w(),r;function S(){return k(async()=>(r||(r=await s("settings::get",[])),r))}function x(e){r&&(r={...r,...e},y(r))}function c(){return r}function a(e){return new Promise(t=>setTimeout(t,e))}var $=[];function C(e,t){e.timedout=!1,$.push(e);let n,i=setInterval(async()=>{await B(e,c())||(clearTimeout(n),clearInterval(i))},400);t&&(n=setTimeout(()=>clearInterval(i),t),e.timedout=!0)}async function B(e,t){if(e.timedout)return!1;let n=e.condition(t);if(n===e.running())return!1;if(!n&&e.running())return e.quit(),!1;if(n&&!e.running()){for(;!e.ready();)await a(200);return e.start(),!1}}function y(e){$.forEach(t=>B(t,e))}function M(){u.runtime.connect({name:"stream"}).onMessage.addListener(t=>{t.event==="settingsUpdate"&&x(t.settings)})}function d(e){if(document.readyState!=="loading")setTimeout(e,0);else{let t;t=()=>{removeEventListener("DOMContentLoaded",t),e()},addEventListener("DOMContentLoaded",t)}}var p,m=!1;function P(){return!(!g()||!document.querySelector("[role=button]"))}function q(){m=!0,p=new MutationObserver(()=>{document.querySelector("[role=button]").dispatchEvent(new MouseEvent("mouseup"))}),p.observe(document.querySelector('[aria-label="Human challenge"], [aria-label="Press & Hold"]'),{attributes:!0,attributeFilter:["aria-label"]}),E()}function A(){m=!1,p.disconnect()}function H(){return m}function g(){try{return!!(window.top!==window&&window.top.top===window.top&&[...window.top.document.querySelectorAll("script")].some(e=>e.innerText.includes("_pxAppId"))&&document.querySelector('[aria-label="Human challenge"], [aria-label="Press & Hold"]'))}catch{return!1}}async function E(){let e=c();e.perimeterx_solve_delay&&await a(e.perimeterx_solve_delay_time),document.querySelector("[role=button]").dispatchEvent(new MouseEvent("mousedown"))}async function N(){let e=!1;for(let n=0;n<3;n++)if(await a(1e3),g()){e=!0;break}if(!e)return;M(),await S(),await s("tab::registerDetectedCaptcha",["perimeterx"]);let t=location.hostname;C({name:"perimeterx/auto-solve",condition:n=>n.enabled&&n.perimeterx_auto_solve&&!n.disabled_hosts.includes(t),ready:P,start:q,quit:A,running:H})}d(N);})();
