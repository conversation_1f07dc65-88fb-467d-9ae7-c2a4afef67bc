(()=>{var yl=Object.defineProperty;var xl=(n,e)=>{for(var t in e)yl(n,t,{get:e[t],enumerable:!0})};var vl=(n,e)=>n.map((t,r,i)=>e(t,r,i)?r:null).filter(t=>t!=null),ji=(n,e)=>[-1,...e,n.length].reduce(({xs:t,a:r},i)=>({xs:t?.concat([n.slice(r+1,i)])??[],a:i}),{}).xs,_l=(n,e)=>n.slice(0,-1).concat([n[n.length-1].concat(e[0])]).concat(e.slice(1)),pr=/\d/,Ht=/^epubcfi\((.*)\)$/,$i=n=>n.replace(/[\^[\](),;=]/g,"^$&"),Sl=n=>Ht.test(n)?n:`epubcfi(${n})`,Al=n=>n.match(Ht)?.[1]??n,El=n=>(...e)=>`epubcfi(${n(...e.map(t=>t.match(Ht)?.[1]??t))})`,Vi=El((...n)=>n.join("!")),kl=n=>{let e=[],t,r,i="",s=o=>(e.push(o),t=null,i=""),a=o=>(i+=o,r=!1);for(let o of Array.from(n.trim()).concat("")){if(o==="^"&&!r){r=!0;continue}if(t==="!")s(["!"]);else if(t===",")s([","]);else if(t==="/"||t===":")if(pr.test(o)){a(o);continue}else s([t,parseInt(i)]);else if(t==="~")if(pr.test(o)||o==="."){a(o);continue}else s(["~",parseFloat(i)]);else if(t==="@"){if(o===":"){s(["@",parseFloat(i)]),t="@";continue}if(pr.test(o)||o==="."){a(o);continue}else s(["@",parseFloat(i)])}else if(t==="["){o===";"&&!r?(s(["[",i]),t=";"):o===","&&!r?(s(["[",i]),t="["):o==="]"&&!r?s(["[",i]):a(o);continue}else if(t?.startsWith(";")){o==="="&&!r?(t=`;${i}`,i=""):o===";"&&!r?(s([t,i]),t=";"):o==="]"&&!r?s([t,i]):a(o);continue}(o==="/"||o===":"||o==="~"||o==="@"||o==="["||o==="!"||o===",")&&(t=o)}return e},qi=(n,e)=>vl(n,([t])=>t===e),Tl=n=>{let e=[],t;for(let[r,i]of n){if(r==="/")e.push({index:i});else{let s=e[e.length-1];if(r===":")s.offset=i;else if(r==="~")s.temporal=i;else if(r==="@")s.spatial=(s.spatial??[]).concat(i);else if(r===";s")s.side=i;else if(r==="[")if(t==="/"&&i)s.id=i;else{s.text=(s.text??[]).concat(i);continue}}t=r}return e},Wi=n=>ji(n,qi(n,"!")).map(Tl),nt=n=>{let e=kl(Al(n)),t=qi(e,",");if(!t.length)return Wi(e);let[r,i,s]=ji(e,t).map(Wi);return{parent:r,start:i,end:s}},Cl=({index:n,id:e,offset:t,temporal:r,spatial:i,text:s,side:a})=>{let o=a?`;s=${a}`:"";return`/${n}`+(e?`[${$i(e)}${o}]`:"")+(t!=null&&n%2?`:${t}`:"")+(r?`~${r}`:"")+(i?`@${i.join(":")}`:"")+(s||!e&&a?"["+(s?.map($i)?.join(",")??"")+o+"]":"")},Xi=n=>n.parent?[n.parent,n.start,n.end].map(Xi).join(","):n.map(e=>e.map(Cl).join("")).join("!"),dn=n=>Sl(Xi(n)),Ge=(n,e)=>typeof n=="string"?dn(Ge(nt(n),e)):n.parent?_l(n.parent,n[e?"end":"start"]):n,Rl=(n,e)=>{typeof n=="string"&&(n=nt(n)),typeof e=="string"&&(e=nt(e)),n=Ge(n),e=Ge(e,!0);let t=n[n.length-1],r=e[e.length-1],i=[],s=[],a=[],o=!0,l=Math.max(t.length,r.length);for(let u=0;u<l;u++){let d=t[u],h=r[u];o&&=d?.index===h?.index&&!d?.offset&&!h?.offset,o?i.push(d):(d&&s.push(d),h&&a.push(h))}let c=n.slice(0,-1).concat([i]);return dn({parent:c,start:[s],end:[a]})},Pt=(n,e)=>{if(typeof n=="string"&&(n=nt(n)),typeof e=="string"&&(e=nt(e)),n.start||e.start)return Pt(Ge(n),Ge(e))||Pt(Ge(n,!0),Ge(e,!0));for(let t=0;t<Math.max(n.length,e.length);t++){let r=n[t],i=e[t],s=Math.max(r.length,i.length)-1;for(let a=0;a<=s;a++){let o=r[a],l=i[a];if(!o)return-1;if(!l||o.index>l.index)return 1;if(o.index<l.index)return-1;if(a===s){if(o.offset>l.offset)return 1;if(o.offset<l.offset)return-1}}}return 0},gr=({nodeType:n})=>n===3||n===4,cn=({nodeType:n})=>n===1,wr=n=>{let e=Array.from(n.childNodes).filter(t=>gr(t)||cn(t)).reduce((t,r)=>{let i=t[t.length-1];return i?gr(r)?Array.isArray(i)?i.push(r):gr(i)?t[t.length-1]=[i,r]:t.push(r):cn(i)?t.push(null,r):t.push(r):t.push(r),t},[]);return cn(e[0])&&e.unshift("first"),cn(e[e.length-1])&&e.push("last"),e.unshift("before"),e.push("after"),e},Il=(n,e)=>n?wr(n)[e]:null,mr=(n,e)=>{let{id:t}=e[e.length-1];if(t){let s=n.ownerDocument.getElementById(t);if(s)return{node:s,offset:0}}for(let{index:s}of e){let a=Il(n,s);if(a==="first")return{node:n.firstChild??n};if(a==="last")return{node:n.lastChild??n};if(a==="before")return{node:n,before:!0};if(a==="after")return{node:n,after:!0};n=a}let{offset:r}=e[e.length-1];if(!Array.isArray(n))return{node:n,offset:r};let i=0;for(let s of n){let{length:a}=s.nodeValue;if(i+a>=r)return{node:s,offset:r-i};i+=a}},un=(n,e)=>{let{parentNode:t,id:r}=n,i=wr(t),s=i.findIndex(l=>Array.isArray(l)?l.some(c=>c===n):l===n),a=i[s];if(Array.isArray(a)){let l=0;for(let c of a)if(c===n){l+=e;break}else l+=c.nodeValue.length;e=l}let o={id:r,index:s,offset:e};return t!==n.ownerDocument.documentElement?un(t).concat(o):[o]},Gi=n=>{let{startContainer:e,startOffset:t,endContainer:r,endOffset:i}=n,s=un(e,t);if(n.collapsed)return dn([s]);let a=un(r,i);return Rl([s],[a])},hn=(n,e)=>{let t=Ge(e),r=Ge(e,!0),i=n.documentElement,s=mr(i,t[0]),a=mr(i,r[0]),o=n.createRange();return s.before?o.setStartBefore(s.node):s.after?o.setStartAfter(s.node):o.setStart(s.node,s.offset),a.before?o.setEndBefore(a.node):a.after?o.setEndAfter(a.node):o.setEnd(a.node,a.offset),o},Zi=n=>{let e=[],{parentNode:t}=n[0],r=un(t);for(let[i,s]of wr(t).entries()){let a=n[e.length];s===a&&e.push(dn([r.concat({id:a.id,index:i})]))}return e},br=(n,e)=>mr(n.documentElement,Ge(e)).node,yr={fromIndex:n=>`/6/${(n+1)*2}`,toIndex:n=>n?.at(-1).index/2-1};var zl=n=>{let e=0,t=r=>{if(r.id=e++,r.subitems)for(let i of r.subitems)t(i)};for(let r of n)t(r);return n},Yi=n=>n.map(e=>e.subitems?.length?[e,Yi(e.subitems)].flat():e).flat(),$t=class{constructor({toc:e,ids:t,splitHref:r,getFragment:i}){zl(e);let s=Yi(e),a=new Map;for(let[l,c]of s.entries()){let[u,d]=r(c?.href)??[],h={fragment:d,item:c};a.has(u)?a.get(u).items.push(h):a.set(u,{prev:s[l-1],items:[h]})}let o=new Map;for(let[l,c]of t.entries())a.has(c)?o.set(c,a.get(c)):o.set(c,o.get(t[l-1]));this.ids=t,this.map=o,this.getFragment=i}getProgress(e,t){let r=this.ids[e],i=this.map.get(r);if(!i)return null;let{prev:s,items:a}=i;if(!a)return s;if(!t||a.length===1&&!a[0].fragment)return a[0].item;let o=t.startContainer.getRootNode();for(let[l,{fragment:c}]of a.entries()){let u=this.getFragment(o,c);if(u&&t.comparePoint(u,0)>0)return a[l-1]?.item??s}return a[a.length-1].item}},fn=class{constructor(e,t,r){this.sizes=e.map(i=>i.linear==="no"?0:i.size),this.sizePerLoc=t,this.sizePerTimeUnit=r,this.sizeTotal=this.sizes.reduce((i,s)=>i+s,0)}getProgress(e,t){let{sizes:r,sizePerLoc:i,sizePerTimeUnit:s,sizeTotal:a}=this,o=r[e]??0,c=r.slice(0,e).reduce((h,m)=>h+m,0)+t*o,u=a-c,d=(1-t)*o;return{fraction:c/a,section:{current:e,total:r.length},location:{current:Math.floor(c/i),total:Math.ceil(a/i)},time:{section:d/s,total:u/s}}}getSection(e){let{sizes:t,sizeTotal:r}=this,i=e*r,s=-1,a=0,o=0;for(let[l,c]of t.entries()){let u=o+c;if(u>i){s=l,a=(i-o)/c;break}o=u}return[s,a]}};var Et=n=>document.createElementNS("http://www.w3.org/2000/svg",n),pn=class{#e=Et("svg");#t=new Map;constructor(){Object.assign(this.#e.style,{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",pointerEvents:"none"});let e=matchMedia("(prefers-color-scheme: dark)"),t=()=>this.#e.style.mixBlendMode=e.matches?"normal":"multiply";e.addEventListener("change",t),t()}get element(){return this.#e}add(e,t,r,i){this.#t.has(e)&&this.remove(e),typeof t=="function"&&(t=t(this.#e.getRootNode()));let s=t.getClientRects(),a=r(s,i);this.#e.append(a),this.#t.set(e,{range:t,draw:r,options:i,element:a,rects:s})}remove(e){this.#t.has(e)&&(this.#e.removeChild(this.#t.get(e).element),this.#t.delete(e))}redraw(){for(let e of this.#t.values()){let{range:t,draw:r,options:i,element:s}=e;this.#e.removeChild(s);let a=t.getClientRects(),o=r(a,i);this.#e.append(o),e.element=o,e.rects=a}}hitTest({x:e,y:t}){let r=Array.from(this.#t.entries());for(let i=r.length-1;i>=0;i--){let[s,a]=r[i];for(let{left:o,top:l,right:c,bottom:u}of a.rects)if(l<=t&&o<=e&&u>t&&c>e)return[s,a.range]}return[]}static underline(e,t={}){let{color:r="red",width:i=2}=t,s=Et("g");s.setAttribute("fill",r);for(let{left:a,bottom:o,width:l}of e){let c=Et("rect");c.setAttribute("x",a),c.setAttribute("y",o-i),c.setAttribute("height",i),c.setAttribute("width",l),s.append(c)}return s}static highlight(e,t={}){let{color:r="red"}=t,i=Et("g");i.setAttribute("fill",r),i.setAttribute("fill-opacity",.3);for(let{left:s,top:a,height:o,width:l}of e){let c=Et("rect");c.setAttribute("x",s),c.setAttribute("y",a),c.setAttribute("height",o),c.setAttribute("width",l),i.append(c)}return i}static copyImage([e],t={}){let{src:r}=t,i=Et("image"),{left:s,top:a,height:o,width:l}=e;return i.setAttribute("href",r),i.setAttribute("x",s),i.setAttribute("y",a),i.setAttribute("height",o),i.setAttribute("width",l),i}};var gn=class{#e=new Map;#t=new Map;#n=new Map;#r=new Map;constructor({resolve:e,compare:t,onAdd:r,onDelete:i,onUpdate:s}){this.resolve=e,this.compare=t,this.onAdd=r,this.onDelete=i,this.onUpdate=s}async add(e,t){let{value:r}=e;if(this.#t.has(r))return;let{index:i,anchor:s}=await this.resolve(r);if(this.#t.set(r,e),this.#r.set(r,i),this.#n.set(r,s),this.#e.has(i)){let a=this.#e.get(i);if(t)a.push(e),this.onAdd?.(e,i,a.length-1);else{let o=0;for(let l=0;l<a.length;l++){let c=a[l].value;if(this.compare(r,c)<=0)break;o=l+1}a.splice(o,0,e),this.onAdd?.(e,i,o)}}else this.#e.set(i,[e]),this.onAdd?.(e,i,0)}update(e){let t=this.#r.get(e.value),r=this.#t.get(e.value);Object.assign(r,e),this.onUpdate?.(e,t)}delete(e){let t=this.#r.get(e),r=this.#e.get(t),i=r.findIndex(s=>s.value===e);r.splice(i,1),this.#t.delete(e),this.#r.delete(e),this.#n.delete(e),this.onDelete?.(e,t,i)}getByIndex(e){return this.#e.get(e)??[]}getAnchor(e){return this.#n.get(e)}};var Dl=n=>new Promise(e=>setTimeout(e,n)),Ll=(n,e,t)=>{let r;return(...i)=>{let s=()=>{r=null,t||n(...i)},a=t&&!r;r&&clearTimeout(r),r=setTimeout(s,e),a&&n(...i)}},Fl=n=>{if(!n?.collapsed)return n;let{endOffset:e,endContainer:t}=n;if(t.nodeType===1)return t;if(e+1<t.length)n.setEnd(t,e+1);else if(e>1)n.setStart(t,e-1);else return t.parentNode;return n},mn=(n,e,t,r=t)=>{let i=n.createRange();return i.setStart(e,t),i.setEnd(e,r),i},wn=(n,e,t,r=0,i=e.nodeValue.length)=>{if(i-r===1)return t(mn(n,e,r),mn(n,e,i))<0?r:i;let s=Math.floor(r+(i-r)/2),a=t(mn(n,e,r,s),mn(n,e,s,i));return a<0?wn(n,e,t,r,s):a>0?wn(n,e,t,s,i):s},{SHOW_ELEMENT:Ol,SHOW_TEXT:Ml,SHOW_CDATA_SECTION:Ul,FILTER_ACCEPT:Ji,FILTER_REJECT:Qi,FILTER_SKIP:es}=NodeFilter,Bl=Ol|Ml|Ul,Nl=(n,e,t,r)=>{let i=h=>{let m=h.localName?.toLowerCase();if(m==="script"||m==="style")return Qi;if(h.nodeType===1){let{left:w,right:f}=r(h.getBoundingClientRect());if(f<e||w>t)return Qi;if(w>=e&&f<=t)return Ji}else{if(!h.nodeValue?.trim())return es;let w=n.createRange();w.selectNodeContents(h);let{left:f,right:p}=r(w.getBoundingClientRect());if(p>=e&&f<=t)return Ji}return es},s=n.createTreeWalker(n.body,Bl,{acceptNode:i}),a=[];for(let h=s.nextNode();h;h=s.nextNode())a.push(h);let o=a[0]??n.body,l=a[a.length-1]??o,c=o.nodeType===1?0:wn(n,o,(h,m)=>{let w=r(h.getBoundingClientRect()),f=r(m.getBoundingClientRect());return w.right<e&&f.left>e?0:f.left>e?-1:1}),u=l.nodeType===1?0:wn(n,l,(h,m)=>{let w=r(h.getBoundingClientRect()),f=r(m.getBoundingClientRect());return w.right<t&&f.left>t?0:f.left>t?-1:1}),d=n.createRange();return d.setStart(o,c),d.setEnd(l,u),d},Pl=n=>{let{defaultView:e}=n,{writingMode:t,direction:r}=e.getComputedStyle(n.body),i=t==="vertical-rl"||t==="vertical-lr",s=n.body.dir==="rtl"||r==="rtl"||n.documentElement.dir==="rtl";return{vertical:i,rtl:s}},Hl=n=>{let e=n.defaultView.getComputedStyle(n.body);return e.backgroundColor==="rgba(0, 0, 0, 0)"&&e.backgroundImage==="none"?n.defaultView.getComputedStyle(n.documentElement).background:e.background},xr=class{#e=document.createElement("div");#t=document.createElement("iframe");#n=document.createRange();#r;#i=!1;#s=!1;#a=!0;#o;#l={};constructor({container:e}){this.container=e,this.#t.classList.add("filter"),this.#e.append(this.#t),Object.assign(this.#e.style,{position:"relative",overflow:"hidden",flex:"0 0 auto",width:"100%",height:"100%"}),Object.assign(this.#t.style,{overflow:"hidden",border:"0",display:"none",width:"100%",height:"100%"}),this.#t.setAttribute("sandbox","allow-same-origin allow-scripts"),this.#t.setAttribute("scrolling","no")}get element(){return this.#e}get document(){return this.#t.contentDocument}async load(e,t,r){if(typeof e!="string")throw new Error(`${e} is not string`);return new Promise(i=>{this.#t.addEventListener("load",()=>{let s=this.document;t?.(s),this.#t.style.display="block";let{vertical:a,rtl:o}=Pl(s),l=Hl(s);this.#t.style.display="none",this.#i=a,this.#s=o,this.#n.selectNodeContents(s.body);let c=r?.({vertical:a,rtl:o,background:l});this.#t.style.display="block",this.render(c),new ResizeObserver(()=>this.expand()).observe(s.body),i()},{once:!0}),this.#t.src=e})}render(e){this.#a=e.flow!=="scrolled",this.#l=e,this.#a?this.columnize(e):this.scrolled(e)}scrolled({gap:e,columnWidth:t}){let r=this.#i,i=this.document;Object.assign(i.documentElement.style,{boxSizing:"border-box",padding:r?`${e}px 0`:`0 ${e}px`,columnWidth:"auto",height:"auto",width:"auto"}),Object.assign(i.body.style,{[r?"maxHeight":"maxWidth"]:`${t}px`,margin:"auto"}),this.setImageSize(),this.expand()}columnize({width:e,height:t,gap:r,columnWidth:i}){let s=this.#i;this.#o=s?t:e;let a=this.document;Object.assign(a.documentElement.style,{boxSizing:"border-box",columnWidth:`${i}px`,columnGap:`${r}px`,columnFill:"auto",...s?{width:`${e}px`}:{height:`${t}px`},padding:s?`${r/2}px 0`:`0 ${r/2}px`,overflow:"hidden",overflowWrap:"anywhere",position:"static",border:"0",margin:"0",maxHeight:"none",maxWidth:"none",minHeight:"none",minWidth:"none",webkitLineBoxContain:"block glyphs replaced"}),Object.assign(a.body.style,{maxHeight:"none",maxWidth:"none",margin:"0"}),this.setImageSize(),this.expand()}setImageSize(){let{width:e,height:t,margin:r}=this.#l,i=this.#i,s=this.document;for(let a of s.body.querySelectorAll("img, svg, video")){let{maxHeight:o,maxWidth:l}=s.defaultView.getComputedStyle(a);Object.assign(a.style,{maxHeight:i?o!=="none"&&o!=="0px"?o:"100%":`${t-r*2}px`,maxWidth:i?`${e-r*2}px`:l!=="none"&&l!=="0px"?l:"100%",objectFit:"contain",pageBreakInside:"avoid",breakInside:"avoid",boxSizing:"border-box"})}}expand(){if(this.#a){let e=this.#i?"height":"width",t=this.#i?"width":"height",r=this.#n.getBoundingClientRect()[e],s=Math.ceil(r/this.#o)*this.#o;this.#e.style.padding="0",this.#t.style[e]=`${s}px`,this.#e.style[e]=`${s}px`,this.#t.style[t]="100%",this.#e.style[t]="100%",this.document&&(this.document.documentElement.style[e]=`${s}px`),this.#r&&(this.#r.element.style.margin="0",this.#r.element.style[e]=`${s}px`,this.#r.redraw())}else{let e=this.#i?"width":"height",t=this.#i?"height":"width",s=this.document?.documentElement?.getBoundingClientRect()?.[e],{margin:a}=this.#l,o=this.#i?`0 ${a}px`:`${a}px 0`;this.#e.style.padding=o,this.#t.style[e]=`${s}px`,this.#e.style[e]=`${s}px`,this.#t.style[t]="100%",this.#e.style[t]="100%",this.#r&&(this.#r.element.style.margin=o,this.#r.element.style[e]=`${s}px`,this.#r.redraw())}}set overlayer(e){this.#r=e,this.#e.append(e.element)}get overlayer(){return this.#r}},bn=class{#e=document.createElement("div");#t=document.createElement("div");#n;#r=!1;#i=!1;#s=-1;#a=0;#o=!1;#l=new WeakMap;layout={margin:48,gap:40,maxColumnWidth:700};constructor({book:e,onLoad:t,onRelocated:r,createOverlayer:i}){this.sections=e.sections,this.onLoad=t,this.onRelocated=r,this.createOverlayer=i,Object.assign(this.#e.style,{boxSizing:"border-box",width:"100%",height:"100%",position:"absolute"}),this.#e.append(this.#t),Object.assign(this.#t.style,{width:"100%",height:"100%"}),new ResizeObserver(()=>this.render()).observe(this.#e),this.#t.addEventListener("scroll",Ll(()=>{this.scrolled&&this.#w("scroll")},250))}get element(){return this.#e}#d(){return this.#n&&this.#t.removeChild(this.#n.element),this.#n=new xr({container:this.#e}),this.#t.append(this.#n.element),this.#n}#c({vertical:e,rtl:t,background:r}){this.#r=e,this.#i=t,this.#e.style.background=r;let{flow:i,margin:s,gap:a,maxColumnWidth:o}=this.layout;if(i==="scrolled"){this.#e.setAttribute("dir",e?"rtl":"ltr"),this.#e.style.padding="0",this.#t.style.overflow="scroll";let f=this.layout.maxColumnWidth;return{flow:i,margin:s,gap:a,columnWidth:f}}let{width:l,height:c}=this.#t.getBoundingClientRect(),u=e?c:l,d=Math.ceil(u/o),h=u/d-a;this.#e.setAttribute("dir",t?"rtl":"ltr");let m=`${e?a:a/2}px`,w=`${e?s-a/2:s}px`;return this.#e.style.padding=`${w} ${m}`,this.#t.style.overflow="hidden",{height:c,width:l,margin:s,gap:a,columnWidth:h}}render(){this.#n&&(this.#n.render(this.#c({vertical:this.#r,rtl:this.#i})),this.#m())}get scrolled(){return this.layout.flow==="scrolled"}get scrollProp(){let{scrolled:e}=this;return this.#r?e?"scrollLeft":"scrollTop":e?"scrollTop":"scrollLeft"}get sideProp(){let{scrolled:e}=this;return this.#r?e?"width":"height":e?"height":"width"}get size(){return this.#t.getBoundingClientRect()[this.sideProp]}get viewSize(){return this.#n.element.getBoundingClientRect()[this.sideProp]}get start(){return Math.abs(this.#t[this.scrollProp])}get end(){return this.start+this.size}get page(){return Math.floor((this.start+this.end)/2/this.size)}get pages(){return Math.round(this.viewSize/this.size)}#u(){if(this.scrolled){let t=this.viewSize,r=this.layout.margin;return this.#r?({left:i,right:s})=>({left:t-s-r,right:t-i-r}):({top:i,bottom:s})=>({left:i+r,right:s+r})}let e=this.pages*this.size;return this.#i?({left:t,right:r})=>({left:e-r,right:e-t}):this.#r?({top:t,bottom:r})=>({left:t,right:r}):t=>t}async#f(e,t){if(this.scrolled){let i=this.#u()(e).left;return this.#h(i,t)}let r=this.#u()(e).left+this.layout.margin/2;return this.#p(Math.floor(r/this.size),t)}async#h(e,t){let r=this.#t,{scrollProp:i}=this;if(r[i]===e){this.#w(t);return}this.scrolled&&this.#r&&(e=-e),r[i]=e,this.#w(t)}async#p(e,t){let r=this.size*(this.#i?-e:e);return this.#h(r,t)}async#m(e){let t=Fl(this.#a).getBoundingClientRect?.();if(t){await this.#f(t,"anchor"),e&&this.#_();return}if(this.scrolled){await this.#h(this.#a*this.viewSize,"anchor");return}let{pages:r}=this;if(!r)return;let i=Math.round(this.#a*(r-1));await this.#p(i,"anchor")}#_(){let{defaultView:e}=this.#n.document;if(this.#a instanceof e.Range){let t=e.getSelection();t.removeAllRanges(),t.addRange(this.#a)}}#S(){return Nl(this.#n.document,this.start,this.end,this.#u(),this.scrolled)}#w(e){let t=this.#S();e!=="anchor"&&(this.#a=t);let r=this.#s;this.scrolled?this.onRelocated?.(t,r,this.end/this.viewSize):this.pages>0&&this.onRelocated?.(t,r,(this.page+1)/this.pages)}async#y(e){let{index:t,src:r,anchor:i,onLoad:s,select:a}=await e;if(this.#s=t,r){let o=this.#d(),l=d=>{if(d.head){let h=d.createElement("style");d.head.append(h),this.#l.set(d,h)}s?.(d,t)},c=this.#c.bind(this);await o.load(r,l,c);let u=this.createOverlayer?.(o.document,t);u&&(o.overlayer=u),this.#n=o}this.#a=(typeof i=="function"?i(this.#n.document):i)??0,await this.#m(a)}#x(e){return e>-1&&e<this.pages}scrollPrev(){if(!this.#n)return null;if(this.scrolled)return this.start>0?this.#h(Math.max(0,this.start-this.size)):null;let e=this.page-1;return this.#x(e)?this.#p(e):null}scrollNext(){if(!this.#n)return null;if(this.scrolled)return this.viewSize-this.end>2?this.#h(Math.min(this.viewSize,this.end)):null;let e=this.page+1;return this.#x(e)?this.#p(e):null}#v(e){return e>=0&&e<=this.sections.length-1}async#b(e,t,r){if(this.#o)return;r&&(this.#o=!0);let i=e?.();if(i)await i;else{let{index:s,anchor:a,select:o}=await t;if(!this.#v(s))return this.#o=!1,null;if(s===this.#s)await this.#y({index:s,anchor:a,select:o});else{let l=this.#s,c=(...u)=>{this.sections[l]?.unload?.(),this.onLoad?.(...u)};await this.#y(Promise.resolve(this.sections[s].load()).then(u=>({index:s,src:u,anchor:a,onLoad:c,select:o})).catch(u=>({})))}}r&&(await Dl(100),this.#o=!1)}async goTo(e){return this.#b(null,e)}#g(e){for(let t=this.#s+e;this.#v(t);t+=e)if(this.sections[t]?.linear!=="no")return t}prev(){let e=this.#g(-1);return this.#b(()=>this.scrollPrev(),{index:e,anchor:()=>1},!0)}next(){let e=this.#g(1);return this.#b(()=>this.scrollNext(),{index:e},!0)}prevSection(){return this.goTo({index:this.#g(-1)})}nextSection(){return this.goTo({index:this.#g(1)})}firstSection(){let e=this.sections.findIndex(t=>t.linear!=="no");return this.goTo({index:e})}lastSection(){let e=this.sections.findLastIndex(t=>t.linear!=="no");return this.goTo({index:e})}getOverlayer(){if(this.#n)return{index:this.#s,overlayer:this.#n.overlayer,doc:this.#n.document}}setStyle(e){let t=this.#l.get(this.#n?.document);t&&(t.textContent=e)}async#A(e,t){this.#a=e,await this.#m(t)}};var ts=n=>n?.split(/[,;\s]/)?.filter(e=>e)?.map(e=>e.split("=").map(t=>t.trim())),$l=(n,e)=>{if(n.documentElement.nodeName==="svg"){let[,,i,s]=n.documentElement.getAttribute("viewBox")?.split(/\s/)??[];return{width:i,height:s}}let t=ts(n.querySelector('meta[name="viewport"]')?.getAttribute("content"));if(t)return Object.fromEntries(t);if(typeof e=="string")return ts(e);if(e)return e;let r=n.querySelector("img");return r?{width:r.naturalWidth,height:r.naturalHeight}:{width:1e3,height:2e3}},vr=class{#e=document.createElement("div");defaultViewport;spread;#t=!1;#n;#r;#i;#s;constructor(){Object.assign(this.#e.style,{width:"100vw",height:"100vh",display:"flex",justifyContent:"center",alignItems:"center"}),new ResizeObserver(()=>this.render()).observe(this.#e)}get element(){return this.#e}get side(){return this.#s}async#a(e){let t=document.createElement("div"),r=document.createElement("iframe");return t.append(r),Object.assign(r.style,{border:"0",display:"none",overflow:"hidden"}),r.setAttribute("scrolling","no"),r.classList.add("filter"),this.#e.append(t),e?new Promise(i=>{let s=()=>{r.removeEventListener("load",s),this.onLoad?.(r);let a=r.contentDocument,{width:o,height:l}=$l(a,this.defaultViewport);i({element:t,iframe:r,width:parseFloat(o),height:parseFloat(l)})};r.addEventListener("load",s),r.src=e}):{blank:!0,element:t,iframe:r}}render(e=this.#s){if(!e)return;let t=this.#n??{},r=this.#i??this.#r,i=e==="left"?t:r,{width:s,height:a}=this.#e.getBoundingClientRect(),o=this.spread!=="both"&&this.spread!=="portrait"&&a>s;this.#t=o;let l=t.width??r.width,c=t.height??r.height,u=o?Math.min(s/(i.width??l),a/(i.height??c)):Math.min(s/((t.width??l)+(r.width??l)),a/Math.max(t.height??c,r.height??c)),d=h=>{let{element:m,iframe:w,width:f,height:p}=h;Object.assign(w.style,{width:`${f}px`,height:`${p}px`,transform:`scale(${u})`,transformOrigin:"top left",display:"block"}),Object.assign(m.style,{width:`${(f??l)*u}px`,height:`${(p??c)*u}px`,overflow:"hidden",display:"block"}),o&&h!==i&&(m.style.display="none")};this.#i?d(this.#i):(d(t),d(r))}async showSpread({left:e,right:t,center:r,side:i}){this.#e.replaceChildren(),this.#n=null,this.#r=null,this.#i=null,r?(this.#i=await this.#a(r),this.#s="center",this.render()):(this.#n=await this.#a(e),this.#r=await this.#a(t),this.#s=i,this.render())}goLeft(){if(!this.#i){if(this.#n?.blank)return!0;if(this.#t&&this.#n?.element?.style?.display==="none")return this.#r.element.style.display="none",this.#n.element.style.display="block",this.#s="left",!0}}goRight(){if(!this.#i){if(this.#r?.blank)return!0;if(this.#t&&this.#r?.element?.style?.display==="none")return this.#n.element.style.display="none",this.#r.element.style.display="block",this.#s="right",!0}}},yn=class{#e;#t=-1;#n=new vr;constructor({book:e,onLoad:t,onRelocated:r}){this.book=e,this.onLoad=t,this.onRelocated=r;let{rendition:i}=e;this.#n.spread=i?.spread,this.#n.defaultViewport=i?.viewport;let s=e.dir==="rtl",a=!s;this.rtl=s,i?.spread==="none"?this.#e=e.sections.map(o=>({center:o})):this.#e=e.sections.reduce((o,l)=>{let c=o[o.length-1],{linear:u,pageSpread:d}=l;if(u==="no")return o;let h=()=>{let m={};return o.push(m),m};if(d==="center")h().center=l;else if(d==="left"){let m=c.center||c.left||a?h():c;m.left=l}else if(d==="right"){let m=c.center||c.right||s?h():c;m.right=l}else a?c.center||c.right?h().left=l:c.left?c.right=l:c.left=l:c.center||c.left?h().right=l:c.right?c.left=l:c.right=l;return o},[{}])}get element(){return this.#n.element}get index(){let e=this.#e[this.#t],t=e?.center??(this.#n.side==="left"?e.left??e.right:e.right??e.left);return this.book.sections.indexOf(t)}getSpreadOf(e){let t=this.#e;for(let r=0;r<t.length;r++){let{left:i,right:s,center:a}=t[r];if(i===e)return{index:r,side:"left"};if(s===e)return{index:r,side:"right"};if(a===e)return{index:r,side:"center"}}}async goToSpread(e,t){if(e<0||e>this.#e.length-1)return;if(e===this.#t){this.#n.render(t);return}this.#t=e;let r=this.#e[e];if(r.center){let i=await r.center?.load?.();await this.#n.showSpread({center:i})}else{let i=await r.left?.load?.(),s=await r.right?.load?.();await this.#n.showSpread({left:i,right:s,side:t})}this.onRelocated?.(null,this.index,0,1)}async select(e){await this.goTo(e)}async goTo(e){let{book:t}=this,r=await e,i=t.sections[r.index];if(!i)return;let{index:s,side:a}=this.getSpreadOf(i);await this.goToSpread(s,a)}async next(){if(this.rtl?this.#n.goLeft():this.#n.goRight())this.onRelocated?.(null,this.index,0,1);else return this.goToSpread(this.#t+1,this.rtl?"right":"left")}async prev(){if(this.rtl?this.#n.goRight():this.#n.goLeft())this.onRelocated?.(null,this.index,0,1);else return this.goToSpread(this.#t-1,this.rtl?"left":"right")}};var ns=n=>n.replace(/\s+/g," "),Wl=(n,{startIndex:e,startOffset:t,endIndex:r,endOffset:i})=>{let s=n[e],a=n[r],o=s===a?s.slice(t,i):s.slice(t)+n.slice(s+1,a).join("")+a.slice(0,i),l=ns(s.slice(0,t)).trimStart(),c=ns(a.slice(i)).trimEnd(),u=l.length<50?"":"\u2026",d=c.length<50?"":"\u2026",h=`${u}${l.slice(-50)}`,m=`${c.slice(0,50)}${d}`;return{pre:h,match:o,post:m}},jl=function*(n,e,t={}){let{locales:r="en",granularity:i="word",sensitivity:s="base"}=t,a,o;try{a=new Intl.Segmenter(r,{usage:"search",granularity:i}),o=new Intl.Collator(r,{sensitivity:s})}catch{a=new Intl.Segmenter("en",{usage:"search",granularity:i}),o=new Intl.Collator("en",{sensitivity:s})}let l=Array.from(a.segment(e)).length,c=[],u=0,d=a.segment(n[u])[Symbol.iterator]();e:for(;u<n.length;){for(;c.length<l;){let{done:m,value:w}=d.next();if(m)if(u++,u<n.length){d=a.segment(n[u])[Symbol.iterator]();continue}else break e;let{index:f,segment:p}=w;if(/[^\p{Format}]/u.test(p)){if(/\s/u.test(p)){/\s/u.test(c[c.length-1]?.segment)||c.push({strIndex:u,index:f,segment:" "});continue}w.strIndex=u,c.push(w)}}let h=c.map(m=>m.segment).join("");if(o.compare(e,h)===0){let m=u,w=c[c.length-1],f=w.index+w.segment.length,p=c[0].strIndex,g=c[0].index,b={startIndex:p,startOffset:g,endIndex:m,endOffset:f};yield{range:b,excerpt:Wl(n,b)}}c.shift()}},rs=(n,e)=>{let{defalutLocale:t,matchCase:r,matchDiacritics:i,matchWholeWords:s}=e;return function*(a,o){let l=n(a,function*(c,u){for(let d of jl(c,o,{locales:a.body.lang||a.documentElement.lang||t||"en",granularity:s?"word":"grapheme",sensitivity:i&&r?"variant":i&&!r?"accent":!i&&r?"case":"base"})){let{startIndex:h,startOffset:m,endIndex:w,endOffset:f}=d.range;d.range=u(h,m,w,f),yield d}});for(let c of l)yield c}};var Vl=function*(n,e){let t=NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT|NodeFilter.SHOW_CDATA_SECTION,{FILTER_ACCEPT:r,FILTER_REJECT:i,FILTER_SKIP:s}=NodeFilter,a=d=>{let h=d.localName?.toLowerCase();return h==="script"||h==="style"?i:d.nodeType===1?s:r},o=n.createTreeWalker(n.body,t,{acceptNode:a}),l=[];for(let d=o.nextNode();d;d=o.nextNode())l.push(d);let c=l.map(d=>d.nodeValue),u=(d,h,m,w)=>{let f=n.createRange();return f.setStart(l[d],h),f.setEnd(l[m],w),f};for(let d of e(c,u))yield d},is=(n,e)=>{let t=e.left+n.left,r=e.right+n.left,i=e.top+n.top,s=e.bottom+n.top;return{left:t,right:r,top:i,bottom:s}},ss=({x:n,y:e})=>n>0&&e>0&&n<window.innerWidth&&e<window.innerHeight,os=n=>{let t=(n.getRootNode?.()??n?.endContainer?.getRootNode?.())?.defaultView?.frameElement?.getBoundingClientRect()??{top:0,left:0},r=Array.from(n.getClientRects()),i=is(t,r[0]),s=is(t,r.at(-1)),a={point:{x:(i.left+i.right)/2,y:i.top},dir:"up"},o={point:{x:(s.left+s.right)/2,y:s.bottom},dir:"down"},l=ss(a.point),c=ss(o.point);return!l&&!c?{point:{x:0,y:0}}:l?c?a.point.y>window.innerHeight-o.point.y?a:o:a:o},as=Object.fromEntries(Array.from(Object.entries({isRef:["annoref","biblioref","glossref","noteref"],isLink:["backlink"],isNote:["annotation","note","footnote","endnote","rearnote"]}),([n,e])=>[n,t=>t.getAttributeNS("http://www.idpf.org/2007/ops","type")?.split(/s/)?.some(r=>e.includes(r))])),xn=class{#e;#t;#n;#r;language="en";textDirection="";isCJK=!1;isFixedLayout=!1;annotations=new gn({resolve:e=>this.resolveCFI(e),compare:Pt,onAdd:(e,t,r)=>{let i=this.#o(t);i&&this.#a(i.doc,i.overlayer,e);let s=this.#t.getProgress(t)?.label??"";this?.emit({type:"add-annotation",annotation:e,label:s,index:t,position:r})},onDelete:(e,t,r)=>{this.#o(t)?.overlayer?.remove(e),this?.emit({type:"delete-annotation",index:t,position:r})},onUpdate:(e,t)=>{let r=this.#o(t);r&&(r.overlayer.remove(e.value),this.#a(r.doc,r.overlayer,e))}});constructor(e,t){if(this.book=e,this.emit=t,e.metadata?.language)try{let r=e.metadata.language;e.metadata.language=Intl.getCanonicalLocales(r)[0];let i=typeof r=="string"?r:r[0],s=new Intl.Locale(i);this.isCJK=["zh","ja","kr"].includes(s.language),s.textInfo&&s.textInfo.direction&&(this.textDirection=s.textInfo.direction)}catch{}if(e.splitTOCHref&&e.getTOCFragment){let r=e.sections.map(a=>a.id);this.#e=new fn(e.sections,150,1600);let i=e.splitTOCHref.bind(e),s=e.getTOCFragment.bind(e);this.#t=new $t({toc:e.toc??[],ids:r,splitHref:i,getFragment:s}),this.#n=new $t({toc:e.pageList??[],ids:r,splitHref:i,getFragment:s})}}async display(){let e={book:this.book,onLoad:this.#s.bind(this),onRelocated:this.#i.bind(this),createOverlayer:this.#l.bind(this)};return this.isFixedLayout=this.book.rendition?.layout==="pre-paginated",this.isFixedLayout?this.renderer=new yn(e):this.renderer=new bn(e),this.renderer.element}async init({lastLocation:e,annotations:t}){if(e){let r=this.resolveNavigation(e);r?await this.renderer.goTo(r):await this.renderer.next()}else await this.renderer.next();if(t){t.sort((r,i)=>Pt(r.value,i.value));for(let r of t)await this.annotations.add(r,!0)}}#i(e,t,r){if(!this.#e)return;let i=this.#e.getProgress(t,r),s=this.#t.getProgress(t,e),a=this.#n.getProgress(t,e),o=this.getCFI(t,e);this.emit?.({type:"relocated",...i,tocItem:s,pageItem:a,cfi:o})}#s(e,t){let{book:r}=this;e.documentElement.lang||=this.language,e.documentElement.dir||=this.isCJK?"":this.textDirection,this.renderer.setStyle(this.#r);let i=r.sections[t];for(let s of e.querySelectorAll("a[href]"))s.addEventListener("click",a=>{a.preventDefault();let o=s.getAttribute("href"),l=i?.resolveHref?.(o)??o;if(r?.isExternal?.(l))this.emit?.({type:"external-link",uri:l});else if(as.isRef(s)){let{index:c,anchor:u}=r.resolveHref(l);Promise.resolve(r.sections[c].createDocument()).then(d=>[u(d),d.contentType]).then(([d,h])=>[d?.innerHTML,h,as.isNote(d)]).then(([d,h,m])=>d?this.emit?.({type:"reference",href:m?null:l,content:d,contentType:h,element:s}):null).catch(d=>{});return}else this.goTo(l)});this.emit?.({type:"loaded",doc:e,index:t})}#a(e,t,r){let{value:i}=r,s=this.annotations.getAnchor(i),a=e?s(e):s,[o,l]=this.emit({type:"draw-annotation",annotation:r});t.add(i,a,o,l)}#o(e){let t=this.renderer.getOverlayer();if(t.index===e)return t}#l(e,t){let r=new pn;for(let i of this.annotations.getByIndex(t))this.#a(e,r,i);return e.addEventListener("click",i=>{let[s,a]=r.hitTest(i);s&&this.emit?.({type:"show-annotation",value:s,range:a})},!1),r}async showAnnotation(e){let{value:t}=e,{index:r,anchor:i}=await this.goTo(t),{doc:s}=this.#o(r),a=i(s);this.emit?.({type:"show-annotation",value:t,range:a})}getCFI(e,t){if(!t)return"";let r=this.book.sections[e].cfi??yr.fromIndex(e);return Vi(r,Gi(t))}resolveCFI(e){if(this.book.resolveCFI)return this.book.resolveCFI(e);{let t=nt(e);return{index:yr.toIndex((t.parent??t).shift()),anchor:s=>hn(s,t)}}}resolveNavigation(e){try{return typeof e=="number"?{index:e}:Ht.test(e)?this.resolveCFI(e):this.book.resolveHref(e)}catch{}}async goTo(e){let t=this.resolveNavigation(e);try{return await this.renderer.goTo(t),t}catch{}}async goToFraction(e){let[t,r]=this.#e.getSection(e);return this.renderer.goTo({index:t,anchor:r})}async select(e){try{let t=await this.resolveNavigation(e);await this.renderer.goTo({...t,select:!0})}catch{}}goLeft(){return this.book.dir==="rtl"?this.renderer.next():this.renderer.prev()}goRight(){return this.book.dir==="rtl"?this.renderer.prev():this.renderer.next()}setAppearance({layout:e,css:t}){this.isFixedLayout||(Object.assign(this.renderer.layout,e),this.#r=t,this.renderer.setStyle(t),this.renderer.render())}async*#d(e,t,r){let i=await this.book.sections[r].createDocument();for(let{range:s,excerpt:a}of e(i,t))yield{cfi:this.getCFI(r,s),excerpt:a}}async*#c(e,t){let{sections:r}=this.book;for(let[i,{createDocument:s}]of r.entries()){if(!s)continue;let a=await s(),o=Array.from(e(a,t),({range:c,excerpt:u})=>({cfi:this.getCFI(i,c),excerpt:u}));yield{progress:(i+1)/r.length},o.length&&(yield{index:i,subitems:o})}}async*search(e){let{query:t,index:r}=e,i=rs(Vl,{defaultLocale:this.language,...e}),s=r!=null?this.#d(i,t,r):this.#c(i,t);for await(let a of s)yield"subitems"in a?{label:this.#t.getProgress(a.index)?.label??"",subitems:a.subitems}:a}};var ls=n=>document.createElementNS("http://www.w3.org/2000/svg",n),ql=()=>{let n=ls("svg");n.setAttribute("viewBox","0 0 13 10"),n.setAttribute("width","13"),n.setAttribute("height","13");let e=ls("polygon");return e.setAttribute("points","2 1, 12 1, 7 9"),n.append(e),n},Xl=(n,e,t)=>{let r=0,i=()=>`toc-element-${r++}`,s=({label:a,href:o,subitems:l},c=0)=>{let u=document.createElement(o?"a":"span");u.innerText=a,u.setAttribute("role","treeitem"),u.tabIndex=-1,u.style.paddingInlineStart=`${(c+1)*24}px`,n.push(u),o?(e.has(o)||e.set(o,u),u.href=o,u.onclick=h=>{h.preventDefault(),t(o)}):u.onclick=h=>u.firstElementChild?.onclick(h);let d=document.createElement("li");if(d.setAttribute("role","none"),d.append(u),l?.length){u.setAttribute("aria-expanded","false");let h=ql();h.onclick=w=>{w.preventDefault(),w.stopPropagation();let f=u.getAttribute("aria-expanded");u.setAttribute("aria-expanded",f==="true"?"false":"true")},u.prepend(h);let m=document.createElement("ol");m.id=i(),m.setAttribute("role","group"),u.setAttribute("aria-owns",m.id),m.replaceChildren(...l.map(w=>s(w,c+1))),d.append(m)}return d};return s},cs=(n,e)=>{let t=document.createElement("ol");t.setAttribute("role","tree");let r=[],i=new Map,s=Xl(r,i,e);t.replaceChildren(...n.map(w=>s(w)));let a=w=>w?.getAttribute("role")==="treeitem",o=function*(w){for(let f=w.parentNode;f!==t;f=f.parentNode){let p=f.previousElementSibling;a(p)&&(yield p)}},l,c;t.addEventListener("focusout",()=>{if(l){if(c&&(c.tabIndex=-1),l.offsetParent){l.tabIndex=0;return}for(let w of o(l))if(w.offsetParent){w.tabIndex=0,c=w;break}}});let u=w=>{l&&(l.removeAttribute("aria-current"),l.tabIndex=-1);let f=i.get(w);if(!f){l=r[0],l.tabIndex=0;return}for(let p of o(f))p.setAttribute("aria-expanded","true");f.setAttribute("aria-current","page"),f.tabIndex=0,f.scrollIntoView({behavior:"smooth",block:"center"}),l=f},d=w=>a(w)&&w.offsetParent?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP,h=document.createTreeWalker(t,1,{acceptNode:d}),m=w=>(h.currentNode=w,h);for(let w of r)w.addEventListener("keydown",f=>{let p=!1,{currentTarget:g,key:b}=f;switch(b){case" ":case"Enter":g.click(),p=!0;break;case"ArrowDown":m(g).nextNode()?.focus(),p=!0;break;case"ArrowUp":m(g).previousNode()?.focus(),p=!0;break;case"ArrowLeft":g.getAttribute("aria-expanded")==="true"?g.setAttribute("aria-expanded","false"):o(g).next()?.value?.focus(),p=!0;break;case"ArrowRight":g.getAttribute("aria-expanded")==="true"?m(g).nextNode()?.focus():g.getAttribute("aria-owns")&&g.setAttribute("aria-expanded","true"),p=!0;break;case"Home":r[0].focus(),p=!0;break;case"End":{let y=r[r.length-1];y.offsetParent?y.focus():m(y).previousNode()?.focus(),p=!0;break}}p&&(f.preventDefault(),f.stopPropagation())});return{element:t,setCurrentHref:u}};var Gl=(n,e,t)=>{let r=document.createElement("ul");r.setAttribute("role","group"),r.setAttribute("aria-label",n);let i=new Map,s=a=>{t(a);let o=i.get(a);for(let l of r.children)l.setAttribute("aria-checked",l===o?"true":"false")};for(let[a,o]of e){let l=document.createElement("li");l.setAttribute("role","menuitemradio"),l.innerText=a,l.onclick=()=>s(o),i.set(o,l),r.append(l)}return{element:r,select:s}},us=n=>{let e={},t=document.createElement("ul");t.setAttribute("role","menu");let r=()=>t.classList.remove("show"),i=s=>(...a)=>(r(),s(...a));for(let{name:s,label:a,type:o,items:l,onclick:c}of n){let u=o==="radio"?Gl(a,l,i(c)):null;s&&(e[s]=u),t.append(u.element)}return window.addEventListener("blur",()=>r()),window.addEventListener("click",s=>{t.parentNode.contains(s.target)||r()}),{element:t,groups:e}};var _r=(n,e,t)=>Math.min(e,Math.max(n,t));var ds=n=>document.createElementNS("http://www.w3.org/2000/svg",n),Zl=n=>{let t=ds("svg");t.setAttribute("aria-hidden","true"),t.setAttribute("width",20),t.setAttribute("height",10);let r=ds("polygon");return r.setAttribute("points",n?"0 11, 10 0, 20 11":"0 0, 10 11, 20 0"),t.classList.add(n?"popover-arrow-down":"popover-arrow-up"),t.append(r),t},hs=(n,e,{x:t,y:r},i)=>{let s=i==="down",a=e+10,o=document.createElement("div");Object.assign(o.style,{position:"absolute",left:"0",top:"0",width:"100vw",height:"100vh"});let l=Zl(s),c=_r(0,window.innerHeight-(s?e:a),s?r+10:r-a);Object.assign(l.style,{position:"absolute",left:`${_r(6,window.innerWidth-20-6,t-10)}px`,top:`${s?c-10:c+e}px`});let u=document.createElement("div");return u.setAttribute("role","dialog"),u.classList.add("popover"),Object.assign(u.style,{position:"absolute",boxSizing:"border-box",overflow:"hidden",left:`${_r(0,window.innerWidth-n,t-n/2)}px`,top:`${c}px`,width:`${n}px`,height:`${e}px`}),o.addEventListener("click",()=>{o.parentNode.removeChild(o),l.parentNode.removeChild(l),u.parentNode.removeChild(u)}),{popover:u,arrow:l,overlay:o}};var fs=({entries:n,loadBlob:e,getSize:t},r)=>{let i=new Map,s=new Map,a=async d=>{if(i.has(d))return i.get(d);let h=URL.createObjectURL(await e(d)),m=URL.createObjectURL(new Blob([`<img src="${h}">`],{type:"text/html"}));return s.set(d,[h,m]),i.set(d,m),m},o=d=>{s.get(d)?.forEach?.(h=>URL.revokeObjectURL(h)),s.delete(d),i.delete(d)},l=[".jpg",".jpeg",".png",".gif",".bmp",".webp",".svg"],c=n.map(d=>d.filename).filter(d=>l.some(h=>d.endsWith(h))).sort(),u={};return u.getCover=()=>e(c[0]),u.metadata={title:r.name},u.sections=c.map(d=>({id:d,load:()=>a(d),unload:()=>o(d),size:t(d)})),u.toc=c.map(d=>({label:d,href:d})),u.rendition={layout:"pre-paginated"},u.resolveHref=d=>({index:u.sections.findIndex(h=>h.id===d)}),u.splitTOCHref=d=>[d,null],u.getTOCFragment=d=>d.documentElement,u};var ps=n=>n?.trim()?.replace(/\s{2,}/g," "),Me=n=>ps(n?.textContent),vn={XLINK:"http://www.w3.org/1999/xlink",EPUB:"http://www.idpf.org/2007/ops"},_n={XML:"application/xml",XHTML:"application/xhtml+xml"},je={strong:["strong","self"],emphasis:["em","self"],style:["span","self"],a:"anchor",strikethrough:["s","self"],sub:["sub","self"],sup:["sup","self"],code:["code","self"],image:"image"},Kl={tr:["tr",["align"]],th:["th",["colspan","rowspan","align","valign"]],td:["td",["colspan","rowspan","align","valign"]]},gs={epigraph:["blockquote"],subtitle:["h2",je],"text-author":["p",je],date:["p",je],stanza:"stanza"},Sn={title:["header",{p:["h1",je],"empty-line":["br"]}],epigraph:["blockquote","self"],image:"image",annotation:["aside"],section:["section","self"],p:["p",je],poem:["blockquote",gs],subtitle:["h2",je],cite:["blockquote","self"],"empty-line":["br"],table:["table",Kl],"text-author":["p",je]};gs.epigraph.push(Sn);var Yl={image:"image",title:["section",{p:["h1",je],"empty-line":["br"]}],epigraph:["section",Sn],section:["section",Sn]},ms=n=>{let e=n.getAttributeNS(vn.XLINK,"href"),[,t]=e.split("#"),r=n.getRootNode().getElementById(t);return r?`data:${r.getAttribute("content-type")};base64,${r.textContent}`:e},Ar=class{constructor(e){this.fb2=e,this.doc=document.implementation.createDocument(vn.XHTML,"html")}image(e){let t=this.doc.createElement("img");return t.alt=e.getAttribute("alt"),t.title=e.getAttribute("title"),t.setAttribute("src",ms(e)),t}anchor(e){let t=this.convert(e,{a:["a",je]});return t.setAttribute("href",e.getAttributeNS(vn.XLINK,"href")),e.getAttribute("type")==="note"&&t.setAttributeNS(vn.EPUB,"epub:type","noteref"),t}stanza(e){let t=this.convert(e,{stanza:["p",{title:["header",{p:["strong",je],"empty-line":["br"]}],subtitle:["p",je]}]});for(let r of e.children)r.nodeName==="v"&&(t.append(this.doc.createTextNode(r.textContent)),t.append(this.doc.createElement("br")));return t}convert(e,t){if(e.nodeType===3)return this.doc.createTextNode(e.textContent);if(e.nodeType===4)return this.doc.createCDATASection(e.textContent);if(e.nodeType===8)return this.doc.createComment(e.textContent);let r=t?.[e.nodeName];if(!r)return null;if(typeof r=="string")return this[r](e);let[i,s]=r,a=this.doc.createElement(i);if(e.id&&(a.id=e.id),a.classList.add(e.nodeName),Array.isArray(s))for(let c of s)a.setAttribute(c,e.getAttribute(c));let o=s==="self"?t:Array.isArray(s)?null:s,l=e.firstChild;for(;l;){let c=this.convert(l,o);c&&a.append(c),l=l.nextSibling}return a}},Jl=async n=>{let e=await n.arrayBuffer(),t=new TextDecoder("utf-8").decode(e),r=new DOMParser,i=r.parseFromString(t,_n.XML),s=i.xmlEncoding||t.match(/^<\?xml\s+version\s*=\s*["']1.\d+"\s+encoding\s*=\s*["']([A-Za-z0-9._-]*)["']/)?.[1];if(s&&s.toLowerCase()!=="utf-8"){let a=new TextDecoder(s).decode(e);return r.parseFromString(a,_n.XML)}return i},Ql=URL.createObjectURL(new Blob([`
@namespace epub "http://www.idpf.org/2007/ops";
body > img, section > img {
    display: block;
    margin: auto;
}
.title {
    text-align: center;
}
body > section > .title, body.notesBodyType > .title {
    margin: 3em 0;
}
body.notesBodyType > section .title {
    text-align: left;
    margin: 1em 0;
}
p {
    text-indent: 1em;
    margin: 0;
}
:not(p) + p, p:first-child {
    text-indent: 0;
}
.poem p {
    text-indent: 0;
    margin: 1em 0;
}
.text-author, .date {
    text-align: end;
}
.text-author:before {
    content: "\u2014";
}
table {
    border-collapse: collapse;
}
td, th {
    padding: .25em;
}
a[epub|type~="noteref"] {
    font-size: .75em;
    vertical-align: super;
}
body:not(.notesBodyType) > .title, body:not(.notesBodyType) > .epigraph {
    margin: 3em 0;
}
`],{type:"text/css"})),ec=n=>`<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml">
    <head><link href="${Ql}" rel="stylesheet" type="text/css"/></head>
    <body>${n}</body>
</html>`,Sr="data-foliate-id",Er=async n=>{let e={},t=await Jl(n),r=new Ar(t),i=h=>t.querySelector(h),s=h=>[...t.querySelectorAll(h)],a=h=>{let m=Me(h.querySelector("nickname"));if(m)return m;let w=Me(h.querySelector("first-name")),f=Me(h.querySelector("middle-name")),p=Me(h.querySelector("last-name")),g=[w,f,p].filter(y=>y).join(" "),b=p?[p,[w,f].filter(y=>y).join(" ")].join(", "):null;return{name:g,sortAs:b}},o=h=>h?.getAttribute("value")??Me(h),l=i("title-info annotation");e.metadata={title:Me(i("title-info book-title")),identifier:Me(i("document-info id")),language:Me(i("title-info lang")),author:s("title-info author").map(a),translator:s("title-info translator").map(a),producer:s("document-info author").map(a).concat(s("document-info program-used").map(Me)),publisher:Me(i("publish-info publisher")),published:o(i("title-info date")),modified:o(i("document-info date")),description:l?r.convert(l,{annotation:["div",Sn]}).innerHTML:null,subject:s("title-info genre").map(Me)},e.getCover=()=>fetch(ms(i("coverpage image"))).then(h=>h.blob());let c=Array.from(t.querySelectorAll("body"),h=>{let m=r.convert(h,{body:["body",Yl]});return[Array.from(m.children,w=>{let f=[w,...w.querySelectorAll("[id]")].map(p=>p.id);return{el:w,ids:f}}),m]}),u=c[0][0].map(({el:h,ids:m})=>{let w=Array.from(h.querySelectorAll(":scope > section > .title"),(f,p)=>(f.setAttribute(Sr,p),{title:Me(f),index:p}));return{ids:m,titles:w,el:h}}).concat(c.slice(1).map(([h,m])=>{let w=h.map(f=>f.ids).flat();return m.classList.add("notesBodyType"),{ids:w,el:m,linear:"no"}})).map(({ids:h,titles:m,el:w,linear:f})=>{let p=ec(w.outerHTML),g=new Blob([p],{type:_n.XHTML}),b=URL.createObjectURL(g),y=ps(w.querySelector(".title, .subtitle, p")?.textContent??(w.classList.contains("title")?w.textContent:""));return{ids:h,title:y,titles:m,load:()=>b,createDocument:()=>new DOMParser().parseFromString(p,_n.XHTML),size:g.size-Array.from(w.querySelectorAll("[src]"),E=>E.getAttribute("src")?.length??0).reduce((E,_)=>E+_,0),linear:f}}),d=new Map;return e.sections=u.map((h,m)=>{let{ids:w,load:f,createDocument:p,size:g,linear:b}=h;for(let y of w)y&&d.set(y,m);return{id:m,load:f,createDocument:p,size:g,linear:b}}),e.toc=u.map(({title:h,titles:m},w)=>{let f=w.toString();return{label:h,href:f,subitems:m?.length?m.map(({title:p,index:g})=>({label:p,href:`${f}#${g}`})):null}}).filter(h=>h),e.resolveHref=h=>{let[m,w]=h.split("#");return m?{index:Number(m),anchor:f=>f.querySelector(`[${Sr}="${w}"]`)}:{index:d.get(w),anchor:f=>f.getElementById(w)}},e.splitTOCHref=h=>h?.split("#")?.map(m=>Number(m))??[],e.getTOCFragment=(h,m)=>h.querySelector(`[${Sr}="${m}"]`),e};var ct=n=>{if(!n)return"";let e=document.createElement("textarea");return e.innerHTML=n,e.value},xt={XML:"application/xml",XHTML:"application/xhtml+xml",HTML:"text/html",CSS:"text/css",SVG:"image/svg+xml"},tc={name:[0,32,"string"],type:[60,4,"string"],creator:[64,4,"string"],numRecords:[76,2,"uint"]},nc={compression:[0,2,"uint"],numTextRecords:[8,2,"uint"],recordSize:[10,2,"uint"],encryption:[12,2,"uint"]},rc={magic:[16,4,"string"],length:[20,4,"uint"],type:[24,4,"uint"],encoding:[28,4,"uint"],uid:[32,4,"uint"],version:[36,4,"uint"],titleOffset:[84,4,"uint"],titleLength:[88,4,"uint"],localeRegion:[94,1,"uint"],localeLanguage:[95,1,"uint"],resourceStart:[108,4,"uint"],huffcdic:[112,4,"uint"],numHuffcdic:[116,4,"uint"],exthFlag:[128,4,"uint"],trailingFlags:[240,4,"uint"],indx:[244,4,"uint"]},ic={resourceStart:[108,4,"uint"],fdst:[192,4,"uint"],numFdst:[196,4,"uint"],frag:[248,4,"uint"],skel:[252,4,"uint"],guide:[260,4,"uint"]},sc={magic:[0,4,"string"],length:[4,4,"uint"],count:[8,4,"uint"]},ws={magic:[0,4,"string"],length:[4,4,"uint"],type:[8,4,"uint"],idxt:[20,4,"uint"],numRecords:[24,4,"uint"],encoding:[28,4,"uint"],language:[32,4,"uint"],total:[36,4,"uint"],ordt:[40,4,"uint"],ligt:[44,4,"uint"],numLigt:[48,4,"uint"],numCncx:[52,4,"uint"]},ac={magic:[0,4,"string"],length:[4,4,"uint"],numControlBytes:[8,4,"uint"]},oc={magic:[0,4,"string"],offset1:[8,4,"uint"],offset2:[12,4,"uint"]},lc={magic:[0,4,"string"],length:[4,4,"uint"],numEntries:[8,4,"uint"],codeLength:[12,4,"uint"]},cc={magic:[0,4,"string"],numEntries:[8,4,"uint"]},uc={flags:[8,4,"uint"],dataStart:[12,4,"uint"],keyLength:[16,4,"uint"],keyStart:[20,4,"uint"]},dc={1252:"windows-1252",65001:"utf-8"},bs={100:["creator","string",!0],101:["publisher"],103:["description"],104:["isbn"],105:["subject","string",!0],106:["date"],108:["contributor","string",!0],109:["rights"],110:["subjectCode","string",!0],112:["source","string",!0],113:["asin"],121:["boundary","uint"],122:["fixedLayout"],125:["numResources","uint"],126:["originalResolution"],127:["zeroGutter"],128:["zeroMargin"],129:["coverURI"],132:["regionMagnification"],201:["coverOffset","uint"],202:["thumbnailOffset","uint"],503:["title"],524:["language","string",!0],527:["pageProgressionDirection"]},hc={1:["ar","ar-SA","ar-IQ","ar-EG","ar-LY","ar-DZ","ar-MA","ar-TN","ar-OM","ar-YE","ar-SY","ar-JO","ar-LB","ar-KW","ar-AE","ar-BH","ar-QA"],2:["bg"],3:["ca"],4:["zh","zh-TW","zh-CN","zh-HK","zh-SG"],5:["cs"],6:["da"],7:["de","de-DE","de-CH","de-AT","de-LU","de-LI"],8:["el"],9:["en","en-US","en-GB","en-AU","en-CA","en-NZ","en-IE","en-ZA","en-JM",null,"en-BZ","en-TT","en-ZW","en-PH"],10:["es","es-ES","es-MX",null,"es-GT","es-CR","es-PA","es-DO","es-VE","es-CO","es-PE","es-AR","es-EC","es-CL","es-UY","es-PY","es-BO","es-SV","es-HN","es-NI","es-PR"],11:["fi"],12:["fr","fr-FR","fr-BE","fr-CA","fr-CH","fr-LU","fr-MC"],13:["he"],14:["hu"],15:["is"],16:["it","it-IT","it-CH"],17:["ja"],18:["ko"],19:["nl","nl-NL","nl-BE"],20:["no","nb","nn"],21:["pl"],22:["pt","pt-BR","pt-PT"],23:["rm"],24:["ro"],25:["ru"],26:["hr",null,"sr"],27:["sk"],28:["sq"],29:["sv","sv-SE","sv-FI"],30:["th"],31:["tr"],32:["ur"],33:["id"],34:["uk"],35:["be"],36:["sl"],37:["et"],38:["lv"],39:["lt"],41:["fa"],42:["vi"],43:["hy"],44:["az"],45:["eu"],46:["hsb"],47:["mk"],48:["st"],49:["ts"],50:["tn"],52:["xh"],53:["zu"],54:["af"],55:["ka"],56:["fo"],57:["hi"],58:["mt"],59:["se"],62:["ms"],63:["kk"],65:["sw"],67:["uz",null,"uz-UZ"],68:["tt"],69:["bn"],70:["pa"],71:["gu"],72:["or"],73:["ta"],74:["te"],75:["kn"],76:["ml"],77:["as"],78:["mr"],79:["sa"],82:["cy","cy-GB"],83:["gl","gl-ES"],87:["kok"],97:["ne"],98:["fy"]},kn=(n,e)=>{let t=new n.constructor(n.length+e.length);return t.set(n),t.set(e,n.length),t},Ss=(n,e,t)=>{let r=new n.constructor(n.length+e.length+t.length);return r.set(n),r.set(e,n.length),r.set(t,n.length+e.length),r},fc=new TextDecoder,Wt=n=>fc.decode(n),Ae=n=>{if(!n)return;let e=n.byteLength,t=e===4?"getUint32":e===2?"getUint16":"getUint8";return new DataView(n)[t](0)},Ue=(n,e)=>Object.fromEntries(Array.from(Object.entries(n)).map(([t,[r,i,s]])=>[t,(s==="string"?Wt:Ae)(e.slice(r,r+i))])),Rr=n=>new TextDecoder(dc[n]),An=(n,e=0)=>{let t=0,r=0;for(let i of n.subarray(e,e+4))if(t=t<<7|(i&127)>>>0,r++,i&128)break;return{value:t,length:r}},pc=n=>{let e=0;for(let t of n.subarray(-4))t&128&&(e=0),e=e<<7|t&127;return e},As=n=>{let e=0;for(;n>0;n=n>>1)(n&1)===1&&e++;return e},gc=n=>{let e=0;for(;!(n&1);)n=n>>1,e++;return e},mc=n=>{let e=[];for(let t=0;t<n.length;t++){let r=n[t];if(r===0)e.push(0);else if(r<=8)for(let i of n.subarray(t+1,(t+=r)+1))e.push(i);else if(r<=127)e.push(r);else if(r<=191){let i=r<<8|n[t+++1],s=(i&16383)>>>3,a=(i&7)+3;for(let o=0;o<a;o++)e.push(e[e.length-s])}else e.push(32,r^128)}return Uint8Array.from(e)},wc=(n,e)=>{let t=e>>3,r=e+32,i=r>>3,s=0n;for(let a=t;a<=i;a++)s=s<<8n|BigInt(n[a]??0);return s>>8n-BigInt(r&7)&0xffffffffn},bc=async(n,e)=>{let t=await e(n.huffcdic),{magic:r,offset1:i,offset2:s}=Ue(oc,t);if(r!=="HUFF")throw new Error("Invalid HUFF record");let a=Array.from({length:256},(u,d)=>i+d*4).map(u=>Ae(t.slice(u,u+4))).map(u=>[u&128,u&31,u>>>8]),o=[null].concat(Array.from({length:32},(u,d)=>s+d*8).map(u=>[Ae(t.slice(u,u+4)),Ae(t.slice(u+4,u+8))])),l=[];for(let u=1;u<n.numHuffcdic;u++){let d=await e(n.huffcdic+u),h=Ue(lc,d);if(h.magic!=="CDIC")throw new Error("Invalid CDIC record");let m=Math.min(1<<h.codeLength,h.numEntries-l.length),w=d.slice(h.length);for(let f=0;f<m;f++){let p=Ae(w.slice(f*2,f*2+2)),g=Ae(w.slice(p,p+2)),b=g&32767,y=g&32768,E=new Uint8Array(w.slice(p+2,p+2+b));l.push([E,y])}}let c=u=>{let d=new Uint8Array,h=u.byteLength*8;for(let m=0;m<h;){let w=Number(wc(u,m)),[f,p,g]=a[w>>>24];if(!f){for(;w>>>32-p<o[p][0];)p+=1;g=o[p][1]}if((m+=p)>h)break;let b=g-(w>>>32-p),[y,E]=l[b];E||(y=c(y),l[b]=[y,!0]),d=kn(d,y)}return d};return c},En=async(n,e)=>{let t=await e(n),r=Ue(ws,t);if(r.magic!=="INDX")throw new Error("Invalid INDX record");let i=Rr(r.encoding),s=t.slice(r.length),a=Ue(ac,s);if(a.magic!=="TAGX")throw new Error("Invalid TAGX section");let o=(a.length-12)/4,l=Array.from({length:o},(h,m)=>new Uint8Array(s.slice(12+m*4,12+m*4+4))),c={},u=0;for(let h=0;h<r.numCncx;h++){let m=await e(n+r.numRecords+h+1),w=new Uint8Array(m);for(let f=0;f<w.byteLength;){let p=f,{value:g,length:b}=An(w,f);f+=b;let y=m.slice(f,f+g);f+=g,c[u+p]=i.decode(y)}u+=65536}let d=[];for(let h=0;h<r.numRecords;h++){let m=await e(n+1+h),w=new Uint8Array(m),f=Ue(ws,m);if(f.magic!=="INDX")throw new Error("Invalid INDX record");for(let p=0;p<f.numRecords;p++){let g=f.idxt+4+2*p,b=Ae(m.slice(g,g+2)),y=Ae(m.slice(b,b+1)),E=Wt(m.slice(b+1,b+1+y)),_=[],x=b+1+y,S=0,v=x+a.numControlBytes;for(let[A,T,D,R]of l){if(R&1){S++;continue}let U=x+S,z=Ae(m.slice(U,U+1))&D;if(z===D)if(As(D)>1){let{value:O,length:M}=An(w,v);_.push([A,null,O,T]),v+=M}else _.push([A,1,null,T]);else _.push([A,z>>gc(D),null,T])}let k={};for(let[A,T,D,R]of _){let U=[];if(T!=null)for(let z=0;z<T*R;z++){let{value:O,length:M}=An(w,v);U.push(O),v+=M}else{let z=0;for(;z<D;){let{value:O,length:M}=An(w,v);U.push(O),v+=M,z+=M}}k[A]=U}d.push({name:E,tagMap:k})}}return{table:d,cncx:c}},yc=async(n,e)=>{let{table:t,cncx:r}=await En(n,e),i=t.map(({tagMap:a},o)=>({index:o,offset:a[1]?.[0],size:a[2]?.[0],label:r[a[3]]??"",headingLevel:a[4]?.[0],pos:a[6],parent:a[21]?.[0],firstChild:a[22]?.[0],lastChild:a[23]?.[0]})),s=a=>(a.firstChild==null||(a.children=i.filter(o=>o.parent===a.index).map(s)),a);return i.filter(a=>a.headingLevel===0).map(s)},xc=(n,e)=>{let{magic:t,count:r}=Ue(sc,n);if(t!=="EXTH")throw new Error("Invalid EXTH header");let i=Rr(e),s={},a=12;for(let o=0;o<r;o++){let l=Ae(n.slice(a,a+4)),c=Ae(n.slice(a+4,a+8));if(l in bs){let[u,d,h]=bs[l],m=n.slice(a+8,a+c),w=d==="uint"?Ae(m):i.decode(m);h?(s[u]??=[],s[u].push(w)):s[u]=w}a+=c}return s},vc=async(n,e)=>{let{flags:t,dataStart:r,keyLength:i,keyStart:s}=Ue(uc,n),a=new Uint8Array(n.slice(r));if(t&2){let l=i===16?1024:1040,c=new Uint8Array(n.slice(s,s+i)),u=Math.min(l,a.length);for(var o=0;o<u;o++)a[o]=a[o]^c[o%c.length]}if(t&1)try{return await e(a)}catch{}return a},Es=async n=>Wt(await n.slice(60,68).arrayBuffer())==="BOOKMOBI",kr=class{#e;#t;pdb;async open(e){this.#e=e;let t=Ue(tc,await e.slice(0,78).arrayBuffer());this.pdb=t;let r=await e.slice(78,78+t.numRecords*8).arrayBuffer();this.#t=Array.from({length:t.numRecords},(i,s)=>Ae(r.slice(s*8,s*8+4))).map((i,s,a)=>[i,a[s+1]])}loadRecord(e){let t=this.#t[e];if(!t)throw new RangeError("Record index out of bounds");return this.#e.slice(...t).arrayBuffer()}async loadMagic(e){let t=this.#t[e][0];return Wt(await this.#e.slice(t,t+4).arrayBuffer())}},Tn=class extends kr{#e=0;#t;#n;#r;#i;#s;constructor({unzlib:e}){super(),this.unzlib=e}async open(e){await super.open(e),this.headers=this.#a(await super.loadRecord(0)),this.#t=this.headers.mobi.resourceStart;let t=this.headers.mobi.version>=8;if(!t){let r=this.headers.exth?.boundary;if(r<4294967295)try{this.headers=this.#a(await super.loadRecord(r)),this.#e=r,t=!0}catch{}}return await this.#o(),t?new Cr(this).init():new Tr(this).init()}#a(e){let t=Ue(nc,e),r=Ue(rc,e);if(r.magic!=="MOBI")throw new Error("Missing MOBI header");let{titleOffset:i,titleLength:s,localeLanguage:a,localeRegion:o}=r;r.title=e.slice(i,i+s);let l=hc[a];r.language=l?.[o>>2]??l?.[0];let c=r.exthFlag&64?xc(e.slice(r.length+16),r.encoding):null,u=r.version>=8?Ue(ic,e):null;return{palmdoc:t,mobi:r,exth:c,kf8:u}}async#o(){let{palmdoc:e,mobi:t}=this.headers;this.#n=Rr(t.encoding),this.#r=new TextEncoder;let{compression:r}=e;if(this.#i=r===1?o=>o:r===2?mc:r===17480?await bc(t,this.loadRecord.bind(this)):null,!this.#i)throw new Error("Unknown compression type");let{trailingFlags:i}=t,s=i&1,a=As(i>>>1);this.#s=o=>{for(let l=0;l<a;l++){let c=pc(o);o=o.subarray(0,-c)}if(s){let l=(o[o.length-1]&3)+1;o=o.subarray(0,-l)}return o}}decode(...e){return this.#n.decode(...e)}encode(...e){return this.#r.encode(...e)}loadRecord(e){return super.loadRecord(this.#e+e)}loadMagic(e){return super.loadMagic(this.#e+e)}loadText(e){return this.loadRecord(e+1).then(t=>new Uint8Array(t)).then(this.#s).then(this.#i)}async loadResource(e){let t=await super.loadRecord(this.#t+e),r=Wt(t.slice(0,4));return r==="FONT"?vc(t,this.unzlib):r==="VIDE"||r==="AUDI"?t.slice(12):t}getNCX(){let e=this.headers.mobi.indx;if(e<4294967295)return yc(e,this.loadRecord.bind(this))}getMetadata(){let{mobi:e,exth:t}=this.headers;return{identifier:e.uid.toString(),title:ct(t?.title||this.decode(e.title)),author:t?.creator?.map(ct),publisher:ct(t?.publisher),language:t?.language??e.language,published:t?.date,description:ct(t?.description),subject:t?.subject?.map(ct),rights:ct(t?.rights)}}async getCover(){let{exth:e}=this.headers,t=e?.coverOffset<4294967295?e?.coverOffset:e?.thumbnailOffset<4294967295?e?.thumbnailOffset:null;if(t!=null){let r=await this.loadResource(t);return new Blob([r])}}},ys=/<\s*(?:mbp:)?pagebreak[^>]*>/gi,_c=/<[^<>]+filepos=['"]{0,1}(\d+)[^<>]*>/gi,Tr=class{parser=new DOMParser;serializer=new XMLSerializer;#e=new Map;#t=new Map;#n=new Map;#r;#i=[];#s=xt.HTML;constructor(e){this.mobi=e}async init(){let e=new Uint8Array;for(let i=0;i<this.mobi.headers.palmdoc.numTextRecords;i++)e=kn(e,await this.mobi.loadText(i));let t=Array.from(new Uint8Array(e),i=>String.fromCharCode(i)).join("");this.#r=[0].concat(Array.from(t.matchAll(ys),i=>i.index)).map((i,s,a)=>t.slice(i,a[s+1])).map(i=>Uint8Array.from(i,s=>s.charCodeAt(0))).map(i=>({book:this,raw:i})).reduce((i,s)=>{let a=i[i.length-1];return s.start=a?.end??0,s.end=s.start+s.raw.byteLength,i.concat(s)},[]),this.sections=this.#r.map((i,s)=>({id:s,load:()=>this.loadSection(i),createDocument:()=>this.createDocument(i),size:i.end-i.start}));let r=[];try{let i=await this.mobi.getNCX(),s=({label:a,offset:o,children:l})=>{let c=o.toString().padStart(10,"0"),u=`filepos:${c}`;return r.push(c),a=ct(a),{label:a,href:u,subitems:l?.map(s)}};if(this.toc=i?.map(s),this.landmarks=await this.getGuide(),!this.toc){let a=this.landmarks.find(({type:o})=>o?.includes("toc"))?.href;if(a){let{index:o}=this.resolveHref(a),l=await this.sections[o].createDocument();this.toc=Array.from(l.querySelectorAll("a[filepos]"),c=>({label:c.innerText?.trim(),href:`filepos:${c.getAttribute("filepos")}`}))}}}catch{}return this.#i=[...new Set(r.concat(Array.from(t.matchAll(_c),i=>i[1])))].map(i=>({filepos:i,number:Number(i)})).sort((i,s)=>i.number-s.number),this.metadata=this.mobi.getMetadata(),this.getCover=this.mobi.getCover.bind(this.mobi),this}async getGuide(){let e=await this.createDocument(this.#r[0]);return Array.from(e.getElementsByTagName("reference"),t=>({label:t.getAttribute("title"),type:t.getAttribute("type")?.split(/\s/),href:`filepos:${t.getAttribute("filepos")}`}))}async loadResource(e){if(this.#e.has(e))return this.#e.get(e);let t=await this.mobi.loadResource(e),r=URL.createObjectURL(new Blob([t]));return this.#e.set(e,r),r}async loadRecindex(e){return this.loadResource(Number(e)-1)}async replaceResources(e){for(let t of e.querySelectorAll("img[recindex]")){let r=t.getAttribute("recindex");try{t.src=await this.loadRecindex(r)}catch{}}for(let t of e.querySelectorAll("[mediarecindex]")){let r=t.getAttribute("mediarecindex"),i=t.getAttribute("recindex");try{t.src=await this.loadRecindex(r),i&&(t.poster=await this.loadRecindex(i))}catch{}}for(let t of e.querySelectorAll("[filepos]")){let r=t.getAttribute("filepos");t.href=`filepos:${r}`}}async loadText(e){if(this.#t.has(e))return this.#t.get(e);let{raw:t}=e,r=this.#i.filter(({number:a})=>a>=e.start&&a<e.end).map(a=>({...a,offset:a.number-e.start})),i=t;r.length&&(i=t.subarray(0,r[0].offset),r.forEach(({filepos:a,offset:o},l)=>{let c=r[l+1],u=this.mobi.encode(`<a id="filepos${a}"></a>`);i=Ss(i,u,t.subarray(o,c?.offset))}));let s=this.mobi.decode(i).replaceAll(ys,"");return this.#t.set(e,s),s}async createDocument(e){let t=await this.loadText(e);return this.parser.parseFromString(t,this.#s)}async loadSection(e){if(this.#n.has(e))return this.#n.get(e);let t=await this.createDocument(e),r=t.createElement("style");t.head.append(r),r.append(t.createTextNode(`blockquote {
            margin-block-start: 0;
            margin-block-end: 0;
            margin-inline-start: 1em;
            margin-inline-end: 0;
        }`)),await this.replaceResources(t);let i=this.serializer.serializeToString(t),s=URL.createObjectURL(new Blob([i],{type:this.#s}));return this.#n.set(e,s),s}resolveHref(e){let t=e.match(/filepos:(.*)/)[1],r=Number(t);return{index:this.#r.findIndex(a=>a.end>r),anchor:a=>a.getElementById(`filepos${t}`)}}splitTOCHref(e){let t=e.match(/filepos:(.*)/)[1],r=Number(t);return[this.#r.findIndex(s=>s.end>r),`filepos${t}`]}getTOCFragment(e,t){return e.getElementById(t)}isExternal(e){return/^(?!blob|filepos)\w+:/i.test(e)}},ks=/kindle:(flow|embed):(\w+)(?:\?mime=(\w+\/[-+.\w]+))?/,Sc=/kindle:pos:fid:(\w+):off:(\w+)/,Ac=n=>{let[e,t,r]=n.match(ks).slice(1);return{resourceType:e,id:parseInt(t,32),type:r}},xs=n=>{let[e,t]=n.match(Sc).slice(1);return{fid:parseInt(e,32),off:parseInt(t,32)}},vs=(n=0,e=0)=>`kindle:pos:fid:${n.toString(32).toUpperCase().padStart(4,"0")}:off:${e.toString(32).toUpperCase().padStart(10,"0")}`,_s=n=>{let e=n.match(/\s(id|name|aid)\s*=\s*['"]([^'"]*)['"]/i);if(!e)return;let[,t,r]=e;return`[${t}="${CSS.escape(r)}"]`},Ec=async(n,e,t)=>{let r=[];n.replace(e,(...s)=>(r.push(s),null));let i=[];for(let s of r)i.push(await t(...s));return n.replace(e,()=>i.shift())},Cr=class{parser=new DOMParser;#e=new Map;#t=new Map;#n=new Map;#r={};#i;#s;#a=new Uint8Array;#o=new Uint8Array;#l=-1;#d=-1;#c=!0;#u=xt.XHTML;constructor(e){this.mobi=e}async init(){let e=this.mobi.loadRecord.bind(this.mobi),{kf8:t}=this.mobi.headers;try{let o=await e(t.fdst),l=Ue(cc,o);if(l.magic!=="FDST")throw new Error("Missing FDST record");let c=Array.from({length:l.numEntries},(u,d)=>12+d*8).map(u=>[Ae(o.slice(u,u+4)),Ae(o.slice(u+4,u+8))]);this.#r.fdstTable=c,this.#s=c[c.length-1][1]}catch{}let r=(await En(t.skel,e)).table.map(({name:o,tagMap:l},c)=>({index:c,name:o,numFrag:l[1][0],offset:l[6][0],length:l[6][1]})),i=await En(t.frag,e),s=i.table.map(({name:o,tagMap:l})=>({insertOffset:parseInt(o),selector:i.cncx[l[2][0]],index:l[4][0],offset:l[6][0],length:l[6][1]}));this.#r.skelTable=r,this.#r.fragTable=s,this.#i=r.reduce((o,l)=>{let c=o[o.length-1],u=c?.fragEnd??0,d=u+l.numFrag,h=s.slice(u,d),m=l.length+h.map(f=>f.length).reduce((f,p)=>f+p),w=(c?.totalLength??0)+m;return o.concat({skel:l,frags:h,fragEnd:d,length:m,totalLength:w})},[]),this.#i.unshift({frags:[]}),this.sections=this.#i.map((o,l)=>o.frags.length?{id:l,load:()=>this.loadSection(o),createDocument:()=>this.createDocument(o),size:o.length}:{linear:"no"});try{let o=await this.mobi.getNCX(),l=({label:c,pos:u,children:d})=>{let[h,m]=u,w=vs(h,m),f=this.#t.get(h);return f?f.push(m):this.#t.set(h,[m]),{label:ct(c),href:w,subitems:d?.map(l)}};this.toc=o?.map(l),this.landmarks=await this.getGuide()}catch{}let{exth:a}=this.mobi.headers;return this.dir=a.pageProgressionDirection,this.rendition={layout:a.fixedLayout==="true"?"pre-paginated":"reflowable",viewport:Object.fromEntries(a.originalResolution?.split("x")?.slice(0,2)?.map((o,l)=>[l?"height":"width",o])??[])},this.metadata=this.mobi.getMetadata(),this.getCover=this.mobi.getCover.bind(this.mobi),this}async getResourcesByMagic(e){let t={},r=this.mobi.headers.kf8.resourceStart,i=this.mobi.pdb.numRecords;for(let s=r;s<i;s++)try{let a=await this.mobi.loadMagic(s),o=e.find(l=>l===a);o&&(t[o]=s)}catch{}return t}async getGuide(){let e=this.mobi.headers.kf8.guide;if(e<4294967295){let t=this.mobi.loadRecord.bind(this.mobi),{table:r,cncx:i}=await En(e,t);return r.map(({name:s,tagMap:a})=>({label:i[a[1][0]]??"",type:s?.split(/\s/),href:vs(a[6]?.[0]??a[3]?.[0])}))}}async loadResourceBlob(e){let{resourceType:t,id:r,type:i}=Ac(e),s=t==="flow"?await this.loadFlow(r):await this.mobi.loadResource(r-1),a=[xt.XHTML,xt.HTML,xt.CSS,xt.SVG].includes(i)?await this.replaceResources(this.mobi.decode(s)):s;return new Blob([a],{type:i})}async loadResource(e){if(this.#e.has(e))return this.#e.get(e);let t=await this.loadResourceBlob(e),r=URL.createObjectURL(t);return this.#e.set(e,r),r}replaceResources(e){let t=new RegExp(ks,"g");return Ec(e,t,this.loadResource.bind(this))}async loadRaw(e,t){let r=t-this.#a.length,i=this.#s==null?1/0:this.#s-this.#o.length-e;if(r<0||r<i){for(;this.#a.length<t;){let a=++this.#l,o=await this.mobi.loadText(a);this.#a=kn(this.#a,o)}return this.#a.slice(e,t)}for(;this.#s-this.#o.length>e;){let a=this.mobi.headers.palmdoc.numTextRecords-1-++this.#d,o=await this.mobi.loadText(a);this.#o=kn(o,this.#o)}let s=this.#s-this.#o.length;return this.#o.slice(e-s,t-s)}loadFlow(e){if(e<4294967295)return this.loadRaw(...this.#r.fdstTable[e])}async loadText(e){let{skel:t,frags:r,length:i}=e,s=await this.loadRaw(t.offset,t.offset+i),a=s.slice(0,t.length);for(let o of r){let l=o.insertOffset-t.offset,c=t.length+o.offset,u=s.slice(c,c+o.length);a=Ss(a.slice(0,l),u,a.slice(l));let d=this.#t.get(o.index);if(d)for(let h of d){let m=this.mobi.decode(u).slice(h),w=_s(m);this.#f(o.index,h,w)}}return this.mobi.decode(a)}async createDocument(e){let t=await this.loadText(e);return this.parser.parseFromString(t,this.#u)}async loadSection(e){if(this.#e.has(e))return this.#e.get(e);let t=await this.loadText(e);this.#c&&this.parser.parseFromString(t,this.#u).querySelector("parsererror")&&(this.#u=xt.HTML),this.#c&&(this.#c=!1);let r=await this.replaceResources(t),i=URL.createObjectURL(new Blob([r],{type:this.#u}));return this.#e.set(e,i),i}getIndexByFID(e){return this.#i.findIndex(t=>t.frags.some(r=>r.index===e))}#f(e,t,r){let i=this.#n.get(e);if(i)i.set(t,r);else{let s=new Map;this.#n.set(e,s),s.set(t,r)}}async resolveHref(e){let{fid:t,off:r}=xs(e),i=this.getIndexByFID(t);if(i<0)return;let s=this.#n.get(t)?.get(r);if(s)return{index:i,anchor:w=>w.querySelector(s)};let{skel:a,frags:o}=this.#i[i],l=o.find(w=>w.index===t),c=a.offset+a.length+l.offset,u=await this.loadRaw(c,c+l.length),d=this.mobi.decode(u).slice(r),h=_s(d);return this.#f(t,r,h),{index:i,anchor:w=>w.querySelector(h)}}splitTOCHref(e){let t=xs(e);return[this.getIndexByFID(t.fid),t]}getTOCFragment(e,{fid:t,off:r}){let i=this.#n.get(t)?.get(r);return e.querySelector(i)}isExternal(e){return/^(?!blob|kindle)\w+:/i.test(e)}};var we={CONTAINER:"urn:oasis:names:tc:opendocument:xmlns:container",XHTML:"http://www.w3.org/1999/xhtml",OPF:"http://www.idpf.org/2007/opf",EPUB:"http://www.idpf.org/2007/ops",DC:"http://purl.org/dc/elements/1.1/",DCTERMS:"http://purl.org/dc/terms/",ENC:"http://www.w3.org/2001/04/xmlenc#",NCX:"http://www.daisy.org/z3986/2005/ncx/",XLINK:"http://www.w3.org/1999/xlink",SMIL:"http://www.w3.org/ns/SMIL"},Ee={XML:"application/xml",NCX:"application/x-dtbncx+xml",XHTML:"application/xhtml+xml",HTML:"text/html",CSS:"text/css",SVG:"image/svg+xml",JS:/\/(x-)?(javascript|ecmascript)/},Rn=n=>n.toLowerCase().replace(/[-:](.)/g,(e,t)=>t.toUpperCase()),kc=n=>n?n.trim().replace(/\s{2,}/g," "):"",jt=(n,e,t)=>t?r=>r.getAttribute(n)?.split(/\s/)?.includes(e):typeof e=="function"?r=>e(r.getAttribute(n)):r=>r.getAttribute(n)===e,In=(...n)=>e=>e?Object.fromEntries(n.map(t=>[Rn(t),e.getAttribute(t)])):null,dt=n=>kc(n?.textContent),qt=(n,e)=>{let t=n.lookupNamespaceURI(null)===e||n.lookupPrefix(e),r=t?(i,s)=>a=>a.namespaceURI===e&&a.localName===s:(i,s)=>a=>a.localName===s;return{$:(i,s)=>[...i.children].find(r(i,s)),$$:(i,s)=>[...i.children].filter(r(i,s)),$$$:t?(i,s)=>[...i.getElementsByTagNameNS(e,s)]:(i,s)=>[...i.getElementsByTagName(e,s)]}},kt=(n,e)=>{try{if(e.includes(":"))return new URL(n,e);let t="file:///";return decodeURI(new URL(n,t+e).href.replace(t,""))}catch{return n}},Rs=n=>/^(?!blob)\w+:/i.test(n),Tc=(n,e)=>{if(!n)return e;let t=n.replace(/\/$/,"").split("/"),r=e.replace(/\/$/,"").split("/"),i=(t.length>r.length?t:r).findIndex((s,a)=>t[a]!==r[a]);return i<0?"":Array(t.length-i).fill("..").concat(r.slice(i)).join("/")},Cc=n=>n.slice(0,n.lastIndexOf("/")+1),Cn=async(n,e,t)=>{let r=[];n.replace(e,(...s)=>(r.push(s),null));let i=[];for(let s of r)i.push(await t(...s));return n.replace(e,()=>i.shift())},Rc=n=>n.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),ut={attrs:["dir","xml:lang"]},vt={name:"alternate-script",many:!0,...ut,props:["file-as"]},Ts={many:!0,...ut,props:[{name:"role",many:!0,attrs:["scheme"]},"file-as",vt]},Ic=[{name:"title",many:!0,...ut,props:["title-type","display-seq","file-as",vt]},{name:"identifier",many:!0,props:[{name:"identifier-type",attrs:["scheme"]}]},{name:"language",many:!0},{name:"creator",...Ts},{name:"contributor",...Ts},{name:"publisher",...ut,props:["file-as",vt]},{name:"description",...ut,props:[vt]},{name:"rights",...ut,props:[vt]},{name:"date"},{name:"dcterms:modified",type:"meta"},{name:"subject",many:!0,...ut,props:["term","authority",vt]},{name:"belongs-to-collection",type:"meta",many:!0,...ut,props:["collection-type","group-position","dcterms:identifier","file-as",vt,{name:"belongs-to-collection",recursive:!0}]}],zc=n=>{let{$:e,$$:t}=qt(n,we.OPF),r=e(n.documentElement,"metadata"),i=Array.from(r.children),s=(d,h)=>{if(!h)return null;let{props:m=[],attrs:w=[]}=d,f=dt(h);if(!m.length&&!w.length)return f;let p=h.getAttribute("id"),g=p?i.filter(jt("refines","#"+p)):[];return Object.fromEntries([["value",f]].concat(m.map(b=>{let{many:y,recursive:E}=b,_=typeof b=="string"?b:b.name,x=jt("property",_),S=E?d:b;return[Rn(_),y?g.filter(x).map(v=>s(S,v)):s(S,g.find(x))]})).concat(w.map(b=>[Rn(b),h.getAttribute(b)])))},a=i.filter(jt("refines",null)),o=Object.fromEntries(Ic.map(d=>{let{type:h,name:m,many:w}=d,f=h==="meta"?p=>p.namespaceURI===we.OPF&&p.getAttribute("property")===m:p=>p.namespaceURI===we.DC&&p.localName===m;return[Rn(m),w?a.filter(f).map(p=>s(d,p)):s(d,a.find(f))]})),l=d=>Object.fromEntries(t(r,"meta").filter(jt("property",h=>h?.startsWith(d))).map(h=>[h.getAttribute("property").replace(d,""),dt(h)])),c=l("rendition:"),u=l("media:");return{metadata:o,rendition:c,media:u}},Dc=(n,e=t=>t)=>{let{$:t,$$:r,$$$:i}=qt(n,we.XHTML),s=w=>w?decodeURI(e(w)):null,a=w=>f=>{let p=t(f,"a")??t(f,"span"),g=t(f,"ol"),b=s(p?.getAttribute("href")),E={label:dt(p)||p?.getAttribute("title"),href:b,subitems:o(g)};return w&&(E.type=p?.getAttributeNS(we.EPUB,"type")?.split(/\s/)),E},o=(w,f)=>w?r(w,"li").map(a(f)):null,l=(w,f)=>o(t(w,"ol"),f),c=i(n,"nav"),u=null,d=null,h=null,m=[];for(let w of c){let f=w.getAttributeNS(we.EPUB,"type")?.split(/\s/)??[];f.includes("toc")?u??=l(w):f.includes("page-list")?d??=l(w):f.includes("landmarks")?h??=l(w,!0):m.push({label:dt(w.firstElementChild),type:f,list:l(w)})}return{toc:u,pageList:d,landmarks:h,others:m}},Lc=(n,e=t=>t)=>{let{$:t,$$:r}=qt(n,we.NCX),i=l=>l?decodeURI(e(l)):null,s=l=>{let c=t(l,"navLabel"),u=t(l,"content"),d=dt(c),h=i(u.getAttribute("src"));if(l.localName==="navPoint"){let m=r(l,"navPoint");return{label:d,href:h,subitems:m.length?m.map(s):null}}return{label:d,href:h}},a=(l,c)=>r(l,c).map(s),o=(l,c)=>{let u=t(n.documentElement,l);return u?a(u,c):null};return{toc:o("navMap","navPoint"),pageList:o("pageList","pageTarget"),others:r(n.documentElement,"navList").map(l=>({label:dt(t(l,"navLabel")),list:a(l,"navTarget")}))}},Ir=n=>{if(!n)return;let e=n.split(":").map(a=>parseFloat(a));if(e.length===3){let[a,o,l]=e;return a*60*60+o*60+l}if(e.length===2){let[a,o]=e;return a*60+o}let[t,r]=n.split(/(?=[^\d.])/),i=parseFloat(t),s=r==="h"?60*60:r==="min"?60:r==="ms"?.001:1;return i*s},Fc=(n,e=t=>t)=>{let{$:t,$$$:r}=qt(n,we.SMIL),i=s=>s?decodeURI(e(s)):null;return r(n,"par").map(s=>{let a=t(s,"text")?.getAttribute("src")?.split("#")?.[1],o=t(s,"audio");return o?{id:a,audio:{src:i(o.getAttribute("src")),clipBegin:Ir(o.getAttribute("clipBegin")),clipEnd:Ir(o.getAttribute("clipEnd"))}}:{id:a}})},Oc=/([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})/,Mc=n=>{for(let e of n.getElementsByTagNameNS(we.DC,"identifier")){let[t]=dt(e).split(":").slice(-1);if(Oc.test(t))return t}return""},Is=n=>dt(n.getElementById(n.documentElement.getAttribute("unique-identifier"))??n.getElementsByTagNameNS(we.DC,"identifier")[0]),Cs=async(n,e,t)=>{let r=new Uint8Array(await t.slice(0,e).arrayBuffer());e=Math.min(e,r.length);for(var i=0;i<e;i++)r[i]=r[i]^n[i%n.length];return new Blob([r,t.slice(e)],{type:t.type})},Uc=async n=>{let e=new TextEncoder().encode(n),t=await globalThis.crypto.subtle.digest("SHA-1",e);return new Uint8Array(t)},Bc=(n=Uc)=>({"http://www.idpf.org/2008/embedding":{key:e=>n(Is(e).replaceAll(/[\u0020\u0009\u000d\u000a]/g,"")),decode:(e,t)=>Cs(e,1040,t)},"http://ns.adobe.com/pdf/enc#RC":{key:e=>{let t=Mc(e).replaceAll("-","");return Uint8Array.from({length:16},(r,i)=>parseInt(t.slice(i*2,i*2+2),16))},decode:(e,t)=>Cs(e,1024,t)}}),zr=class{#e=new Map;#t=new Map;#n;constructor(e){this.#n=e}async init(e,t){if(!e)return;let r=Array.from(e.getElementsByTagNameNS(we.ENC,"EncryptedData"),i=>({algorithm:i.getElementsByTagNameNS(we.ENC,"EncryptionMethod")[0]?.getAttribute("Algorithm"),uri:i.getElementsByTagNameNS(we.ENC,"CipherReference")[0]?.getAttribute("URI")}));for(let{algorithm:i,uri:s}of r){if(!this.#t.has(i)){let a=this.#n[i];if(!a)continue;let o=await a.key(t);this.#t.set(i,l=>a.decode(o,l))}this.#e.set(s,i)}}getDecoder(e){return this.#t.get(this.#e.get(e))??(t=>t)}},Dr=class{constructor({opf:e,resolveHref:t}){this.opf=e;let{$:r,$$:i,$$$:s}=qt(e,we.OPF),a=r(e.documentElement,"manifest"),o=r(e.documentElement,"spine"),l=i(o,"itemref");this.manifest=i(a,"item").map(In("href","id","media-type","properties","media-overlay")).map(u=>(u.href=t(u.href),u.properties=u.properties?.split(/\s/),u)),this.spine=l.map(In("idref","id","linear","properties")).map(u=>(u.properties=u.properties?.split(/\s/),u)),this.pageProgressionDirection=o.getAttribute("page-progression-direction"),this.navPath=this.getItemByProperty("nav")?.href,this.ncxPath=(this.getItemByID(o.getAttribute("toc"))??this.manifest.find(u=>u.mediaType===Ee.NCX))?.href;let c=r(e.documentElement,"guide");c&&(this.guide=i(c,"reference").map(In("type","title","href")).map(({type:u,title:d,href:h})=>({label:d,type:u.split(/\s/),href:t(h)}))),this.cover=this.getItemByProperty("cover-image")??this.getItemByID(s(e,"meta").find(jt("name","cover"))?.getAttribute("content"))??this.getItemByHref(this.guide?.find(u=>u.type.includes("cover"))?.href),this.cfis=Zi(l)}getItemByID(e){return this.manifest.find(t=>t.id===e)}getItemByHref(e){return this.manifest.find(t=>t.href===e)}getItemByProperty(e){return this.manifest.find(t=>t.properties?.includes(e))}resolveCFI(e){let t=nt(e),r=(t.parent??t).shift(),i=br(this.opf,r);i&&i.nodeName!=="idref"&&(r.at(-1).id=null,i=br(this.opf,r));let s=i?.getAttribute("idref");return{index:this.spine.findIndex(l=>l.idref===s),anchor:l=>hn(l,t)}}},Lr=class{#e=new Map;#t=new Map;#n=new Map;allowScript=!1;constructor({loadText:e,loadBlob:t,resources:r}){this.loadText=e,this.loadBlob=t,this.manifest=r.manifest,this.assets=r.manifest}createURL(e,t,r,i){if(!t)return"";let s=URL.createObjectURL(new Blob([t],{type:r}));if(this.#e.set(e,s),this.#n.set(e,1),i){let a=this.#t.get(i);a?a.push(e):this.#t.set(i,[e])}return s}ref(e,t){let r=this.#t.get(t);return r?.includes(e)||(this.#n.set(e,this.#n.get(e)+1),r?r.push(e):this.#t.set(t,[e])),this.#e.get(e)}unref(e){if(!this.#n.has(e))return;let t=this.#n.get(e)-1;if(t<1){URL.revokeObjectURL(this.#e.get(e)),this.#e.delete(e),this.#n.delete(e);let r=this.#t.get(e);if(r)for(;r.length;)this.unref(r.pop());this.#t.delete(e)}else this.#n.set(e,t)}async loadItem(e,t=[]){if(!e)return null;let{href:r,mediaType:i}=e,s=Ee.JS.test(e.mediaType);if(s&&!this.allowScript)return null;let a=t.at(-1);return this.#e.has(r)?this.ref(r,a):(s||[Ee.XHTML,Ee.HTML,Ee.CSS,Ee.SVG].includes(i))&&t.every(l=>l!==r)?this.loadReplaced(e,t):this.createURL(r,await this.loadBlob(r),i,a)}async loadHref(e,t,r=[]){if(Rs(e))return e;let i=kt(e,t),s=this.manifest.find(a=>a.href===i);return s?this.loadItem(s,r.concat(t)):e}async loadReplaced(e,t=[]){let{href:r,mediaType:i}=e,s=t.at(-1),a=await this.loadText(r);if(!a)return null;if([Ee.XHTML,Ee.HTML,Ee.SVG].includes(i)){let l=new DOMParser().parseFromString(a,i);if(i===Ee.XHTML&&l.querySelector("parsererror")&&(e.mediaType=Ee.HTML,l=new DOMParser().parseFromString(a,e.mediaType)),[Ee.XHTML,Ee.SVG].includes(e.mediaType)){let d=l.firstChild;for(;d instanceof ProcessingInstruction;){if(d.data){let h=await Cn(d.data,/(?:^|\s*)(href\s*=\s*['"])([^'"]*)(['"])/i,(m,w,f,p)=>this.loadHref(f,r,t).then(g=>`${w}${g}${p}`));d.replaceWith(l.createProcessingInstruction(d.target,h))}d=d.nextSibling}}let c=async(d,h)=>d.setAttribute(h,await this.loadHref(d.getAttribute(h),r,t));for(let d of l.querySelectorAll("link[href]"))await c(d,"href");for(let d of l.querySelectorAll("[src]"))await c(d,"src");for(let d of l.querySelectorAll("[poster]"))await c(d,"poster");for(let d of l.querySelectorAll("object[data]"))await c(d,"data");for(let d of l.querySelectorAll("[*|href]:not([href]"))d.setAttributeNS(we.XLINK,"href",await this.loadHref(d.getAttributeNS(we.XLINK,"href"),r,t));for(let d of l.querySelectorAll("style"))d.textContent&&(d.textContent=await this.replaceCSS(d.textContent,r,t));for(let d of l.querySelectorAll("[style]"))d.setAttribute("style",await this.replaceCSS(d.getAttribute("style"),r,t));let u=new XMLSerializer().serializeToString(l);return this.createURL(r,u,e.mediaType,s)}let o=i===Ee.CSS?await this.replaceCSS(a,r,t):await this.replaceString(a,r,t);return this.createURL(r,o,i,s)}async replaceCSS(e,t,r=[]){let i=await Cn(e,/url\(\s*["']?([^'"\n]*?)\s*["']?\s*\)/gi,(l,c)=>this.loadHref(c,t,r).then(u=>`url("${u}")`)),s=await Cn(i,/@import\s*["']([^"'\n]*?)["']/gi,(l,c)=>this.loadHref(c,t,r).then(u=>`@import "${u}"`)),a=window?.innerWidth??800,o=window?.innerHeight??600;return s.replace(/-epub-/gi,"").replace(/(\d*\.?\d+)vw/gi,(l,c)=>parseFloat(c)*a/100+"px").replace(/(\d*\.?\d+)vh/gi,(l,c)=>parseFloat(c)*o/100+"px").replace(/page-break-(after|before|inside)/gi,(l,c)=>`-webkit-column-break-${c}`)}replaceString(e,t,r=[]){let i=new Map,s=this.assets.map(o=>{if(o.href===t)return;let l=Tc(Cc(t),o.href),c=encodeURI(l),u="/"+o.href,d=encodeURI(u),h=new Set([l,c,u,d]);for(let m of h)i.set(m,o);return Array.from(h)}).flat().filter(o=>o);if(!s.length)return e;let a=new RegExp(s.map(Rc).join("|"),"g");return Cn(e,a,async o=>this.loadItem(i.get(o.replace(/^\//,"")),r.concat(t)))}unloadItem(e){this.unref(e?.href)}},Nc=(n,e)=>n.getElementById(e)??n.querySelector(`[name="${CSS.escape(e)}"]`),Pc=n=>{for(let e of n){if(e==="page-spread-left"||e==="rendition:page-spread-left")return"left";if(e==="page-spread-right"||e==="rendition:page-spread-right")return"right";if(e==="rendition:page-spread-center")return"center"}},Vt=class{parser=new DOMParser;#e;constructor({loadText:e,loadBlob:t,getSize:r,sha1:i}){this.loadText=e,this.loadBlob=t,this.getSize=r,this.#e=new zr(Bc(i))}#t(e){return e?this.parser.parseFromString(e,Ee.XML):null}async#n(e){return this.#t(await this.loadText(e))}opfPath=null;async init(){let e=await this.#n("META-INF/container.xml");if(!e)throw new Error("Failed to load container file");let t=Array.from(e.getElementsByTagNameNS(we.CONTAINER,"rootfile"),In("full-path","media-type")).filter(f=>f.mediaType==="application/oebps-package+xml");if(!t.length)throw new Error("No package document defined in container");let r=t[0].fullPath;this.opfPath=r;let i=await this.#n(r);if(!i)throw new Error("Failed to load package document");let s=await this.#n("META-INF/encryption.xml");await this.#e.init(s,i),this.resources=new Dr({opf:i,resolveHref:f=>kt(f,r)});let a=new Lr({loadText:this.loadText,loadBlob:f=>Promise.resolve(this.loadBlob(f)).then(this.#e.getDecoder(f)),resources:this.resources});this.sections=this.resources.spine.map((f,p)=>{let{idref:g,linear:b,properties:y=[]}=f,E=this.resources.getItemByID(g);return E?{id:this.resources.getItemByID(g)?.href,load:()=>a.loadItem(E),unload:()=>a.unloadItem(E),createDocument:()=>this.loadDocument(E),size:this.getSize(E.href),cfi:this.resources.cfis[p],linear:b,pageSpread:Pc(y),resolveHref:_=>kt(_,E.href),loadMediaOverlay:()=>this.loadMediaOverlay(E)}:null}).filter(f=>f);let{navPath:o,ncxPath:l}=this.resources;if(o)try{let f=g=>kt(g,o),p=Dc(await this.#n(o),f);this.toc=p.toc,this.pageList=p.pageList,this.landmarks=p.landmarks}catch{}if(!this.toc&&l)try{let f=g=>kt(g,l),p=Lc(await this.#n(l),f);this.toc=p.toc,this.pageList=p.pageList}catch{}this.landmarks??=this.resources.guide;let{metadata:c,rendition:u,media:d}=zc(i);this.rendition=u,this.media=d,d.duration=Ir(d.duration),this.dir=this.resources.pageProgressionDirection,this.rawMetadata=c;let h=c?.title?.[0];this.metadata={title:h?.value,sortAs:h?.fileAs,language:c?.language,identifier:Is(i),description:c?.description?.value,publisher:c?.publisher?.value,published:c?.date,modified:c?.dctermsModified,subject:c?.subject?.filter(({value:f,code:p})=>f||p)?.map(({value:f,code:p,scheme:g})=>({name:f,code:p,scheme:g})),rights:c?.rights?.value};let m={art:"artist",aut:"author",bkp:"producer",clr:"colorist",edt:"editor",ill:"illustrator",trl:"translator",pbl:"publisher"},w=f=>p=>{let g=[...new Set(p.role?.map(({value:y,scheme:E})=>(!E||E==="marc:relators"?m[y]:null)??f))],b={name:p.value,sortAs:p.fileAs};return[g?.length?g:[f],b]};return c?.creator?.map(w("author"))?.concat(c?.contributor?.map?.(w("contributor")))?.forEach(([f,p])=>f.forEach(g=>{this.metadata[g]?this.metadata[g].push(p):this.metadata[g]=[p]})),this}async loadDocument(e){let t=await this.loadText(e.href);return this.parser.parseFromString(t,e.mediaType)}async loadMediaOverlay(e){let t=e.mediaOverlay;if(!t)return null;let r=this.resources.getItemByID(t),i=await this.#n(r.href);return Fc(i,a=>kt(a,r.href))}resolveCFI(e){return this.resources.resolveCFI(e)}resolveHref(e){let[t,r]=e.split("#"),i=this.resources.getItemByHref(decodeURI(t));return i?{index:this.resources.spine.findIndex(({idref:o})=>o===i.id),anchor:r?o=>Nc(o,r):()=>0}:null}splitTOCHref(e){return e?.split("#")??[]}getTOCFragment(e,t){return e.getElementById(t)??e.querySelector(`[name="${CSS.escape(t)}"]`)}isExternal(e){return Rs(e)}async getCover(){let e=this.resources?.cover;return e?.href?new Blob([await this.loadBlob(e.href)],{type:e.mediaType}):null}async getCalibreBookmarks(){let e=await this.loadText("META-INF/calibre_bookmarks.txt"),t="encoding=json+base64:";if(e?.startsWith(t)){let r=atob(e.slice(t.length));return JSON.parse(r)}}};var vi={};xl(vi,{BlobReader:()=>hi,BlobWriter:()=>fi,Data64URIReader:()=>hd,Data64URIWriter:()=>fd,ERR_BAD_FORMAT:()=>$n,ERR_CENTRAL_DIRECTORY_NOT_FOUND:()=>yo,ERR_DUPLICATED_NAME:()=>ko,ERR_ENCRYPTED:()=>_o,ERR_EOCDR_LOCATOR_ZIP64_NOT_FOUND:()=>bo,ERR_EOCDR_NOT_FOUND:()=>mo,ERR_EOCDR_ZIP64_NOT_FOUND:()=>wo,ERR_EXTRAFIELD_ZIP64_NOT_FOUND:()=>vo,ERR_HTTP_RANGE:()=>nn,ERR_INVALID_COMMENT:()=>To,ERR_INVALID_ENCRYPTION_STRENGTH:()=>Io,ERR_INVALID_ENTRY_COMMENT:()=>Co,ERR_INVALID_ENTRY_NAME:()=>Ro,ERR_INVALID_EXTRAFIELD_DATA:()=>Do,ERR_INVALID_EXTRAFIELD_TYPE:()=>zo,ERR_INVALID_PASSWORD:()=>si,ERR_INVALID_SIGNATURE:()=>ai,ERR_INVALID_VERSION:()=>ri,ERR_ITERATOR_COMPLETED_TOO_SOON:()=>Ga,ERR_LOCAL_FILE_HEADER_NOT_FOUND:()=>xo,ERR_SPLIT_ZIP_FILE:()=>ni,ERR_UNSUPPORTED_COMPRESSION:()=>ti,ERR_UNSUPPORTED_ENCRYPTION:()=>So,ERR_UNSUPPORTED_FORMAT:()=>er,HttpRangeReader:()=>bd,HttpReader:()=>to,Reader:()=>bt,SplitDataReader:()=>gi,SplitDataWriter:()=>en,SplitZipReader:()=>_d,SplitZipWriter:()=>Sd,TextReader:()=>pd,TextWriter:()=>gd,Uint8ArrayReader:()=>yd,Uint8ArrayWriter:()=>xd,Writer:()=>di,ZipReader:()=>zd,ZipWriter:()=>Pd,configure:()=>Yn,getMimeType:()=>yu,initReader:()=>mi,initStream:()=>rt,initWriter:()=>wi,readUint8Array:()=>xe,terminateWorkers:()=>rd});function Zn(n){return Kn(n.map(([e,t])=>new Array(e).fill(t,0,e)))}function Kn(n){return n.reduce((e,t)=>e.concat(Array.isArray(t)?Kn(t):t),[])}var zs=[0,1,2,3].concat(...Zn([[2,4],[2,5],[4,6],[4,7],[8,8],[8,9],[16,10],[16,11],[32,12],[32,13],[64,14],[64,15],[2,0],[1,16],[1,17],[2,18],[2,19],[4,20],[4,21],[8,22],[8,23],[16,24],[16,25],[32,26],[32,27],[64,28],[64,29]]));function se(){let n=this;function e(i){let s=n.dyn_tree,a=n.stat_desc.static_tree,o=n.stat_desc.extra_bits,l=n.stat_desc.extra_base,c=n.stat_desc.max_length,u,d,h,m,w,f,p=0;for(m=0;m<=15;m++)i.bl_count[m]=0;for(s[i.heap[i.heap_max]*2+1]=0,u=i.heap_max+1;u<573;u++)d=i.heap[u],m=s[s[d*2+1]*2+1]+1,m>c&&(m=c,p++),s[d*2+1]=m,!(d>n.max_code)&&(i.bl_count[m]++,w=0,d>=l&&(w=o[d-l]),f=s[d*2],i.opt_len+=f*(m+w),a&&(i.static_len+=f*(a[d*2+1]+w)));if(p!==0){do{for(m=c-1;i.bl_count[m]===0;)m--;i.bl_count[m]--,i.bl_count[m+1]+=2,i.bl_count[c]--,p-=2}while(p>0);for(m=c;m!==0;m--)for(d=i.bl_count[m];d!==0;)h=i.heap[--u],!(h>n.max_code)&&(s[h*2+1]!=m&&(i.opt_len+=(m-s[h*2+1])*s[h*2],s[h*2+1]=m),d--)}}function t(i,s){let a=0;do a|=i&1,i>>>=1,a<<=1;while(--s>0);return a>>>1}function r(i,s,a){let o=[],l=0,c,u,d;for(c=1;c<=15;c++)o[c]=l=l+a[c-1]<<1;for(u=0;u<=s;u++)d=i[u*2+1],d!==0&&(i[u*2]=t(o[d]++,d))}n.build_tree=function(i){let s=n.dyn_tree,a=n.stat_desc.static_tree,o=n.stat_desc.elems,l,c,u=-1,d;for(i.heap_len=0,i.heap_max=573,l=0;l<o;l++)s[l*2]!==0?(i.heap[++i.heap_len]=u=l,i.depth[l]=0):s[l*2+1]=0;for(;i.heap_len<2;)d=i.heap[++i.heap_len]=u<2?++u:0,s[d*2]=1,i.depth[d]=0,i.opt_len--,a&&(i.static_len-=a[d*2+1]);for(n.max_code=u,l=Math.floor(i.heap_len/2);l>=1;l--)i.pqdownheap(s,l);d=o;do l=i.heap[1],i.heap[1]=i.heap[i.heap_len--],i.pqdownheap(s,1),c=i.heap[1],i.heap[--i.heap_max]=l,i.heap[--i.heap_max]=c,s[d*2]=s[l*2]+s[c*2],i.depth[d]=Math.max(i.depth[l],i.depth[c])+1,s[l*2+1]=s[c*2+1]=d,i.heap[1]=d++,i.pqdownheap(s,1);while(i.heap_len>=2);i.heap[--i.heap_max]=i.heap[1],e(i),r(s,n.max_code,i.bl_count)}}se._length_code=[0,1,2,3,4,5,6,7].concat(...Zn([[2,8],[2,9],[2,10],[2,11],[4,12],[4,13],[4,14],[4,15],[8,16],[8,17],[8,18],[8,19],[16,20],[16,21],[16,22],[16,23],[32,24],[32,25],[32,26],[31,27],[1,28]]));se.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0];se.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576];se.d_code=function(n){return n<256?zs[n]:zs[256+(n>>>7)]};se.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0];se.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];se.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7];se.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];function be(n,e,t,r,i){let s=this;s.static_tree=n,s.extra_bits=e,s.extra_base=t,s.elems=r,s.max_length=i}var Hc=[12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254,1,129,65,193,33,161,97,225,17,145,81,209,49,177,113,241,9,137,73,201,41,169,105,233,25,153,89,217,57,185,121,249,5,133,69,197,37,165,101,229,21,149,85,213,53,181,117,245,13,141,77,205,45,173,109,237,29,157,93,221,61,189,125,253,19,275,147,403,83,339,211,467,51,307,179,435,115,371,243,499,11,267,139,395,75,331,203,459,43,299,171,427,107,363,235,491,27,283,155,411,91,347,219,475,59,315,187,443,123,379,251,507,7,263,135,391,71,327,199,455,39,295,167,423,103,359,231,487,23,279,151,407,87,343,215,471,55,311,183,439,119,375,247,503,15,271,143,399,79,335,207,463,47,303,175,431,111,367,239,495,31,287,159,415,95,351,223,479,63,319,191,447,127,383,255,511,0,64,32,96,16,80,48,112,8,72,40,104,24,88,56,120,4,68,36,100,20,84,52,116,3,131,67,195,35,163,99,227],$c=Zn([[144,8],[112,9],[24,7],[8,8]]);be.static_ltree=Kn(Hc.map((n,e)=>[n,$c[e]]));var Wc=[0,16,8,24,4,20,12,28,2,18,10,26,6,22,14,30,1,17,9,25,5,21,13,29,3,19,11,27,7,23],jc=Zn([[30,5]]);be.static_dtree=Kn(Wc.map((n,e)=>[n,jc[e]]));be.static_l_desc=new be(be.static_ltree,se.extra_lbits,256+1,286,15);be.static_d_desc=new be(be.static_dtree,se.extra_dbits,0,30,15);be.static_bl_desc=new be(null,se.extra_blbits,0,19,7);var Vc=9,qc=8;function Ze(n,e,t,r,i){let s=this;s.good_length=n,s.max_lazy=e,s.nice_length=t,s.max_chain=r,s.func=i}var ya=0,Nn=1,St=2,Ve=[new Ze(0,0,0,0,ya),new Ze(4,4,8,4,Nn),new Ze(4,5,16,8,Nn),new Ze(4,6,32,32,Nn),new Ze(4,4,16,16,St),new Ze(8,16,32,32,St),new Ze(8,16,128,128,St),new Ze(8,32,128,256,St),new Ze(32,128,258,1024,St),new Ze(32,258,258,4096,St)],zn=["need dictionary","stream end","","","stream error","data error","","buffer error","",""],qe=0,Dn=1,Xt=2,Ln=3,Xc=32,Fr=42,Fn=113,Gt=666,Or=8,Gc=0,Mr=1,Zc=2,ae=3,Pn=258,De=Pn+ae+1;function Ds(n,e,t,r){let i=n[e*2],s=n[t*2];return i<s||i==s&&r[e]<=r[t]}function Kc(){let n=this,e,t,r,i,s,a,o,l,c,u,d,h,m,w,f,p,g,b,y,E,_,x,S,v,k,A,T,D,R,U,z,O,M,P=new se,Z=new se,B=new se;n.depth=[];let q,H,W,K,V,Y;n.bl_count=[],n.heap=[],z=[],O=[],M=[];function he(){c=2*s,d[m-1]=0;for(let C=0;C<m-1;C++)d[C]=0;A=Ve[T].max_lazy,R=Ve[T].good_length,U=Ve[T].nice_length,k=Ve[T].max_chain,_=0,g=0,S=0,b=v=ae-1,E=0,h=0}function fe(){let C;for(C=0;C<286;C++)z[C*2]=0;for(C=0;C<30;C++)O[C*2]=0;for(C=0;C<19;C++)M[C*2]=0;z[256*2]=1,n.opt_len=n.static_len=0,H=W=0}function Te(){P.dyn_tree=z,P.stat_desc=be.static_l_desc,Z.dyn_tree=O,Z.stat_desc=be.static_d_desc,B.dyn_tree=M,B.stat_desc=be.static_bl_desc,V=0,Y=0,K=8,fe()}n.pqdownheap=function(C,L){let I=n.heap,F=I[L],N=L<<1;for(;N<=n.heap_len&&(N<n.heap_len&&Ds(C,I[N+1],I[N],n.depth)&&N++,!Ds(C,F,I[N],n.depth));)I[L]=I[N],L=N,N<<=1;I[L]=F};function oe(C,L){let I=-1,F,N=C[0*2+1],$=0,G=7,Se=4;N===0&&(G=138,Se=3),C[(L+1)*2+1]=65535;for(let We=0;We<=L;We++)F=N,N=C[(We+1)*2+1],!(++$<G&&F==N)&&($<Se?M[F*2]+=$:F!==0?(F!=I&&M[F*2]++,M[16*2]++):$<=10?M[17*2]++:M[18*2]++,$=0,I=F,N===0?(G=138,Se=3):F==N?(G=6,Se=3):(G=7,Se=4))}function ze(){let C;for(oe(z,P.max_code),oe(O,Z.max_code),B.build_tree(n),C=19-1;C>=3&&M[se.bl_order[C]*2+1]===0;C--);return n.opt_len+=3*(C+1)+5+5+4,C}function _e(C){n.pending_buf[n.pending++]=C}function Oe(C){_e(C&255),_e(C>>>8&255)}function Ce(C){_e(C>>8&255),_e(C&255&255)}function ce(C,L){let I,F=L;Y>16-F?(I=C,V|=I<<Y&65535,Oe(V),V=I>>>16-Y,Y+=F-16):(V|=C<<Y&65535,Y+=F)}function me(C,L){let I=C*2;ce(L[I]&65535,L[I+1]&65535)}function yt(C,L){let I,F=-1,N,$=C[0*2+1],G=0,Se=7,We=4;for($===0&&(Se=138,We=3),I=0;I<=L;I++)if(N=$,$=C[(I+1)*2+1],!(++G<Se&&N==$)){if(G<We)do me(N,M);while(--G!==0);else N!==0?(N!=F&&(me(N,M),G--),me(16,M),ce(G-3,2)):G<=10?(me(17,M),ce(G-3,3)):(me(18,M),ce(G-11,7));G=0,F=N,$===0?(Se=138,We=3):N==$?(Se=6,We=3):(Se=7,We=4)}}function $e(C,L,I){let F;for(ce(C-257,5),ce(L-1,5),ce(I-4,4),F=0;F<I;F++)ce(M[se.bl_order[F]*2+1],3);yt(z,C-1),yt(O,L-1)}function ln(){Y==16?(Oe(V),V=0,Y=0):Y>=8&&(_e(V&255),V>>>=8,Y-=8)}function dr(){ce(Mr<<1,3),me(256,be.static_ltree),ln(),1+K+10-Y<9&&(ce(Mr<<1,3),me(256,be.static_ltree),ln()),K=7}function tt(C,L){let I,F,N;if(n.dist_buf[H]=C,n.lc_buf[H]=L&255,H++,C===0?z[L*2]++:(W++,C--,z[(se._length_code[L]+256+1)*2]++,O[se.d_code(C)*2]++),!(H&8191)&&T>2){for(I=H*8,F=_-g,N=0;N<30;N++)I+=O[N*2]*(5+se.extra_dbits[N]);if(I>>>=3,W<Math.floor(H/2)&&I<Math.floor(F/2))return!0}return H==q-1}function Mi(C,L){let I,F,N=0,$,G;if(H!==0)do I=n.dist_buf[N],F=n.lc_buf[N],N++,I===0?me(F,C):($=se._length_code[F],me($+256+1,C),G=se.extra_lbits[$],G!==0&&(F-=se.base_length[$],ce(F,G)),I--,$=se.d_code(I),me($,L),G=se.extra_dbits[$],G!==0&&(I-=se.base_dist[$],ce(I,G)));while(N<H);me(256,C),K=C[256*2+1]}function Ui(){Y>8?Oe(V):Y>0&&_e(V&255),V=0,Y=0}function fl(C,L,I){Ui(),K=8,I&&(Oe(L),Oe(~L)),n.pending_buf.set(l.subarray(C,C+L),n.pending),n.pending+=L}function Bi(C,L,I){ce((Gc<<1)+(I?1:0),3),fl(C,L,!0)}function pl(C,L,I){let F,N,$=0;T>0?(P.build_tree(n),Z.build_tree(n),$=ze(),F=n.opt_len+3+7>>>3,N=n.static_len+3+7>>>3,N<=F&&(F=N)):F=N=L+5,L+4<=F&&C!=-1?Bi(C,L,I):N==F?(ce((Mr<<1)+(I?1:0),3),Mi(be.static_ltree,be.static_dtree)):(ce((Zc<<1)+(I?1:0),3),$e(P.max_code+1,Z.max_code+1,$+1),Mi(z,O)),fe(),I&&Ui()}function lt(C){pl(g>=0?g:-1,_-g,C),g=_,e.flush_pending()}function hr(){let C,L,I,F;do{if(F=c-S-_,F===0&&_===0&&S===0)F=s;else if(F==-1)F--;else if(_>=s+s-De){l.set(l.subarray(s,s+s),0),x-=s,_-=s,g-=s,C=m,I=C;do L=d[--I]&65535,d[I]=L>=s?L-s:0;while(--C!==0);C=s,I=C;do L=u[--I]&65535,u[I]=L>=s?L-s:0;while(--C!==0);F+=s}if(e.avail_in===0)return;C=e.read_buf(l,_+S,F),S+=C,S>=ae&&(h=l[_]&255,h=(h<<p^l[_+1]&255)&f)}while(S<De&&e.avail_in!==0)}function gl(C){let L=65535,I;for(L>r-5&&(L=r-5);;){if(S<=1){if(hr(),S===0&&C==0)return qe;if(S===0)break}if(_+=S,S=0,I=g+L,(_===0||_>=I)&&(S=_-I,_=I,lt(!1),e.avail_out===0)||_-g>=s-De&&(lt(!1),e.avail_out===0))return qe}return lt(C==4),e.avail_out===0?C==4?Xt:qe:C==4?Ln:Dn}function Ni(C){let L=k,I=_,F,N,$=v,G=_>s-De?_-(s-De):0,Se=U,We=o,fr=_+Pn,Pi=l[I+$-1],Hi=l[I+$];v>=R&&(L>>=2),Se>S&&(Se=S);do if(F=C,!(l[F+$]!=Hi||l[F+$-1]!=Pi||l[F]!=l[I]||l[++F]!=l[I+1])){I+=2,F++;do;while(l[++I]==l[++F]&&l[++I]==l[++F]&&l[++I]==l[++F]&&l[++I]==l[++F]&&l[++I]==l[++F]&&l[++I]==l[++F]&&l[++I]==l[++F]&&l[++I]==l[++F]&&I<fr);if(N=Pn-(fr-I),I=fr-Pn,N>$){if(x=C,$=N,N>=Se)break;Pi=l[I+$-1],Hi=l[I+$]}}while((C=u[C&We]&65535)>G&&--L!==0);return $<=S?$:S}function ml(C){let L=0,I;for(;;){if(S<De){if(hr(),S<De&&C==0)return qe;if(S===0)break}if(S>=ae&&(h=(h<<p^l[_+(ae-1)]&255)&f,L=d[h]&65535,u[_&o]=d[h],d[h]=_),L!==0&&(_-L&65535)<=s-De&&D!=2&&(b=Ni(L)),b>=ae)if(I=tt(_-x,b-ae),S-=b,b<=A&&S>=ae){b--;do _++,h=(h<<p^l[_+(ae-1)]&255)&f,L=d[h]&65535,u[_&o]=d[h],d[h]=_;while(--b!==0);_++}else _+=b,b=0,h=l[_]&255,h=(h<<p^l[_+1]&255)&f;else I=tt(0,l[_]&255),S--,_++;if(I&&(lt(!1),e.avail_out===0))return qe}return lt(C==4),e.avail_out===0?C==4?Xt:qe:C==4?Ln:Dn}function wl(C){let L=0,I,F;for(;;){if(S<De){if(hr(),S<De&&C==0)return qe;if(S===0)break}if(S>=ae&&(h=(h<<p^l[_+(ae-1)]&255)&f,L=d[h]&65535,u[_&o]=d[h],d[h]=_),v=b,y=x,b=ae-1,L!==0&&v<A&&(_-L&65535)<=s-De&&(D!=2&&(b=Ni(L)),b<=5&&(D==1||b==ae&&_-x>4096)&&(b=ae-1)),v>=ae&&b<=v){F=_+S-ae,I=tt(_-1-y,v-ae),S-=v-1,v-=2;do++_<=F&&(h=(h<<p^l[_+(ae-1)]&255)&f,L=d[h]&65535,u[_&o]=d[h],d[h]=_);while(--v!==0);if(E=0,b=ae-1,_++,I&&(lt(!1),e.avail_out===0))return qe}else if(E!==0){if(I=tt(0,l[_-1]&255),I&&lt(!1),_++,S--,e.avail_out===0)return qe}else E=1,_++,S--}return E!==0&&(I=tt(0,l[_-1]&255),E=0),lt(C==4),e.avail_out===0?C==4?Xt:qe:C==4?Ln:Dn}function bl(C){return C.total_in=C.total_out=0,C.msg=null,n.pending=0,n.pending_out=0,t=Fn,i=0,Te(),he(),0}n.deflateInit=function(C,L,I,F,N,$){return F||(F=Or),N||(N=qc),$||($=0),C.msg=null,L==-1&&(L=6),N<1||N>Vc||F!=Or||I<9||I>15||L<0||L>9||$<0||$>2?-2:(C.dstate=n,a=I,s=1<<a,o=s-1,w=N+7,m=1<<w,f=m-1,p=Math.floor((w+ae-1)/ae),l=new Uint8Array(s*2),u=[],d=[],q=1<<N+6,n.pending_buf=new Uint8Array(q*4),r=q*4,n.dist_buf=new Uint16Array(q),n.lc_buf=new Uint8Array(q),T=L,D=$,bl(C))},n.deflateEnd=function(){return t!=Fr&&t!=Fn&&t!=Gt?-2:(n.lc_buf=null,n.dist_buf=null,n.pending_buf=null,d=null,u=null,l=null,n.dstate=null,t==Fn?-3:0)},n.deflateParams=function(C,L,I){let F=0;return L==-1&&(L=6),L<0||L>9||I<0||I>2?-2:(Ve[T].func!=Ve[L].func&&C.total_in!==0&&(F=C.deflate(1)),T!=L&&(T=L,A=Ve[T].max_lazy,R=Ve[T].good_length,U=Ve[T].nice_length,k=Ve[T].max_chain),D=I,F)},n.deflateSetDictionary=function(C,L,I){let F=I,N,$=0;if(!L||t!=Fr)return-2;if(F<ae)return 0;for(F>s-De&&(F=s-De,$=I-F),l.set(L.subarray($,$+F),0),_=F,g=F,h=l[0]&255,h=(h<<p^l[1]&255)&f,N=0;N<=F-ae;N++)h=(h<<p^l[N+(ae-1)]&255)&f,u[N&o]=d[h],d[h]=N;return 0},n.deflate=function(C,L){let I,F,N,$,G;if(L>4||L<0)return-2;if(!C.next_out||!C.next_in&&C.avail_in!==0||t==Gt&&L!=4)return C.msg=zn[2- -2],-2;if(C.avail_out===0)return C.msg=zn[2- -5],-5;if(e=C,$=i,i=L,t==Fr&&(F=Or+(a-8<<4)<<8,N=(T-1&255)>>1,N>3&&(N=3),F|=N<<6,_!==0&&(F|=Xc),F+=31-F%31,t=Fn,Ce(F)),n.pending!==0){if(e.flush_pending(),e.avail_out===0)return i=-1,0}else if(e.avail_in===0&&L<=$&&L!=4)return e.msg=zn[2- -5],-5;if(t==Gt&&e.avail_in!==0)return C.msg=zn[2- -5],-5;if(e.avail_in!==0||S!==0||L!=0&&t!=Gt){switch(G=-1,Ve[T].func){case ya:G=gl(L);break;case Nn:G=ml(L);break;case St:G=wl(L);break;default:}if((G==Xt||G==Ln)&&(t=Gt),G==qe||G==Xt)return e.avail_out===0&&(i=-1),0;if(G==Dn){if(L==1)dr();else if(Bi(0,0,!1),L==3)for(I=0;I<m;I++)d[I]=0;if(e.flush_pending(),e.avail_out===0)return i=-1,0}}return L!=4?0:1}}function xa(){let n=this;n.next_in_index=0,n.next_out_index=0,n.avail_in=0,n.total_in=0,n.avail_out=0,n.total_out=0}xa.prototype={deflateInit(n,e){let t=this;return t.dstate=new Kc,e||(e=15),t.dstate.deflateInit(t,n,e)},deflate(n){let e=this;return e.dstate?e.dstate.deflate(e,n):-2},deflateEnd(){let n=this;if(!n.dstate)return-2;let e=n.dstate.deflateEnd();return n.dstate=null,e},deflateParams(n,e){let t=this;return t.dstate?t.dstate.deflateParams(t,n,e):-2},deflateSetDictionary(n,e){let t=this;return t.dstate?t.dstate.deflateSetDictionary(t,n,e):-2},read_buf(n,e,t){let r=this,i=r.avail_in;return i>t&&(i=t),i===0?0:(r.avail_in-=i,n.set(r.next_in.subarray(r.next_in_index,r.next_in_index+i),e),r.next_in_index+=i,r.total_in+=i,i)},flush_pending(){let n=this,e=n.dstate.pending;e>n.avail_out&&(e=n.avail_out),e!==0&&(n.next_out.set(n.dstate.pending_buf.subarray(n.dstate.pending_out,n.dstate.pending_out+e),n.next_out_index),n.next_out_index+=e,n.dstate.pending_out+=e,n.total_out+=e,n.avail_out-=e,n.dstate.pending-=e,n.dstate.pending===0&&(n.dstate.pending_out=0))}};function Yc(n){let e=this,t=new xa,r=Jc(n&&n.chunkSize?n.chunkSize:64*1024),i=0,s=new Uint8Array(r),a=n?n.level:-1;typeof a>"u"&&(a=-1),t.deflateInit(a),t.next_out=s,e.append=function(o,l){let c,u,d=0,h=0,m=0,w=[];if(o.length){t.next_in_index=0,t.next_in=o,t.avail_in=o.length;do{if(t.next_out_index=0,t.avail_out=r,c=t.deflate(i),c!=0)throw new Error("deflating: "+t.msg);t.next_out_index&&(t.next_out_index==r?w.push(new Uint8Array(s)):w.push(s.slice(0,t.next_out_index))),m+=t.next_out_index,l&&t.next_in_index>0&&t.next_in_index!=d&&(l(t.next_in_index),d=t.next_in_index)}while(t.avail_in>0||t.avail_out===0);return w.length>1?(u=new Uint8Array(m),w.forEach(function(f){u.set(f,h),h+=f.length})):u=w[0]||new Uint8Array,u}},e.flush=function(){let o,l,c=0,u=0,d=[];do{if(t.next_out_index=0,t.avail_out=r,o=t.deflate(4),o!=1&&o!=0)throw new Error("deflating: "+t.msg);r-t.avail_out>0&&d.push(s.slice(0,t.next_out_index)),u+=t.next_out_index}while(t.avail_in>0||t.avail_out===0);return t.deflateEnd(),l=new Uint8Array(u),d.forEach(function(h){l.set(h,c),c+=h.length}),l}}function Jc(n){return n+5*(Math.floor(n/16383)+1)}var Le=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],va=1440,Qc=0,eu=4,tu=9,nu=5,ru=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],iu=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],su=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],au=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],ou=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],lu=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],ht=15;function qr(){let n=this,e,t,r,i,s,a;function o(c,u,d,h,m,w,f,p,g,b,y){let E,_,x,S,v,k,A,T,D,R,U,z,O,M,P;R=0,v=d;do r[c[u+R]]++,R++,v--;while(v!==0);if(r[0]==d)return f[0]=-1,p[0]=0,0;for(T=p[0],k=1;k<=ht&&r[k]===0;k++);for(A=k,T<k&&(T=k),v=ht;v!==0&&r[v]===0;v--);for(x=v,T>v&&(T=v),p[0]=T,M=1<<k;k<v;k++,M<<=1)if((M-=r[k])<0)return-3;if((M-=r[v])<0)return-3;for(r[v]+=M,a[1]=k=0,R=1,O=2;--v!==0;)a[O]=k+=r[R],O++,R++;v=0,R=0;do(k=c[u+R])!==0&&(y[a[k]++]=v),R++;while(++v<d);for(d=a[x],a[0]=v=0,R=0,S=-1,z=-T,s[0]=0,U=0,P=0;A<=x;A++)for(E=r[A];E--!==0;){for(;A>z+T;){if(S++,z+=T,P=x-z,P=P>T?T:P,(_=1<<(k=A-z))>E+1&&(_-=E+1,O=A,k<P))for(;++k<P&&!((_<<=1)<=r[++O]);)_-=r[O];if(P=1<<k,b[0]+P>va)return-3;s[S]=U=b[0],b[0]+=P,S!==0?(a[S]=v,i[0]=k,i[1]=T,k=v>>>z-T,i[2]=U-s[S-1]-k,g.set(i,(s[S-1]+k)*3)):f[0]=U}for(i[1]=A-z,R>=d?i[0]=128+64:y[R]<h?(i[0]=y[R]<256?0:32+64,i[2]=y[R++]):(i[0]=w[y[R]-h]+16+64,i[2]=m[y[R++]-h]),_=1<<A-z,k=v>>>z;k<P;k+=_)g.set(i,(U+k)*3);for(k=1<<A-1;v&k;k>>>=1)v^=k;for(v^=k,D=(1<<z)-1;(v&D)!=a[S];)S--,z-=T,D=(1<<z)-1}return M!==0&&x!=1?-5:0}function l(c){let u;for(e||(e=[],t=[],r=new Int32Array(ht+1),i=[],s=new Int32Array(ht),a=new Int32Array(ht+1)),t.length<c&&(t=[]),u=0;u<c;u++)t[u]=0;for(u=0;u<ht+1;u++)r[u]=0;for(u=0;u<3;u++)i[u]=0;s.set(r.subarray(0,ht),0),a.set(r.subarray(0,ht+1),0)}n.inflate_trees_bits=function(c,u,d,h,m){let w;return l(19),e[0]=0,w=o(c,0,19,19,null,null,d,u,h,e,t),w==-3?m.msg="oversubscribed dynamic bit lengths tree":(w==-5||u[0]===0)&&(m.msg="incomplete dynamic bit lengths tree",w=-3),w},n.inflate_trees_dynamic=function(c,u,d,h,m,w,f,p,g){let b;return l(288),e[0]=0,b=o(d,0,c,257,su,au,w,h,p,e,t),b!=0||h[0]===0?(b==-3?g.msg="oversubscribed literal/length tree":b!=-4&&(g.msg="incomplete literal/length tree",b=-3),b):(l(288),b=o(d,c,u,0,ou,lu,f,m,p,e,t),b!=0||m[0]===0&&c>257?(b==-3?g.msg="oversubscribed distance tree":b==-5?(g.msg="incomplete distance tree",b=-3):b!=-4&&(g.msg="empty distance tree with lengths",b=-3),b):0)}}qr.inflate_trees_fixed=function(n,e,t,r){return n[0]=tu,e[0]=nu,t[0]=ru,r[0]=iu,0};var On=0,Ls=1,Fs=2,Os=3,Ms=4,Us=5,Bs=6,Ur=7,Ns=8,Mn=9;function cu(){let n=this,e,t=0,r,i=0,s=0,a=0,o=0,l=0,c=0,u=0,d,h=0,m,w=0;function f(p,g,b,y,E,_,x,S){let v,k,A,T,D,R,U,z,O,M,P,Z,B,q,H,W;U=S.next_in_index,z=S.avail_in,D=x.bitb,R=x.bitk,O=x.write,M=O<x.read?x.read-O-1:x.end-O,P=Le[p],Z=Le[g];do{for(;R<20;)z--,D|=(S.read_byte(U++)&255)<<R,R+=8;if(v=D&P,k=b,A=y,W=(A+v)*3,(T=k[W])===0){D>>=k[W+1],R-=k[W+1],x.win[O++]=k[W+2],M--;continue}do{if(D>>=k[W+1],R-=k[W+1],T&16){for(T&=15,B=k[W+2]+(D&Le[T]),D>>=T,R-=T;R<15;)z--,D|=(S.read_byte(U++)&255)<<R,R+=8;v=D&Z,k=E,A=_,W=(A+v)*3,T=k[W];do if(D>>=k[W+1],R-=k[W+1],T&16){for(T&=15;R<T;)z--,D|=(S.read_byte(U++)&255)<<R,R+=8;if(q=k[W+2]+(D&Le[T]),D>>=T,R-=T,M-=B,O>=q)H=O-q,O-H>0&&2>O-H?(x.win[O++]=x.win[H++],x.win[O++]=x.win[H++],B-=2):(x.win.set(x.win.subarray(H,H+2),O),O+=2,H+=2,B-=2);else{H=O-q;do H+=x.end;while(H<0);if(T=x.end-H,B>T){if(B-=T,O-H>0&&T>O-H)do x.win[O++]=x.win[H++];while(--T!==0);else x.win.set(x.win.subarray(H,H+T),O),O+=T,H+=T,T=0;H=0}}if(O-H>0&&B>O-H)do x.win[O++]=x.win[H++];while(--B!==0);else x.win.set(x.win.subarray(H,H+B),O),O+=B,H+=B,B=0;break}else if(!(T&64))v+=k[W+2],v+=D&Le[T],W=(A+v)*3,T=k[W];else return S.msg="invalid distance code",B=S.avail_in-z,B=R>>3<B?R>>3:B,z+=B,U-=B,R-=B<<3,x.bitb=D,x.bitk=R,S.avail_in=z,S.total_in+=U-S.next_in_index,S.next_in_index=U,x.write=O,-3;while(!0);break}if(T&64)return T&32?(B=S.avail_in-z,B=R>>3<B?R>>3:B,z+=B,U-=B,R-=B<<3,x.bitb=D,x.bitk=R,S.avail_in=z,S.total_in+=U-S.next_in_index,S.next_in_index=U,x.write=O,1):(S.msg="invalid literal/length code",B=S.avail_in-z,B=R>>3<B?R>>3:B,z+=B,U-=B,R-=B<<3,x.bitb=D,x.bitk=R,S.avail_in=z,S.total_in+=U-S.next_in_index,S.next_in_index=U,x.write=O,-3);if(v+=k[W+2],v+=D&Le[T],W=(A+v)*3,(T=k[W])===0){D>>=k[W+1],R-=k[W+1],x.win[O++]=k[W+2],M--;break}}while(!0)}while(M>=258&&z>=10);return B=S.avail_in-z,B=R>>3<B?R>>3:B,z+=B,U-=B,R-=B<<3,x.bitb=D,x.bitk=R,S.avail_in=z,S.total_in+=U-S.next_in_index,S.next_in_index=U,x.write=O,0}n.init=function(p,g,b,y,E,_){e=On,c=p,u=g,d=b,h=y,m=E,w=_,r=null},n.proc=function(p,g,b){let y,E,_,x=0,S=0,v=0,k,A,T,D;for(v=g.next_in_index,k=g.avail_in,x=p.bitb,S=p.bitk,A=p.write,T=A<p.read?p.read-A-1:p.end-A;;)switch(e){case On:if(T>=258&&k>=10&&(p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,b=f(c,u,d,h,m,w,p,g),v=g.next_in_index,k=g.avail_in,x=p.bitb,S=p.bitk,A=p.write,T=A<p.read?p.read-A-1:p.end-A,b!=0)){e=b==1?Ur:Mn;break}s=c,r=d,i=h,e=Ls;case Ls:for(y=s;S<y;){if(k!==0)b=0;else return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);k--,x|=(g.read_byte(v++)&255)<<S,S+=8}if(E=(i+(x&Le[y]))*3,x>>>=r[E+1],S-=r[E+1],_=r[E],_===0){a=r[E+2],e=Bs;break}if(_&16){o=_&15,t=r[E+2],e=Fs;break}if(!(_&64)){s=_,i=E/3+r[E+2];break}if(_&32){e=Ur;break}return e=Mn,g.msg="invalid literal/length code",b=-3,p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);case Fs:for(y=o;S<y;){if(k!==0)b=0;else return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);k--,x|=(g.read_byte(v++)&255)<<S,S+=8}t+=x&Le[y],x>>=y,S-=y,s=u,r=m,i=w,e=Os;case Os:for(y=s;S<y;){if(k!==0)b=0;else return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);k--,x|=(g.read_byte(v++)&255)<<S,S+=8}if(E=(i+(x&Le[y]))*3,x>>=r[E+1],S-=r[E+1],_=r[E],_&16){o=_&15,l=r[E+2],e=Ms;break}if(!(_&64)){s=_,i=E/3+r[E+2];break}return e=Mn,g.msg="invalid distance code",b=-3,p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);case Ms:for(y=o;S<y;){if(k!==0)b=0;else return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);k--,x|=(g.read_byte(v++)&255)<<S,S+=8}l+=x&Le[y],x>>=y,S-=y,e=Us;case Us:for(D=A-l;D<0;)D+=p.end;for(;t!==0;){if(T===0&&(A==p.end&&p.read!==0&&(A=0,T=A<p.read?p.read-A-1:p.end-A),T===0&&(p.write=A,b=p.inflate_flush(g,b),A=p.write,T=A<p.read?p.read-A-1:p.end-A,A==p.end&&p.read!==0&&(A=0,T=A<p.read?p.read-A-1:p.end-A),T===0)))return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);p.win[A++]=p.win[D++],T--,D==p.end&&(D=0),t--}e=On;break;case Bs:if(T===0&&(A==p.end&&p.read!==0&&(A=0,T=A<p.read?p.read-A-1:p.end-A),T===0&&(p.write=A,b=p.inflate_flush(g,b),A=p.write,T=A<p.read?p.read-A-1:p.end-A,A==p.end&&p.read!==0&&(A=0,T=A<p.read?p.read-A-1:p.end-A),T===0)))return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);b=0,p.win[A++]=a,T--,e=On;break;case Ur:if(S>7&&(S-=8,k++,v--),p.write=A,b=p.inflate_flush(g,b),A=p.write,T=A<p.read?p.read-A-1:p.end-A,p.read!=p.write)return p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);e=Ns;case Ns:return b=1,p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);case Mn:return b=-3,p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b);default:return b=-2,p.bitb=x,p.bitk=S,g.avail_in=k,g.total_in+=v-g.next_in_index,g.next_in_index=v,p.write=A,p.inflate_flush(g,b)}},n.free=function(){}}var Ps=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Tt=0,Br=1,Hs=2,$s=3,Ws=4,js=5,Un=6,Bn=7,Vs=8,_t=9;function uu(n,e){let t=this,r=Tt,i=0,s=0,a=0,o,l=[0],c=[0],u=new cu,d=0,h=new Int32Array(va*3),m=0,w=new qr;t.bitk=0,t.bitb=0,t.win=new Uint8Array(e),t.end=e,t.read=0,t.write=0,t.reset=function(f,p){p&&(p[0]=m),r==Un&&u.free(f),r=Tt,t.bitk=0,t.bitb=0,t.read=t.write=0},t.reset(n,null),t.inflate_flush=function(f,p){let g,b,y;return b=f.next_out_index,y=t.read,g=(y<=t.write?t.write:t.end)-y,g>f.avail_out&&(g=f.avail_out),g!==0&&p==-5&&(p=0),f.avail_out-=g,f.total_out+=g,f.next_out.set(t.win.subarray(y,y+g),b),b+=g,y+=g,y==t.end&&(y=0,t.write==t.end&&(t.write=0),g=t.write-y,g>f.avail_out&&(g=f.avail_out),g!==0&&p==-5&&(p=0),f.avail_out-=g,f.total_out+=g,f.next_out.set(t.win.subarray(y,y+g),b),b+=g,y+=g),f.next_out_index=b,t.read=y,p},t.proc=function(f,p){let g,b,y,E,_,x,S,v;for(E=f.next_in_index,_=f.avail_in,b=t.bitb,y=t.bitk,x=t.write,S=x<t.read?t.read-x-1:t.end-x;;){let k,A,T,D,R,U,z,O;switch(r){case Tt:for(;y<3;){if(_!==0)p=0;else return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);_--,b|=(f.read_byte(E++)&255)<<y,y+=8}switch(g=b&7,d=g&1,g>>>1){case 0:b>>>=3,y-=3,g=y&7,b>>>=g,y-=g,r=Br;break;case 1:k=[],A=[],T=[[]],D=[[]],qr.inflate_trees_fixed(k,A,T,D),u.init(k[0],A[0],T[0],0,D[0],0),b>>>=3,y-=3,r=Un;break;case 2:b>>>=3,y-=3,r=$s;break;case 3:return b>>>=3,y-=3,r=_t,f.msg="invalid block type",p=-3,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p)}break;case Br:for(;y<32;){if(_!==0)p=0;else return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);_--,b|=(f.read_byte(E++)&255)<<y,y+=8}if((~b>>>16&65535)!=(b&65535))return r=_t,f.msg="invalid stored block lengths",p=-3,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);i=b&65535,b=y=0,r=i!==0?Hs:d!==0?Bn:Tt;break;case Hs:if(_===0||S===0&&(x==t.end&&t.read!==0&&(x=0,S=x<t.read?t.read-x-1:t.end-x),S===0&&(t.write=x,p=t.inflate_flush(f,p),x=t.write,S=x<t.read?t.read-x-1:t.end-x,x==t.end&&t.read!==0&&(x=0,S=x<t.read?t.read-x-1:t.end-x),S===0)))return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);if(p=0,g=i,g>_&&(g=_),g>S&&(g=S),t.win.set(f.read_buf(E,g),x),E+=g,_-=g,x+=g,S-=g,(i-=g)!==0)break;r=d!==0?Bn:Tt;break;case $s:for(;y<14;){if(_!==0)p=0;else return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);_--,b|=(f.read_byte(E++)&255)<<y,y+=8}if(s=g=b&16383,(g&31)>29||(g>>5&31)>29)return r=_t,f.msg="too many length or distance symbols",p=-3,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);if(g=258+(g&31)+(g>>5&31),!o||o.length<g)o=[];else for(v=0;v<g;v++)o[v]=0;b>>>=14,y-=14,a=0,r=Ws;case Ws:for(;a<4+(s>>>10);){for(;y<3;){if(_!==0)p=0;else return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);_--,b|=(f.read_byte(E++)&255)<<y,y+=8}o[Ps[a++]]=b&7,b>>>=3,y-=3}for(;a<19;)o[Ps[a++]]=0;if(l[0]=7,g=w.inflate_trees_bits(o,l,c,h,f),g!=0)return p=g,p==-3&&(o=null,r=_t),t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);a=0,r=js;case js:for(;g=s,!(a>=258+(g&31)+(g>>5&31));){let M,P;for(g=l[0];y<g;){if(_!==0)p=0;else return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);_--,b|=(f.read_byte(E++)&255)<<y,y+=8}if(g=h[(c[0]+(b&Le[g]))*3+1],P=h[(c[0]+(b&Le[g]))*3+2],P<16)b>>>=g,y-=g,o[a++]=P;else{for(v=P==18?7:P-14,M=P==18?11:3;y<g+v;){if(_!==0)p=0;else return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);_--,b|=(f.read_byte(E++)&255)<<y,y+=8}if(b>>>=g,y-=g,M+=b&Le[v],b>>>=v,y-=v,v=a,g=s,v+M>258+(g&31)+(g>>5&31)||P==16&&v<1)return o=null,r=_t,f.msg="invalid bit length repeat",p=-3,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);P=P==16?o[v-1]:0;do o[v++]=P;while(--M!==0);a=v}}if(c[0]=-1,R=[],U=[],z=[],O=[],R[0]=9,U[0]=6,g=s,g=w.inflate_trees_dynamic(257+(g&31),1+(g>>5&31),o,R,U,z,O,h,f),g!=0)return g==-3&&(o=null,r=_t),p=g,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);u.init(R[0],U[0],h,z[0],h,O[0]),r=Un;case Un:if(t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,(p=u.proc(t,f,p))!=1)return t.inflate_flush(f,p);if(p=0,u.free(f),E=f.next_in_index,_=f.avail_in,b=t.bitb,y=t.bitk,x=t.write,S=x<t.read?t.read-x-1:t.end-x,d===0){r=Tt;break}r=Bn;case Bn:if(t.write=x,p=t.inflate_flush(f,p),x=t.write,S=x<t.read?t.read-x-1:t.end-x,t.read!=t.write)return t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);r=Vs;case Vs:return p=1,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);case _t:return p=-3,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p);default:return p=-2,t.bitb=b,t.bitk=y,f.avail_in=_,f.total_in+=E-f.next_in_index,f.next_in_index=E,t.write=x,t.inflate_flush(f,p)}}},t.free=function(f){t.reset(f,null),t.win=null,h=null},t.set_dictionary=function(f,p,g){t.win.set(f.subarray(p,p+g),0),t.read=t.write=g},t.sync_point=function(){return r==Br?1:0}}var du=32,hu=8,fu=0,qs=1,Xs=2,Gs=3,Zs=4,Ks=5,Nr=6,Zt=7,Ys=12,ft=13,pu=[0,0,255,255];function gu(){let n=this;n.mode=0,n.method=0,n.was=[0],n.need=0,n.marker=0,n.wbits=0;function e(t){return!t||!t.istate?-2:(t.total_in=t.total_out=0,t.msg=null,t.istate.mode=Zt,t.istate.blocks.reset(t,null),0)}n.inflateEnd=function(t){return n.blocks&&n.blocks.free(t),n.blocks=null,0},n.inflateInit=function(t,r){return t.msg=null,n.blocks=null,r<8||r>15?(n.inflateEnd(t),-2):(n.wbits=r,t.istate.blocks=new uu(t,1<<r),e(t),0)},n.inflate=function(t,r){let i,s;if(!t||!t.istate||!t.next_in)return-2;let a=t.istate;for(r=r==eu?-5:0,i=-5;;)switch(a.mode){case fu:if(t.avail_in===0)return i;if(i=r,t.avail_in--,t.total_in++,((a.method=t.read_byte(t.next_in_index++))&15)!=hu){a.mode=ft,t.msg="unknown compression method",a.marker=5;break}if((a.method>>4)+8>a.wbits){a.mode=ft,t.msg="invalid win size",a.marker=5;break}a.mode=qs;case qs:if(t.avail_in===0)return i;if(i=r,t.avail_in--,t.total_in++,s=t.read_byte(t.next_in_index++)&255,((a.method<<8)+s)%31!==0){a.mode=ft,t.msg="incorrect header check",a.marker=5;break}if(!(s&du)){a.mode=Zt;break}a.mode=Xs;case Xs:if(t.avail_in===0)return i;i=r,t.avail_in--,t.total_in++,a.need=(t.read_byte(t.next_in_index++)&255)<<24&4278190080,a.mode=Gs;case Gs:if(t.avail_in===0)return i;i=r,t.avail_in--,t.total_in++,a.need+=(t.read_byte(t.next_in_index++)&255)<<16&16711680,a.mode=Zs;case Zs:if(t.avail_in===0)return i;i=r,t.avail_in--,t.total_in++,a.need+=(t.read_byte(t.next_in_index++)&255)<<8&65280,a.mode=Ks;case Ks:return t.avail_in===0?i:(i=r,t.avail_in--,t.total_in++,a.need+=t.read_byte(t.next_in_index++)&255,a.mode=Nr,2);case Nr:return a.mode=ft,t.msg="need dictionary",a.marker=0,-2;case Zt:if(i=a.blocks.proc(t,i),i==-3){a.mode=ft,a.marker=0;break}if(i==0&&(i=r),i!=1)return i;i=r,a.blocks.reset(t,a.was),a.mode=Ys;case Ys:return t.avail_in=0,1;case ft:return-3;default:return-2}},n.inflateSetDictionary=function(t,r,i){let s=0,a=i;if(!t||!t.istate||t.istate.mode!=Nr)return-2;let o=t.istate;return a>=1<<o.wbits&&(a=(1<<o.wbits)-1,s=i-a),o.blocks.set_dictionary(r,s,a),o.mode=Zt,0},n.inflateSync=function(t){let r,i,s,a,o;if(!t||!t.istate)return-2;let l=t.istate;if(l.mode!=ft&&(l.mode=ft,l.marker=0),(r=t.avail_in)===0)return-5;for(i=t.next_in_index,s=l.marker;r!==0&&s<4;)t.read_byte(i)==pu[s]?s++:t.read_byte(i)!==0?s=0:s=4-s,i++,r--;return t.total_in+=i-t.next_in_index,t.next_in_index=i,t.avail_in=r,l.marker=s,s!=4?-3:(a=t.total_in,o=t.total_out,e(t),t.total_in=a,t.total_out=o,l.mode=Zt,0)},n.inflateSyncPoint=function(t){return!t||!t.istate||!t.istate.blocks?-2:t.istate.blocks.sync_point()}}function _a(){}_a.prototype={inflateInit(n){let e=this;return e.istate=new gu,n||(n=15),e.istate.inflateInit(e,n)},inflate(n){let e=this;return e.istate?e.istate.inflate(e,n):-2},inflateEnd(){let n=this;if(!n.istate)return-2;let e=n.istate.inflateEnd(n);return n.istate=null,e},inflateSync(){let n=this;return n.istate?n.istate.inflateSync(n):-2},inflateSetDictionary(n,e){let t=this;return t.istate?t.istate.inflateSetDictionary(t,n,e):-2},read_byte(n){return this.next_in[n]},read_buf(n,e){return this.next_in.subarray(n,n+e)}};function mu(n){let e=this,t=new _a,r=n&&n.chunkSize?Math.floor(n.chunkSize*2):128*1024,i=Qc,s=new Uint8Array(r),a=!1;t.inflateInit(),t.next_out=s,e.append=function(o,l){let c=[],u,d,h=0,m=0,w=0;if(o.length!==0){t.next_in_index=0,t.next_in=o,t.avail_in=o.length;do{if(t.next_out_index=0,t.avail_out=r,t.avail_in===0&&!a&&(t.next_in_index=0,a=!0),u=t.inflate(i),a&&u===-5){if(t.avail_in!==0)throw new Error("inflating: bad input")}else if(u!==0&&u!==1)throw new Error("inflating: "+t.msg);if((a||u===1)&&t.avail_in===o.length)throw new Error("inflating: bad input");t.next_out_index&&(t.next_out_index===r?c.push(new Uint8Array(s)):c.push(s.slice(0,t.next_out_index))),w+=t.next_out_index,l&&t.next_in_index>0&&t.next_in_index!=h&&(l(t.next_in_index),h=t.next_in_index)}while(t.avail_in>0||t.avail_out===0);return c.length>1?(d=new Uint8Array(w),c.forEach(function(f){d.set(f,m),m+=f.length})):d=c[0]||new Uint8Array,d}},e.flush=function(){t.inflateEnd()}}var Hn="/",Js=new Date(2107,11,31),Qs=new Date(1980,0,1),de=void 0,jn="undefined",Sa="function",ea=class{constructor(n){return class extends TransformStream{constructor(e,t){let r=new n(t);super({transform(i,s){s.enqueue(r.append(i))},flush(i){let s=r.flush();s&&i.enqueue(s)}})}}}},wu=64,Aa=2;try{typeof navigator!=jn&&navigator.hardwareConcurrency&&(Aa=navigator.hardwareConcurrency)}catch{}var bu={chunkSize:512*1024,maxWorkers:Aa,terminateWorkerTimeout:5e3,useWebWorkers:!0,useCompressionStream:!0,workerScripts:de,CompressionStreamNative:typeof CompressionStream!=jn&&CompressionStream,DecompressionStreamNative:typeof DecompressionStream!=jn&&DecompressionStream},wt=Object.assign({},bu);function ii(){return wt}function Ea(n){return Math.max(n.chunkSize,wu)}function Yn(n){let{baseURL:e,chunkSize:t,maxWorkers:r,terminateWorkerTimeout:i,useCompressionStream:s,useWebWorkers:a,Deflate:o,Inflate:l,CompressionStream:c,DecompressionStream:u,workerScripts:d}=n;if(pt("baseURL",e),pt("chunkSize",t),pt("maxWorkers",r),pt("terminateWorkerTimeout",i),pt("useCompressionStream",s),pt("useWebWorkers",a),o&&(wt.CompressionStream=new ea(o)),l&&(wt.DecompressionStream=new ea(l)),pt("CompressionStream",c),pt("DecompressionStream",u),d!==de){let{deflate:h,inflate:m}=d;if((h||m)&&(wt.workerScripts||(wt.workerScripts={})),h){if(!Array.isArray(h))throw new Error("workerScripts.deflate must be an array");wt.workerScripts.deflate=h}if(m){if(!Array.isArray(m))throw new Error("workerScripts.inflate must be an array");wt.workerScripts.inflate=m}}}function pt(n,e){e!==de&&(wt[n]=e)}function yu(){return"application/octet-stream"}var ka=[];for(let n=0;n<256;n++){let e=n;for(let t=0;t<8;t++)e&1?e=e>>>1^3988292384:e=e>>>1;ka[n]=e}var Vn=class{constructor(n){this.crc=n||-1}append(n){let e=this.crc|0;for(let t=0,r=n.length|0;t<r;t++)e=e>>>8^ka[(e^n[t])&255];this.crc=e}get(){return~this.crc}},Ta=class extends TransformStream{constructor(){let n=new Vn;super({transform(e){n.append(e)},flush(e){let t=new Uint8Array(4);new DataView(t.buffer).setUint32(0,n.get()),e.enqueue(t)}})}};function Xr(n){if(typeof TextEncoder>"u"){n=unescape(encodeURIComponent(n));let e=new Uint8Array(n.length);for(let t=0;t<e.length;t++)e[t]=n.charCodeAt(t);return e}else return new TextEncoder().encode(n)}var ke={concat(n,e){if(n.length===0||e.length===0)return n.concat(e);let t=n[n.length-1],r=ke.getPartial(t);return r===32?n.concat(e):ke._shiftRight(e,r,t|0,n.slice(0,n.length-1))},bitLength(n){let e=n.length;if(e===0)return 0;let t=n[e-1];return(e-1)*32+ke.getPartial(t)},clamp(n,e){if(n.length*32<e)return n;n=n.slice(0,Math.ceil(e/32));let t=n.length;return e=e&31,t>0&&e&&(n[t-1]=ke.partial(e,n[t-1]&2147483648>>e-1,1)),n},partial(n,e,t){return n===32?e:(t?e|0:e<<32-n)+n*1099511627776},getPartial(n){return Math.round(n/1099511627776)||32},_shiftRight(n,e,t,r){for(r===void 0&&(r=[]);e>=32;e-=32)r.push(t),t=0;if(e===0)return r.concat(n);for(let a=0;a<n.length;a++)r.push(t|n[a]>>>e),t=n[a]<<32-e;let i=n.length?n[n.length-1]:0,s=ke.getPartial(i);return r.push(ke.partial(e+s&31,e+s>32?t:r.pop(),1)),r}},qn={bytes:{fromBits(n){let e=ke.bitLength(n)/8,t=new Uint8Array(e),r;for(let i=0;i<e;i++)i&3||(r=n[i/4]),t[i]=r>>>24,r<<=8;return t},toBits(n){let e=[],t,r=0;for(t=0;t<n.length;t++)r=r<<8|n[t],(t&3)===3&&(e.push(r),r=0);return t&3&&e.push(ke.partial(8*(t&3),r)),e}}},Ca={};Ca.sha1=class{constructor(n){let e=this;e.blockSize=512,e._init=[1732584193,4023233417,2562383102,271733878,3285377520],e._key=[1518500249,1859775393,2400959708,3395469782],n?(e._h=n._h.slice(0),e._buffer=n._buffer.slice(0),e._length=n._length):e.reset()}reset(){let n=this;return n._h=n._init.slice(0),n._buffer=[],n._length=0,n}update(n){let e=this;typeof n=="string"&&(n=qn.utf8String.toBits(n));let t=e._buffer=ke.concat(e._buffer,n),r=e._length,i=e._length=r+ke.bitLength(n);if(i>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");let s=new Uint32Array(t),a=0;for(let o=e.blockSize+r-(e.blockSize+r&e.blockSize-1);o<=i;o+=e.blockSize)e._block(s.subarray(16*a,16*(a+1))),a+=1;return t.splice(0,16*a),e}finalize(){let n=this,e=n._buffer,t=n._h;e=ke.concat(e,[ke.partial(1,1)]);for(let r=e.length+2;r&15;r++)e.push(0);for(e.push(Math.floor(n._length/4294967296)),e.push(n._length|0);e.length;)n._block(e.splice(0,16));return n.reset(),t}_f(n,e,t,r){if(n<=19)return e&t|~e&r;if(n<=39)return e^t^r;if(n<=59)return e&t|e&r|t&r;if(n<=79)return e^t^r}_S(n,e){return e<<n|e>>>32-n}_block(n){let e=this,t=e._h,r=Array(80);for(let c=0;c<16;c++)r[c]=n[c];let i=t[0],s=t[1],a=t[2],o=t[3],l=t[4];for(let c=0;c<=79;c++){c>=16&&(r[c]=e._S(1,r[c-3]^r[c-8]^r[c-14]^r[c-16]));let u=e._S(5,i)+e._f(c,s,a,o)+l+r[c]+e._key[Math.floor(c/20)]|0;l=o,o=a,a=e._S(30,s),s=i,i=u}t[0]=t[0]+i|0,t[1]=t[1]+s|0,t[2]=t[2]+a|0,t[3]=t[3]+o|0,t[4]=t[4]+l|0}};var Ra={};Ra.aes=class{constructor(n){let e=this;e._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],e._tables[0][0][0]||e._precompute();let t=e._tables[0][4],r=e._tables[1],i=n.length,s,a,o,l=1;if(i!==4&&i!==6&&i!==8)throw new Error("invalid aes key size");for(e._key=[a=n.slice(0),o=[]],s=i;s<4*i+28;s++){let c=a[s-1];(s%i===0||i===8&&s%i===4)&&(c=t[c>>>24]<<24^t[c>>16&255]<<16^t[c>>8&255]<<8^t[c&255],s%i===0&&(c=c<<8^c>>>24^l<<24,l=l<<1^(l>>7)*283)),a[s]=a[s-i]^c}for(let c=0;s;c++,s--){let u=a[c&3?s:s-4];s<=4||c<4?o[c]=u:o[c]=r[0][t[u>>>24]]^r[1][t[u>>16&255]]^r[2][t[u>>8&255]]^r[3][t[u&255]]}}encrypt(n){return this._crypt(n,0)}decrypt(n){return this._crypt(n,1)}_precompute(){let n=this._tables[0],e=this._tables[1],t=n[4],r=e[4],i=[],s=[],a,o,l,c;for(let u=0;u<256;u++)s[(i[u]=u<<1^(u>>7)*283)^u]=u;for(let u=a=0;!t[u];u^=o||1,a=s[a]||1){let d=a^a<<1^a<<2^a<<3^a<<4;d=d>>8^d&255^99,t[u]=d,r[d]=u,c=i[l=i[o=i[u]]];let h=c*16843009^l*65537^o*257^u*16843008,m=i[d]*257^d*16843008;for(let w=0;w<4;w++)n[w][u]=m=m<<24^m>>>8,e[w][d]=h=h<<24^h>>>8}for(let u=0;u<5;u++)n[u]=n[u].slice(0),e[u]=e[u].slice(0)}_crypt(n,e){if(n.length!==4)throw new Error("invalid aes block size");let t=this._key[e],r=t.length/4-2,i=[0,0,0,0],s=this._tables[e],a=s[0],o=s[1],l=s[2],c=s[3],u=s[4],d=n[0]^t[0],h=n[e?3:1]^t[1],m=n[2]^t[2],w=n[e?1:3]^t[3],f=4,p,g,b;for(let y=0;y<r;y++)p=a[d>>>24]^o[h>>16&255]^l[m>>8&255]^c[w&255]^t[f],g=a[h>>>24]^o[m>>16&255]^l[w>>8&255]^c[d&255]^t[f+1],b=a[m>>>24]^o[w>>16&255]^l[d>>8&255]^c[h&255]^t[f+2],w=a[w>>>24]^o[d>>16&255]^l[h>>8&255]^c[m&255]^t[f+3],f+=4,d=p,h=g,m=b;for(let y=0;y<4;y++)i[e?3&-y:y]=u[d>>>24]<<24^u[h>>16&255]<<16^u[m>>8&255]<<8^u[w&255]^t[f++],p=d,d=h,h=m,m=w,w=p;return i}};var xu={getRandomValues(n){let e=new Uint32Array(n.buffer),t=r=>{let i=987654321,s=4294967295;return function(){return i=36969*(i&65535)+(i>>16)&s,r=18e3*(r&65535)+(r>>16)&s,(((i<<16)+r&s)/4294967296+.5)*(Math.random()>.5?1:-1)}};for(let r=0,i;r<n.length;r+=4){let s=t((i||Math.random())*4294967296);i=s()*987654071,e[r/4]=s()*4294967296|0}return n}},Ia={};Ia.ctrGladman=class{constructor(n,e){this._prf=n,this._initIv=e,this._iv=e}reset(){this._iv=this._initIv}update(n){return this.calculate(this._prf,n,this._iv)}incWord(n){if((n>>24&255)===255){let e=n>>16&255,t=n>>8&255,r=n&255;e===255?(e=0,t===255?(t=0,r===255?r=0:++r):++t):++e,n=0,n+=e<<16,n+=t<<8,n+=r}else n+=1<<24;return n}incCounter(n){(n[0]=this.incWord(n[0]))===0&&(n[1]=this.incWord(n[1]))}calculate(n,e,t){let r;if(!(r=e.length))return[];let i=ke.bitLength(e);for(let s=0;s<r;s+=4){this.incCounter(t);let a=n.encrypt(t);e[s]^=a[0],e[s+1]^=a[1],e[s+2]^=a[2],e[s+3]^=a[3]}return ke.clamp(e,i)}};var At={importKey(n){return new At.hmacSha1(qn.bytes.toBits(n))},pbkdf2(n,e,t,r){if(t=t||1e4,r<0||t<0)throw new Error("invalid params to pbkdf2");let i=(r>>5)+1<<2,s,a,o,l,c,u=new ArrayBuffer(i),d=new DataView(u),h=0,m=ke;for(e=qn.bytes.toBits(e),c=1;h<(i||1);c++){for(s=a=n.encrypt(m.concat(e,[c])),o=1;o<t;o++)for(a=n.encrypt(a),l=0;l<a.length;l++)s[l]^=a[l];for(o=0;h<(i||1)&&o<s.length;o++)d.setInt32(h,s[o]),h+=4}return u.slice(0,r/8)}};At.hmacSha1=class{constructor(n){let e=this,t=e._hash=Ca.sha1,r=[[],[]];e._baseHash=[new t,new t];let i=e._baseHash[0].blockSize/32;n.length>i&&(n=new t().update(n).finalize());for(let s=0;s<i;s++)r[0][s]=n[s]^909522486,r[1][s]=n[s]^1549556828;e._baseHash[0].update(r[0]),e._baseHash[1].update(r[1]),e._resultHash=new t(e._baseHash[0])}reset(){let n=this;n._resultHash=new n._hash(n._baseHash[0]),n._updated=!1}update(n){let e=this;e._updated=!0,e._resultHash.update(n)}digest(){let n=this,e=n._resultHash.finalize(),t=new n._hash(n._baseHash[1]).update(e).finalize();return n.reset(),t}encrypt(n){if(this._updated)throw new Error("encrypt on already updated hmac called!");return this.update(n),this.digest(n)}};var vu=typeof crypto<"u"&&typeof crypto.getRandomValues=="function",si="Invalid password",ai="Invalid signature";function za(n){return vu?crypto.getRandomValues(n):xu.getRandomValues(n)}var Ct=16,_u="raw",Da={name:"PBKDF2"},Su={name:"HMAC"},Au="SHA-1",Eu=Object.assign({hash:Su},Da),Gr=Object.assign({iterations:1e3,hash:{name:Au}},Da),ku=["deriveBits"],Yt=[8,12,16],Kt=[16,24,32],gt=10,Tu=[0,0,0,0],La="undefined",Fa="function",Jn=typeof crypto!=La,tn=Jn&&crypto.subtle,Oa=Jn&&typeof tn!=La,Ye=qn.bytes,Cu=Ra.aes,Ru=Ia.ctrGladman,Iu=At.hmacSha1,ta=Jn&&Oa&&typeof tn.importKey==Fa,na=Jn&&Oa&&typeof tn.deriveBits==Fa,zu=class extends TransformStream{constructor({password:n,signed:e,encryptionStrength:t}){super({start(){Object.assign(this,{ready:new Promise(r=>this.resolveReady=r),password:n,signed:e,strength:t-1,pending:new Uint8Array})},async transform(r,i){let s=this,{password:a,strength:o,resolveReady:l,ready:c}=s;a?(await Lu(s,o,a,Be(r,0,Yt[o]+2)),r=Be(r,Yt[o]+2),l()):await c;let u=new Uint8Array(r.length-gt-(r.length-gt)%Ct);i.enqueue(Ma(s,r,u,0,gt,!0))},async flush(r){let{signed:i,ctr:s,hmac:a,pending:o,ready:l}=this;await l;let c=Be(o,0,o.length-gt),u=Be(o,o.length-gt),d=new Uint8Array;if(c.length){let h=Qt(Ye,c);a.update(h);let m=s.update(h);d=Jt(Ye,m)}if(i){let h=Be(Jt(Ye,a.digest()),0,gt);for(let m=0;m<gt;m++)if(h[m]!=u[m])throw new Error(ai)}r.enqueue(d)}})}},Du=class extends TransformStream{constructor({password:n,encryptionStrength:e}){let t;super({start(){Object.assign(this,{ready:new Promise(r=>this.resolveReady=r),password:n,strength:e-1,pending:new Uint8Array})},async transform(r,i){let s=this,{password:a,strength:o,resolveReady:l,ready:c}=s,u=new Uint8Array;a?(u=await Fu(s,o,a),l()):await c;let d=new Uint8Array(u.length+r.length-r.length%Ct);d.set(u,0),i.enqueue(Ma(s,r,d,u.length,0))},async flush(r){let{ctr:i,hmac:s,pending:a,ready:o}=this;await o;let l=new Uint8Array;if(a.length){let c=i.update(Qt(Ye,a));s.update(c),l=Jt(Ye,c)}t.signature=Jt(Ye,s.digest()).slice(0,gt),r.enqueue(oi(l,t.signature))}}),t=this}};function Ma(n,e,t,r,i,s){let{ctr:a,hmac:o,pending:l}=n,c=e.length-i;l.length&&(e=oi(l,e),t=Uu(t,c-c%Ct));let u;for(u=0;u<=c-Ct;u+=Ct){let d=Qt(Ye,Be(e,u,u+Ct));s&&o.update(d);let h=a.update(d);s||o.update(h),t.set(Jt(Ye,h),u+r)}return n.pending=Be(e,u),t}async function Lu(n,e,t,r){let i=await Ua(n,e,t,Be(r,0,Yt[e])),s=Be(r,Yt[e]);if(i[0]!=s[0]||i[1]!=s[1])throw new Error(si)}async function Fu(n,e,t){let r=za(new Uint8Array(Yt[e])),i=await Ua(n,e,t,r);return oi(r,i)}async function Ua(n,e,t,r){n.password=null;let i=Xr(t),s=await Ou(_u,i,Eu,!1,ku),a=await Mu(Object.assign({salt:r},Gr),s,8*(Kt[e]*2+2)),o=new Uint8Array(a),l=Qt(Ye,Be(o,0,Kt[e])),c=Qt(Ye,Be(o,Kt[e],Kt[e]*2)),u=Be(o,Kt[e]*2);return Object.assign(n,{keys:{key:l,authentication:c,passwordVerification:u},ctr:new Ru(new Cu(l),Array.from(Tu)),hmac:new Iu(c)}),u}async function Ou(n,e,t,r,i){if(ta)try{return await tn.importKey(n,e,t,r,i)}catch{return ta=!1,At.importKey(e)}else return At.importKey(e)}async function Mu(n,e,t){if(na)try{return await tn.deriveBits(n,e,t)}catch{return na=!1,At.pbkdf2(e,n.salt,Gr.iterations,t)}else return At.pbkdf2(e,n.salt,Gr.iterations,t)}function oi(n,e){let t=n;return n.length+e.length&&(t=new Uint8Array(n.length+e.length),t.set(n,0),t.set(e,n.length)),t}function Uu(n,e){if(e&&e>n.length){let t=n;n=new Uint8Array(e),n.set(t,0)}return n}function Be(n,e,t){return n.subarray(e,t)}function Jt(n,e){return n.fromBits(e)}function Qt(n,e){return n.toBits(e)}var Rt=12,Bu=class extends TransformStream{constructor({password:n,passwordVerification:e}){super({start(){Object.assign(this,{password:n,passwordVerification:e}),Ba(this,n)},transform(t,r){let i=this;if(i.password){let s=ra(i,t.subarray(0,Rt));if(i.password=null,s[Rt-1]!=i.passwordVerification)throw new Error(si);t=t.subarray(Rt)}r.enqueue(ra(i,t))}})}},Nu=class extends TransformStream{constructor({password:n,passwordVerification:e}){super({start(){Object.assign(this,{password:n,passwordVerification:e}),Ba(this,n)},transform(t,r){let i=this,s,a;if(i.password){i.password=null;let o=za(new Uint8Array(Rt));o[Rt-1]=i.passwordVerification,s=new Uint8Array(t.length+o.length),s.set(ia(i,o),0),a=Rt}else s=new Uint8Array(t.length),a=0;s.set(ia(i,t),a),r.enqueue(s)}})}};function ra(n,e){let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=Na(n)^e[r],li(n,t[r]);return t}function ia(n,e){let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=Na(n)^e[r],li(n,e[r]);return t}function Ba(n,e){let t=[305419896,591751049,878082192];Object.assign(n,{keys:t,crcKey0:new Vn(t[0]),crcKey2:new Vn(t[2])});for(let r=0;r<e.length;r++)li(n,e.charCodeAt(r))}function li(n,e){let[t,r,i]=n.keys;n.crcKey0.append([e]),t=~n.crcKey0.get(),r=sa(Math.imul(sa(r+Pa(t)),134775813)+1),n.crcKey2.append([r>>>24]),i=~n.crcKey2.get(),n.keys=[t,r,i]}function Na(n){let e=n.keys[2]|2;return Pa(Math.imul(e,e^1)>>>8)}function Pa(n){return n&255}function sa(n){return n&4294967295}var aa="deflate-raw",Pu=class extends TransformStream{constructor(n,{chunkSize:e,CompressionStream:t,CompressionStreamNative:r}){super({});let{compressed:i,encrypted:s,useCompressionStream:a,zipCrypto:o,signed:l,level:c}=n,u=this,d,h,m=Ha(super.readable);(!s||o)&&l&&([m,d]=m.tee(),d=Je(d,new Ta)),i&&(m=Wa(m,a,{level:c,chunkSize:e},r,t)),s&&(o?m=Je(m,new Nu(n)):(h=new Du(n),m=Je(m,h))),$a(u,m,async()=>{let w;s&&!o&&(w=h.signature),(!s||o)&&l&&(w=await d.getReader().read(),w=new DataView(w.value.buffer).getUint32(0)),u.signature=w})}},Hu=class extends TransformStream{constructor(n,{chunkSize:e,DecompressionStream:t,DecompressionStreamNative:r}){super({});let{zipCrypto:i,encrypted:s,signed:a,signature:o,compressed:l,useCompressionStream:c}=n,u,d,h=Ha(super.readable);s&&(i?h=Je(h,new Bu(n)):(d=new zu(n),h=Je(h,d))),l&&(h=Wa(h,c,{chunkSize:e},r,t)),(!s||i)&&a&&([h,u]=h.tee(),u=Je(u,new Ta)),$a(this,h,async()=>{if((!s||i)&&a){let m=await u.getReader().read(),w=new DataView(m.value.buffer);if(o!=w.getUint32(0,!1))throw new Error(ai)}})}};function Ha(n){return Je(n,new TransformStream({transform(e,t){e&&e.length&&t.enqueue(e)}}))}function $a(n,e,t){e=Je(e,new TransformStream({flush:t})),Object.defineProperty(n,"readable",{get(){return e}})}function Wa(n,e,t,r,i){try{let s=e&&r?r:i;n=Je(n,new s(aa,t))}catch(s){if(e)n=Je(n,new i(aa,t));else throw s}return n}function Je(n,e){return n.pipeThrough(e)}var $u="message",Wu="start",ju="pull",oa="data",Vu="ack",qu="close",ja="deflate",Va="inflate",Xu=class extends TransformStream{constructor(n,e){super({});let t=this,{codecType:r}=n,i;r.startsWith(ja)?i=Pu:r.startsWith(Va)&&(i=Hu);let s=0,a=new i(n,e),o=super.readable,l=new TransformStream({transform(c,u){c&&c.length&&(s+=c.length,u.enqueue(c))},flush(){let{signature:c}=a;Object.assign(t,{signature:c,size:s})}});Object.defineProperty(t,"readable",{get(){return o.pipeThrough(a).pipeThrough(l)}})}},Gu=typeof Worker!=jn,Pr=class{constructor(n,{readable:e,writable:t},{options:r,config:i,streamOptions:s,useWebWorkers:a,transferStreams:o,scripts:l},c){let{signal:u}=s;return Object.assign(n,{busy:!0,readable:e.pipeThrough(new Zu(e,s,i),{signal:u}),writable:t,options:Object.assign({},r),scripts:l,transferStreams:o,terminate(){let{worker:d,busy:h}=n;d&&!h&&(d.terminate(),n.interface=null)},onTaskFinished(){n.busy=!1,c(n)}}),(a&&Gu?Yu:Ku)(n,i)}},Zu=class extends TransformStream{constructor(n,{onstart:e,onprogress:t,size:r,onend:i},{chunkSize:s}){let a=0;super({start(){e&&Hr(e,r)},async transform(o,l){a+=o.length,t&&await Hr(t,a,r),l.enqueue(o)},flush(){n.size=a,i&&Hr(i,a)}},{highWaterMark:1,size:()=>s})}};async function Hr(n,...e){try{await n(...e)}catch{}}function Ku(n,e){return{run:()=>Ju(n,e)}}function Yu(n,{baseURL:e,chunkSize:t}){return n.interface||Object.assign(n,{worker:td(n.scripts[0],e,n),interface:{run:()=>Qu(n,{chunkSize:t})}}),n.interface}async function Ju({options:n,readable:e,writable:t,onTaskFinished:r},i){let s=new Xu(n,i);try{await e.pipeThrough(s).pipeTo(t,{preventClose:!0,preventAbort:!0});let{signature:a,size:o}=s;return{signature:a,size:o}}finally{r()}}async function Qu(n,e){let t,r,i=new Promise((d,h)=>{t=d,r=h});Object.assign(n,{reader:null,writer:null,resolveResult:t,rejectResult:r,result:i});let{readable:s,options:a,scripts:o}=n,{writable:l,closed:c}=ed(n.writable);Zr({type:Wu,scripts:o.slice(1),options:a,config:e,readable:s,writable:l},n)||Object.assign(n,{reader:s.getReader(),writer:l.getWriter()});let u=await i;try{await l.close()}catch{}return await c,u}function ed(n){let e=n.getWriter(),t,r=new Promise(i=>t=i);return{writable:new WritableStream({async write(i){await e.ready,await e.write(i)},close(){e.releaseLock(),t()},abort(i){return e.abort(i)}}),closed:r}}var la=!0,ca=!0;function td(n,e,t){let r={type:"module"},i,s;typeof n==Sa&&(n=n());try{i=new URL(n,e)}catch{i=n}if(la)try{s=new Worker(i)}catch{la=!1,s=new Worker(i,r)}else s=new Worker(i,r);return s.addEventListener($u,a=>nd(a,t)),s}function Zr(n,{worker:e,writer:t,onTaskFinished:r,transferStreams:i}){try{let{value:s,readable:a,writable:o}=n,l=[];if(s){let{buffer:c,length:u}=s;u!=c.byteLength&&(s=new Uint8Array(s)),n.value=s.buffer,l.push(n.value)}if(i&&ca?(a&&l.push(a),o&&l.push(o)):n.readable=n.writable=null,l.length)try{return e.postMessage(n,l),!0}catch{ca=!1,n.readable=n.writable=null,e.postMessage(n)}else e.postMessage(n)}catch(s){throw t&&t.releaseLock(),r(),s}}async function nd({data:n},e){let{type:t,value:r,messageId:i,result:s,error:a}=n,{reader:o,writer:l,resolveResult:c,rejectResult:u,onTaskFinished:d}=e;try{if(a){let{message:m,stack:w,code:f,name:p}=a,g=new Error(m);Object.assign(g,{stack:w,code:f,name:p}),h(g)}else{if(t==ju){let{value:m,done:w}=await o.read();Zr({type:oa,value:m,done:w,messageId:i},e)}t==oa&&(await l.ready,await l.write(new Uint8Array(r)),Zr({type:Vu,messageId:i},e)),t==qu&&h(null,s)}}catch(m){h(m)}function h(m,w){m?u(m):c(w),l&&l.releaseLock(),d()}}var mt=[],$r=[],ua=0;async function qa(n,e){let{options:t,config:r}=e,{transferStreams:i,useWebWorkers:s,useCompressionStream:a,codecType:o,compressed:l,signed:c,encrypted:u}=t,{workerScripts:d,maxWorkers:h,terminateWorkerTimeout:m}=r;e.transferStreams=i||i===de;let w=!l&&!c&&!u&&!e.transferStreams;e.useWebWorkers=!w&&(s||s===de&&r.useWebWorkers),e.scripts=e.useWebWorkers&&d?d[o]:[],t.useCompressionStream=a||a===de&&r.useCompressionStream;let f,p=mt.find(b=>!b.busy);if(p)Kr(p),f=new Pr(p,n,e,g);else if(mt.length<h){let b={indexWorker:ua};ua++,mt.push(b),f=new Pr(b,n,e,g)}else f=await new Promise(b=>$r.push({resolve:b,stream:n,workerOptions:e}));return f.run();function g(b){if($r.length){let[{resolve:y,stream:E,workerOptions:_}]=$r.splice(0,1);y(new Pr(b,E,_,g))}else b.worker?(Kr(b),Number.isFinite(m)&&m>=0&&(b.terminateTimeout=setTimeout(()=>{mt=mt.filter(y=>y!=b),b.terminate()},m))):mt=mt.filter(y=>y!=b)}}function Kr(n){let{terminateTimeout:e}=n;e&&(clearTimeout(e),n.terminateTimeout=null)}function rd(){mt.forEach(n=>{Kr(n),n.terminate()})}var Xa="HTTP error ",nn="HTTP Range not supported",Ga="Writer iterator completed too soon",id="text/plain",sd="Content-Length",ad="Content-Range",od="Accept-Ranges",ld="Range",cd="Content-Type",ud="HEAD",ci="GET",Za="bytes",dd=64*1024,ui="writable",Qn=class{constructor(){this.size=0}init(){this.initialized=!0}},bt=class extends Qn{get readable(){let n=this,{chunkSize:e=dd}=n,t=new ReadableStream({start(){this.chunkOffset=0},async pull(r){let{offset:i=0,size:s,diskNumberStart:a}=t,{chunkOffset:o}=this;r.enqueue(await xe(n,i+o,Math.min(e,s-o),a)),o+e>s?r.close():this.chunkOffset+=e}});return t}},di=class extends Qn{constructor(){super();let n=this,e=new WritableStream({write(t){return n.writeUint8Array(t)}});Object.defineProperty(n,ui,{get(){return e}})}writeUint8Array(){}},hd=class extends bt{constructor(n){super();let e=n.length;for(;n.charAt(e-1)=="=";)e--;let t=n.indexOf(",")+1;Object.assign(this,{dataURI:n,dataStart:t,size:Math.floor((e-t)*.75)})}readUint8Array(n,e){let{dataStart:t,dataURI:r}=this,i=new Uint8Array(e),s=Math.floor(n/3)*4,a=atob(r.substring(s+t,Math.ceil((n+e)/3)*4+t)),o=n-Math.floor(s/4)*3;for(let l=o;l<o+e;l++)i[l-o]=a.charCodeAt(l);return i}},fd=class extends di{constructor(n){super(),Object.assign(this,{data:"data:"+(n||"")+";base64,",pending:[]})}writeUint8Array(n){let e=this,t=0,r=e.pending,i=e.pending.length;for(e.pending="",t=0;t<Math.floor((i+n.length)/3)*3-i;t++)r+=String.fromCharCode(n[t]);for(;t<n.length;t++)e.pending+=String.fromCharCode(n[t]);r.length>2?e.data+=btoa(r):e.pending=r}getData(){return this.data+btoa(this.pending)}},hi=class extends bt{constructor(n){super(),Object.assign(this,{blob:n,size:n.size})}async readUint8Array(n,e){let t=this,r=n+e,i=n||r<t.size?t.blob.slice(n,r):t.blob;return new Uint8Array(await i.arrayBuffer())}},fi=class extends Qn{constructor(n){super();let e=this,t=new TransformStream,r=[];n&&r.push([cd,n]),Object.defineProperty(e,ui,{get(){return t.writable}}),e.blob=new Response(t.readable,{headers:r}).blob()}getData(){return this.blob}},pd=class extends hi{constructor(n){super(new Blob([n],{type:id}))}},gd=class extends fi{constructor(n){super(n),Object.assign(this,{encoding:n,utf8:!n||n.toLowerCase()=="utf-8"})}async getData(){let{encoding:n,utf8:e}=this,t=await super.getData();if(t.text&&e)return t.text();{let r=new FileReader;return new Promise((i,s)=>{Object.assign(r,{onload:({target:a})=>i(a.result),onerror:()=>s(r.error)}),r.readAsText(t,n)})}}},md=class extends bt{constructor(n,e){super(),Ka(this,n,e)}async init(){await Ya(this,Yr,da),super.init()}readUint8Array(n,e){return Ja(this,n,e,Yr,da)}},wd=class extends bt{constructor(n,e){super(),Ka(this,n,e)}async init(){await Ya(this,Jr,ha),super.init()}readUint8Array(n,e){return Ja(this,n,e,Jr,ha)}};function Ka(n,e,t){let{preventHeadRequest:r,useRangeHeader:i,forceRangeRequests:s}=t;t=Object.assign({},t),delete t.preventHeadRequest,delete t.useRangeHeader,delete t.forceRangeRequests,delete t.useXHR,Object.assign(n,{url:e,options:t,preventHeadRequest:r,useRangeHeader:i,forceRangeRequests:s})}async function Ya(n,e,t){let{url:r,useRangeHeader:i,forceRangeRequests:s}=n;if(vd(r)&&(i||s)){let{headers:a}=await e(ci,n,Qa(n));if(!s&&a.get(od)!=Za)throw new Error(nn);{let o,l=a.get(ad);if(l){let c=l.trim().split(/\s*\/\s*/);if(c.length){let u=c[1];u&&u!="*"&&(o=Number(u))}}o===de?await fa(n,e,t):n.size=o}}else await fa(n,e,t)}async function Ja(n,e,t,r,i){let{useRangeHeader:s,forceRangeRequests:a,options:o}=n;if(s||a){let l=await r(ci,n,Qa(n,e,t));if(l.status!=206)throw new Error(nn);return new Uint8Array(await l.arrayBuffer())}else{let{data:l}=n;return l||await i(n,o),new Uint8Array(n.data.subarray(e,e+t))}}function Qa(n,e=0,t=1){return Object.assign({},pi(n),{[ld]:Za+"="+e+"-"+(e+t-1)})}function pi({options:n}){let{headers:e}=n;if(e)return Symbol.iterator in e?Object.fromEntries(e):e}async function da(n){await eo(n,Yr)}async function ha(n){await eo(n,Jr)}async function eo(n,e){let t=await e(ci,n,pi(n));n.data=new Uint8Array(await t.arrayBuffer()),n.size||(n.size=n.data.length)}async function fa(n,e,t){if(n.preventHeadRequest)await t(n,n.options);else{let r=(await e(ud,n,pi(n))).headers.get(sd);r?n.size=Number(r):await t(n,n.options)}}async function Yr(n,{options:e,url:t},r){let i=await fetch(t,Object.assign({},e,{method:n,headers:r}));if(i.status<400)return i;throw i.status==416?new Error(nn):new Error(Xa+(i.statusText||i.status))}function Jr(n,{url:e},t){return new Promise((r,i)=>{let s=new XMLHttpRequest;if(s.addEventListener("load",()=>{if(s.status<400){let a=[];s.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach(o=>{let l=o.trim().split(/\s*:\s*/);l[0]=l[0].trim().replace(/^[a-z]|-[a-z]/g,c=>c.toUpperCase()),a.push(l)}),r({status:s.status,arrayBuffer:()=>s.response,headers:new Map(a)})}else i(s.status==416?new Error(nn):new Error(Xa+(s.statusText||s.status)))},!1),s.addEventListener("error",a=>i(a.detail.error),!1),s.open(n,e),t)for(let a of Object.entries(t))s.setRequestHeader(a[0],a[1]);s.responseType="arraybuffer",s.send()})}var to=class extends bt{constructor(n,e={}){super(),Object.assign(this,{url:n,reader:e.useXHR?new wd(n,e):new md(n,e)})}set size(n){}get size(){return this.reader.size}async init(){await this.reader.init(),super.init()}readUint8Array(n,e){return this.reader.readUint8Array(n,e)}},bd=class extends to{constructor(n,e={}){e.useRangeHeader=!0,super(n,e)}},yd=class extends bt{constructor(n){super(),Object.assign(this,{array:n,size:n.length})}readUint8Array(n,e){return this.array.slice(n,n+e)}},xd=class extends di{init(n=0){Object.assign(this,{offset:0,array:new Uint8Array(n)}),super.init()}writeUint8Array(n){let e=this;if(e.offset+n.length>e.array.length){let t=e.array;e.array=new Uint8Array(t.length+n.length),e.array.set(t)}e.array.set(n,e.offset),e.offset+=n.length}getData(){return this.array}},gi=class extends bt{constructor(n){super(),this.readers=n}async init(){let n=this,{readers:e}=n;n.lastDiskNumber=0,await Promise.all(e.map(async t=>{await t.init(),n.size+=t.size})),super.init()}async readUint8Array(n,e,t=0){let r=this,{readers:i}=this,s,a=t;a==-1&&(a=i.length-1);let o=n;for(;o>=i[a].size;)o-=i[a].size,a++;let l=i[a],c=l.size;if(o+e<=c)s=await xe(l,o,e);else{let u=c-o;s=new Uint8Array(e),s.set(await xe(l,o,u)),s.set(await r.readUint8Array(n+u,e-u,t),u)}return r.lastDiskNumber=Math.max(a,r.lastDiskNumber),s}},en=class extends Qn{constructor(n,e=4294967295){super();let t=this;Object.assign(t,{diskNumber:0,diskOffset:0,size:0,maxSize:e,availableSize:e});let r,i,s,a=new WritableStream({async write(c){let{availableSize:u}=t;if(s)c.length>=u?(await o(c.slice(0,u)),await l(),t.diskOffset+=r.size,t.diskNumber++,s=null,await this.write(c.slice(u))):await o(c);else{let{value:d,done:h}=await n.next();if(h&&!d)throw new Error(Ga);r=d,r.size=0,r.maxSize&&(t.maxSize=r.maxSize),t.availableSize=t.maxSize,await rt(r),i=d.writable,s=i.getWriter(),await this.write(c)}},async close(){await s.ready,await l()}});Object.defineProperty(t,ui,{get(){return a}});async function o(c){let u=c.length;u&&(await s.ready,await s.write(c),r.size+=u,t.size+=u,t.availableSize-=u)}async function l(){i.size=r.size,await s.close()}}};function vd(n){let{baseURL:e}=ii(),{protocol:t}=new URL(n,e);return t=="http:"||t=="https:"}async function rt(n,e){n.init&&!n.initialized&&await n.init(e)}function mi(n){return Array.isArray(n)&&(n=new gi(n)),n instanceof ReadableStream&&(n={readable:n}),n}function wi(n){n.writable===de&&typeof n.next==Sa&&(n=new en(n)),n instanceof WritableStream&&(n={writable:n});let{writable:e}=n;return e.size===de&&(e.size=0),n instanceof en||Object.assign(n,{diskNumber:0,diskOffset:0,availableSize:1/0,maxSize:1/0}),n}function xe(n,e,t,r){return n.readUint8Array(e,t,r)}var _d=gi,Sd=en,no="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0 ".split(""),Ad=no.length==256;function Ed(n){if(Ad){let e="";for(let t=0;t<n.length;t++)e+=no[n[t]];return e}else return new TextDecoder().decode(n)}function Qr(n,e){return e&&e.trim().toLowerCase()=="cp437"?Ed(n):new TextDecoder(e).decode(n)}var ro="filename",io="rawFilename",so="comment",ao="rawComment",oo="uncompressedSize",lo="compressedSize",co="offset",ei="diskNumberStart",bi="lastModDate",uo="rawLastModDate",yi="lastAccessDate",kd="rawLastAccessDate",xi="creationDate",Td="rawCreationDate",ho="internalFileAttribute",fo="externalFileAttribute",po="msDosCompatible",go="zip64",Cd=[ro,io,lo,oo,bi,uo,so,ao,yi,xi,co,ei,ei,ho,fo,po,go,"directory","bitFlag","encrypted","signature","filenameUTF8","commentUTF8","compressionMethod","version","versionMadeBy","extraField","rawExtraField","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","extraFieldNTFS","extraFieldExtendedTimestamp"],Xn=class{constructor(n){Cd.forEach(e=>this[e]=n[e])}},$n="File format is not recognized",mo="End of central directory not found",wo="End of Zip64 central directory not found",bo="End of Zip64 central directory locator not found",yo="Central directory header not found",xo="Local file header not found",vo="Zip64 extra field not found",_o="File contains encrypted entry",So="Encryption method not supported",ti="Compression method not supported",ni="Split zip file",pa="utf-8",ga="cp437",Rd=[[oo,4294967295],[lo,4294967295],[co,4294967295],[ei,65535]],Id={[65535]:{getValue:le,bytes:4},[4294967295]:{getValue:Wn,bytes:8}},zd=class{constructor(n,e={}){Object.assign(this,{reader:mi(n),options:e,config:ii()})}async*getEntriesGenerator(n={}){let e=this,{reader:t}=e,{config:r}=e;if(await rt(t),(t.size===de||!t.readUint8Array)&&(t=new hi(await new Response(t.readable).blob()),await rt(t)),t.size<22)throw new Error($n);t.chunkSize=Ea(r);let i=await Ud(t,101010256,t.size,22,65535*16);if(!i){let v=await xe(t,0,4),k=ye(v);throw le(k)==134695760?new Error(ni):new Error(mo)}let s=ye(i),a=le(s,12),o=le(s,16),l=i.offset,c=pe(s,20),u=l+22+c,d=pe(s,4),h=t.lastDiskNumber||0,m=pe(s,6),w=pe(s,8),f=0,p=0;if(o==4294967295||a==4294967295||w==65535||m==65535){let v=await xe(t,i.offset-20,20),k=ye(v);if(le(k,0)!=117853008)throw new Error(wo);o=Wn(k,8);let A=await xe(t,o,56,-1),T=ye(A),D=i.offset-20-56;if(le(T,0)!=101075792&&o!=D){let R=o;o=D,f=o-R,A=await xe(t,o,56,-1),T=ye(A)}if(le(T,0)!=101075792)throw new Error(bo);d==65535&&(d=le(T,16)),m==65535&&(m=le(T,20)),w==65535&&(w=Wn(T,32)),a==4294967295&&(a=Wn(T,40)),o-=a}if(h!=d)throw new Error(ni);if(o<0||o>=t.size)throw new Error($n);let g=0,b=await xe(t,o,a,m),y=ye(b);if(a){let v=i.offset-a;if(le(y,g)!=33639248&&o!=v){let k=o;o=v,f=o-k,b=await xe(t,o,a,m),y=ye(b)}}if(o<0||o>=t.size)throw new Error($n);let E=Xe(e,n,"filenameEncoding"),_=Xe(e,n,"commentEncoding");for(let v=0;v<w;v++){let k=new Dd(t,r,e.options);if(le(y,g)!=33639248)throw new Error(yo);Ao(k,y,g+6);let A=!!k.bitFlag.languageEncodingFlag,T=g+46,D=T+k.filenameLength,R=D+k.extraFieldLength,U=pe(y,g+4),z=(U&0)==0,O=b.subarray(T,D),M=pe(y,g+32),P=R+M,Z=b.subarray(R,P),B=A,q=A,H=z&&(It(y,g+38)&16)==16,W=le(y,g+42)+f;Object.assign(k,{versionMadeBy:U,msDosCompatible:z,compressedSize:0,uncompressedSize:0,commentLength:M,directory:H,offset:W,diskNumberStart:pe(y,g+34),internalFileAttribute:pe(y,g+36),externalFileAttribute:le(y,g+38),rawFilename:O,filenameUTF8:B,commentUTF8:q,rawExtraField:b.subarray(D,R)});let[K,V]=await Promise.all([Qr(O,B?pa:E||ga),Qr(Z,q?pa:_||ga)]);Object.assign(k,{rawComment:Z,filename:K,comment:V,directory:H||K.endsWith(Hn)}),p=Math.max(W,p),await Eo(k,k,y,g+6);let Y=new Xn(k);Y.getData=(fe,Te)=>k.getData(fe,Y,Te),g=P;let{onprogress:he}=n;if(he)try{await he(v+1,w,new Xn(k))}catch{}yield Y}let x=Xe(e,n,"extractPrependedData"),S=Xe(e,n,"extractAppendedData");return x&&(e.prependedData=p>0?await xe(t,0,p):new Uint8Array),e.comment=c?await xe(t,l+22,c):new Uint8Array,S&&(e.appendedData=u<t.size?await xe(t,u,t.size-u):new Uint8Array),!0}async getEntries(n={}){let e=[];for await(let t of this.getEntriesGenerator(n))e.push(t);return e}async close(){}},Dd=class{constructor(n,e,t){Object.assign(this,{reader:n,config:e,options:t})}async getData(n,e,t={}){let r=this,{reader:i,offset:s,diskNumberStart:a,extraFieldAES:o,compressionMethod:l,config:c,bitFlag:u,signature:d,rawLastModDate:h,uncompressedSize:m,compressedSize:w}=r,f=r.localDirectory={},p=await xe(i,s,30,a),g=ye(p),b=Xe(r,t,"password");if(b=b&&b.length&&b,o&&o.originalCompressionMethod!=99)throw new Error(ti);if(l!=0&&l!=8)throw new Error(ti);if(le(g,0)!=67324752)throw new Error(xo);Ao(f,g,4),f.rawExtraField=f.extraFieldLength?await xe(i,s+30+f.filenameLength,f.extraFieldLength,a):new Uint8Array,await Eo(r,f,g,4),Object.assign(e,{lastAccessDate:f.lastAccessDate,creationDate:f.creationDate});let y=r.encrypted&&f.encrypted,E=y&&!o;if(y){if(!E&&o.strength===de)throw new Error(So);if(!b)throw new Error(_o)}let _=s+30+f.filenameLength+f.extraFieldLength,x=i.readable;x.diskNumberStart=a,x.offset=_;let S=x.size=w,v=Xe(r,t,"signal");n=wi(n),await rt(n,m);let{writable:k}=n,{onstart:A,onprogress:T,onend:D}=t,R={options:{codecType:Va,password:b,zipCrypto:E,encryptionStrength:o&&o.strength,signed:Xe(r,t,"checkSignature"),passwordVerification:E&&(u.dataDescriptor?h>>>8&255:d>>>24&255),signature:d,compressed:l!=0,encrypted:y,useWebWorkers:Xe(r,t,"useWebWorkers"),useCompressionStream:Xe(r,t,"useCompressionStream"),transferStreams:Xe(r,t,"transferStreams")},config:c,streamOptions:{signal:v,size:S,onstart:A,onprogress:T,onend:D}};return k.size+=(await qa({readable:x,writable:k},R)).size,Xe(r,t,"preventClose")||await k.close(),n.getData?n.getData():k}};function Ao(n,e,t){let r=n.rawBitFlag=pe(e,t+2),i=(r&1)==1,s=le(e,t+6);Object.assign(n,{encrypted:i,version:pe(e,t),bitFlag:{level:(r&6)>>1,dataDescriptor:(r&8)==8,languageEncodingFlag:(r&2048)==2048},rawLastModDate:s,lastModDate:Bd(s),filenameLength:pe(e,t+22),extraFieldLength:pe(e,t+24)})}async function Eo(n,e,t,r){let{rawExtraField:i}=e,s=e.extraField=new Map,a=ye(new Uint8Array(i)),o=0;try{for(;o<i.length;){let f=pe(a,o),p=pe(a,o+2);s.set(f,{type:f,data:i.slice(o+4,o+4+p)}),o+=4+p}}catch{}let l=pe(t,r+4);Object.assign(e,{signature:le(t,r+10),uncompressedSize:le(t,r+18),compressedSize:le(t,r+14)});let c=s.get(1);c&&(Ld(c,e),e.extraFieldZip64=c);let u=s.get(28789);u&&(await ma(u,ro,io,e,n),e.extraFieldUnicodePath=u);let d=s.get(25461);d&&(await ma(d,so,ao,e,n),e.extraFieldUnicodeComment=d);let h=s.get(39169);h?(Fd(h,e,l),e.extraFieldAES=h):e.compressionMethod=l;let m=s.get(10);m&&(Od(m,e),e.extraFieldNTFS=m);let w=s.get(21589);w&&(Md(w,e),e.extraFieldExtendedTimestamp=w)}function Ld(n,e){e.zip64=!0;let t=ye(n.data),r=Rd.filter(([i,s])=>e[i]==s);for(let i=0,s=0;i<r.length;i++){let[a,o]=r[i];if(e[a]==o){let l=Id[o];e[a]=n[a]=l.getValue(t,s),s+=l.bytes}else if(n[a])throw new Error(vo)}}async function ma(n,e,t,r,i){let s=ye(n.data),a=new Vn;a.append(i[t]);let o=ye(new Uint8Array(4));o.setUint32(0,a.get(),!0),Object.assign(n,{version:It(s,0),signature:le(s,1),[e]:await Qr(n.data.subarray(5)),valid:!i.bitFlag.languageEncodingFlag&&n.signature==le(o,0)}),n.valid&&(r[e]=n[e],r[e+"UTF8"]=!0)}function Fd(n,e,t){let r=ye(n.data),i=It(r,4);Object.assign(n,{vendorVersion:It(r,0),vendorId:It(r,2),strength:i,originalCompressionMethod:t,compressionMethod:pe(r,5)}),e.compressionMethod=n.compressionMethod}function Od(n,e){let t=ye(n.data),r=4,i;try{for(;r<n.data.length&&!i;){let s=pe(t,r),a=pe(t,r+2);s==1&&(i=n.data.slice(r+4,r+4+a)),r+=4+a}}catch{}try{if(i&&i.length==24){let s=ye(i),a=s.getBigUint64(0,!0),o=s.getBigUint64(8,!0),l=s.getBigUint64(16,!0);Object.assign(n,{rawLastModDate:a,rawLastAccessDate:o,rawCreationDate:l});let c=Wr(a),u=Wr(o),d=Wr(l),h={lastModDate:c,lastAccessDate:u,creationDate:d};Object.assign(n,h),Object.assign(e,h)}}catch{}}function Md(n,e){let t=ye(n.data),r=It(t,0),i=[],s=[];(r&1)==1&&(i.push(bi),s.push(uo)),(r&2)==2&&(i.push(yi),s.push(kd)),(r&4)==4&&(i.push(xi),s.push(Td));let a=1;i.forEach((o,l)=>{if(n.data.length>=a+4){let c=le(t,a);e[o]=n[o]=new Date(c*1e3);let u=s[l];n[u]=c}a+=4})}async function Ud(n,e,t,r,i){let s=new Uint8Array(4),a=ye(s);Nd(a,0,e);let o=r+i;return await l(r)||await l(Math.min(o,t));async function l(c){let u=t-c,d=await xe(n,u,c);for(let h=d.length-r;h>=0;h--)if(d[h]==s[0]&&d[h+1]==s[1]&&d[h+2]==s[2]&&d[h+3]==s[3])return{offset:u+h,buffer:d.slice(h,h+r).buffer}}}function Xe(n,e,t){return e[t]===de?n.options[t]:e[t]}function Bd(n){let e=(n&4294901760)>>16,t=n&65535;try{return new Date(1980+((e&65024)>>9),((e&480)>>5)-1,e&31,(t&63488)>>11,(t&2016)>>5,(t&31)*2,0)}catch{}}function Wr(n){return new Date(Number(n/BigInt(1e4)-BigInt(116444736e5)))}function It(n,e){return n.getUint8(e)}function pe(n,e){return n.getUint16(e,!0)}function le(n,e){return n.getUint32(e,!0)}function Wn(n,e){return Number(n.getBigUint64(e,!0))}function Nd(n,e,t){n.setUint32(e,t,!0)}function ye(n){return new DataView(n.buffer)}var ko="File already exists",To="Zip file comment exceeds 64KB",Co="File entry comment exceeds 64KB",Ro="File entry name exceeds 64KB",ri="Version exceeds 65535",Io="The strength must equal 1, 2, or 3",zo="Extra field type exceeds 65535",Do="Extra field data exceeds 64KB",er="Zip64 is not supported (make sure 'keepOrder' is set to 'true')",wa=new Uint8Array([7,0,2,0,65,69,3,0,0]),jr=0,ba=[],Pd=class{constructor(n,e={}){n=wi(n),Object.assign(this,{writer:n,addSplitZipSignature:n instanceof en,options:e,config:ii(),files:new Map,filenames:new Set,offset:n.writable.size,pendingEntriesSize:0,pendingAddFileCalls:new Set,bufferedWrites:0})}async add(n="",e,t={}){let r=this,{pendingAddFileCalls:i,config:s}=r;jr<s.maxWorkers?jr++:await new Promise(o=>ba.push(o));let a;try{if(n=n.trim(),r.filenames.has(n))throw new Error(ko);return r.filenames.add(n),a=Hd(r,n,e,t),i.add(a),await a}catch(o){throw r.filenames.delete(n),o}finally{i.delete(a);let o=ba.shift();o?o():jr--}}async close(n=new Uint8Array,e={}){let t=this,{pendingAddFileCalls:r,writer:i}=this,{writable:s}=i;for(;r.size;)await Promise.all(Array.from(r));return await Zd(this,n,e),ie(t,e,"preventClose")||await s.close(),i.getData?i.getData():s}};async function Hd(n,e,t,r){e=e.trim(),r.directory&&!e.endsWith(Hn)?e+=Hn:r.directory=e.endsWith(Hn);let i=Xr(e);if(J(i)>65535)throw new Error(Ro);let s=r.comment||"",a=Xr(s);if(J(a)>65535)throw new Error(Co);let o=ie(n,r,"version",20);if(o>65535)throw new Error(ri);let l=ie(n,r,"versionMadeBy",20);if(l>65535)throw new Error(ri);let c=ie(n,r,bi,new Date),u=ie(n,r,yi),d=ie(n,r,xi),h=ie(n,r,po,!0),m=ie(n,r,ho,0),w=ie(n,r,fo,0),f=ie(n,r,"password"),p=ie(n,r,"encryptionStrength",3),g=ie(n,r,"zipCrypto"),b=ie(n,r,"extendedTimestamp",!0),y=ie(n,r,"keepOrder",!0),E=ie(n,r,"level"),_=ie(n,r,"useWebWorkers"),x=ie(n,r,"bufferedWrite"),S=ie(n,r,"dataDescriptorSignature",!1),v=ie(n,r,"signal"),k=ie(n,r,"useCompressionStream"),A=ie(n,r,"dataDescriptor",!0),T=ie(n,r,go);if(f!==de&&p!==de&&(p<1||p>3))throw new Error(Io);let D=new Uint8Array,{extraField:R}=r;if(R){let fe=0,Te=0;R.forEach(oe=>fe+=4+J(oe)),D=new Uint8Array(fe),R.forEach((oe,ze)=>{if(ze>65535)throw new Error(zo);if(J(oe)>65535)throw new Error(Do);ge(D,new Uint16Array([ze]),Te),ge(D,new Uint16Array([J(oe)]),Te+2),ge(D,oe,Te+4),Te+=4+J(oe)})}let U=0,z=0,O=0,M=T===!0;t&&(t=mi(t),await rt(t),t.size===de?(A=!0,(T||T===de)&&(T=!0,U=4294967295)):(O=t.size,U=Yd(O)));let{diskOffset:P,diskNumber:Z,maxSize:B}=n.writer,q=M||O>=4294967295,H=M||U>=4294967295,W=M||n.offset+n.pendingEntriesSize-P>=4294967295,K=ie(n,r,"supportZip64SplitFile",!0)&&M||Z+Math.ceil(n.pendingEntriesSize/B)>=65535;if(W||q||H||K){if(T===!1||!y)throw new Error(er);T=!0}T=T||!1,r=Object.assign({},r,{rawFilename:i,rawComment:a,version:o,versionMadeBy:l,lastModDate:c,lastAccessDate:u,creationDate:d,rawExtraField:D,zip64:T,zip64UncompressedSize:q,zip64CompressedSize:H,zip64Offset:W,zip64DiskNumberStart:K,password:f,level:E,useWebWorkers:_,encryptionStrength:p,extendedTimestamp:b,zipCrypto:g,bufferedWrite:x,keepOrder:y,dataDescriptor:A,dataDescriptorSignature:S,signal:v,msDosCompatible:h,internalFileAttribute:m,externalFileAttribute:w,useCompressionStream:k});let V=jd(r),Y=Vd(r);z=J(V.localHeaderArray,Y.dataDescriptorArray)+U,n.pendingEntriesSize+=z;let he;try{he=await $d(n,e,t,{headerInfo:V,dataDescriptorInfo:Y},r)}finally{n.pendingEntriesSize-=z}return Object.assign(he,{name:e,comment:s,extraField:R}),new Xn(he)}async function $d(n,e,t,r,i){let{files:s,writer:a}=n,{keepOrder:o,dataDescriptor:l,signal:c}=i,{headerInfo:u}=r,d=Array.from(s.values()).pop(),h={},m,w,f,p,g,b;s.set(e,h);try{let x;o&&(x=d&&d.lock,y()),i.bufferedWrite||n.writerLocked||n.bufferedWrites&&o||!l?(b=new fi,b.writable.size=0,m=!0,n.bufferedWrites++,await rt(a)):(b=a,await E()),await rt(b);let{writable:S}=a,{diskOffset:v}=a;if(n.addSplitZipSignature){delete n.addSplitZipSignature;let A=new Uint8Array(4),T=Ie(A);X(T,0,134695760),await Ke(S,A),n.offset+=4}m||(await x,await _(S));let{diskNumber:k}=a;if(g=!0,h.diskNumberStart=k,h=await Wd(t,b,h,r,n.config,i),g=!1,s.set(e,h),h.filename=e,m){await b.writable.close();let A=await b.getData();await x,await E(),p=!0,l||(A=await Xd(h,A,S,i)),await _(S),h.diskNumberStart=a.diskNumber,v=a.diskOffset,await A.stream().pipeTo(S,{preventClose:!0,preventAbort:!0,signal:c}),S.size+=A.size,p=!1}if(h.offset=n.offset-v,h.zip64)Gd(h,i);else if(h.offset>=4294967295)throw new Error(er);return n.offset+=h.length,h}catch(x){if(m&&p||!m&&g){if(n.hasCorruptedEntries=!0,x)try{x.corruptedEntry=!0}catch{}m?n.offset+=b.writable.size:n.offset=b.writable.size}throw s.delete(e),x}finally{m&&n.bufferedWrites--,f&&f(),w&&w()}function y(){h.lock=new Promise(x=>f=x)}async function E(){n.writerLocked=!0;let{lockWriter:x}=n;n.lockWriter=new Promise(S=>w=()=>{n.writerLocked=!1,S()}),await x}async function _(x){u.localHeaderArray.length>a.availableSize&&(a.availableSize=0,await Ke(x,new Uint8Array))}}async function Wd(n,e,{diskNumberStart:t,lock:r},i,s,a){let{headerInfo:o,dataDescriptorInfo:l}=i,{localHeaderArray:c,headerArray:u,lastModDate:d,rawLastModDate:h,encrypted:m,compressed:w,version:f,compressionMethod:p,rawExtraFieldExtendedTimestamp:g,rawExtraFieldNTFS:b,rawExtraFieldAES:y}=o,{dataDescriptorArray:E}=l,{rawFilename:_,lastAccessDate:x,creationDate:S,password:v,level:k,zip64:A,zip64UncompressedSize:T,zip64CompressedSize:D,zip64Offset:R,zip64DiskNumberStart:U,zipCrypto:z,dataDescriptor:O,directory:M,versionMadeBy:P,rawComment:Z,rawExtraField:B,useWebWorkers:q,onstart:H,onprogress:W,onend:K,signal:V,encryptionStrength:Y,extendedTimestamp:he,msDosCompatible:fe,internalFileAttribute:Te,externalFileAttribute:oe,useCompressionStream:ze}=a,_e={lock:r,versionMadeBy:P,zip64:A,directory:!!M,filenameUTF8:!0,rawFilename:_,commentUTF8:!0,rawComment:Z,rawExtraFieldExtendedTimestamp:g,rawExtraFieldNTFS:b,rawExtraFieldAES:y,rawExtraField:B,extendedTimestamp:he,msDosCompatible:fe,internalFileAttribute:Te,externalFileAttribute:oe,diskNumberStart:t},Oe=0,Ce=0,ce,{writable:me}=e;if(n){n.chunkSize=Ea(s),await Ke(me,c);let $e=n.readable,ln=$e.size=n.size,dr={options:{codecType:ja,level:k,password:v,encryptionStrength:Y,zipCrypto:m&&z,passwordVerification:m&&z&&h>>8&255,signed:!0,compressed:w,encrypted:m,useWebWorkers:q,useCompressionStream:ze,transferStreams:!1},config:s,streamOptions:{signal:V,size:ln,onstart:H,onprogress:W,onend:K}},tt=await qa({readable:$e,writable:me},dr);me.size+=tt.size,ce=tt.signature,Ce=n.size=$e.size,Oe=tt.size}else await Ke(me,c);let yt;if(A){let $e=4;T&&($e+=8),D&&($e+=8),R&&($e+=8),U&&($e+=4),yt=new Uint8Array($e)}else yt=new Uint8Array;return n&&qd({signature:ce,rawExtraFieldZip64:yt,compressedSize:Oe,uncompressedSize:Ce,headerInfo:o,dataDescriptorInfo:l},a),O&&await Ke(me,E),Object.assign(_e,{uncompressedSize:Ce,compressedSize:Oe,lastModDate:d,rawLastModDate:h,creationDate:S,lastAccessDate:x,encrypted:m,length:J(c,E)+Oe,compressionMethod:p,version:f,headerArray:u,signature:ce,rawExtraFieldZip64:yt,zip64UncompressedSize:T,zip64CompressedSize:D,zip64Offset:R,zip64DiskNumberStart:U}),_e}function jd(n){let{rawFilename:e,lastModDate:t,lastAccessDate:r,creationDate:i,password:s,level:a,zip64:o,zipCrypto:l,dataDescriptor:c,directory:u,rawExtraField:d,encryptionStrength:h,extendedTimestamp:m}=n,w=a!==0&&!u,f=!!(s&&J(s)),p=n.version,g;if(f&&!l){g=new Uint8Array(J(wa)+2);let z=Ie(g);ee(z,0,39169),ge(g,wa,2),Gn(z,8,h)}else g=new Uint8Array;let b,y;if(m){y=new Uint8Array(9+(r?4:0)+(i?4:0));let z=Ie(y);ee(z,0,21589),ee(z,2,J(y)-4);let O=1+(r?2:0)+(i?4:0);Gn(z,4,O),X(z,5,Math.floor(t.getTime()/1e3)),r&&X(z,9,Math.floor(r.getTime()/1e3)),i&&X(z,13,Math.floor(i.getTime()/1e3));try{b=new Uint8Array(36);let M=Ie(b),P=Vr(t);ee(M,0,10),ee(M,2,32),ee(M,8,1),ee(M,10,24),Re(M,12,P),Re(M,20,Vr(r)||P),Re(M,28,Vr(i)||P)}catch{b=new Uint8Array}}else b=y=new Uint8Array;let E=2048;c&&(E=E|8);let _=0;w&&(_=8),o&&(p=p>45?p:45),f&&(E=E|1,l||(p=p>51?p:51,_=99,w&&(g[9]=8)));let x=new Uint8Array(26),S=Ie(x);ee(S,0,p),ee(S,2,E),ee(S,4,_);let v=new Uint32Array(1),k=Ie(v),A;t<Qs?A=Qs:t>Js?A=Js:A=t,ee(k,0,(A.getHours()<<6|A.getMinutes())<<5|A.getSeconds()/2),ee(k,2,(A.getFullYear()-1980<<4|A.getMonth()+1)<<5|A.getDate());let T=v[0];X(S,6,T),ee(S,22,J(e));let D=J(g,y,b,d);ee(S,24,D);let R=new Uint8Array(30+J(e)+D),U=Ie(R);return X(U,0,67324752),ge(R,x,4),ge(R,e,30),ge(R,g,30+J(e)),ge(R,y,30+J(e,g)),ge(R,b,30+J(e,g,y)),ge(R,d,30+J(e,g,y,b)),{localHeaderArray:R,headerArray:x,headerView:S,lastModDate:t,rawLastModDate:T,encrypted:f,compressed:w,version:p,compressionMethod:_,rawExtraFieldExtendedTimestamp:y,rawExtraFieldNTFS:b,rawExtraFieldAES:g}}function Vd(n){let{zip64:e,dataDescriptor:t,dataDescriptorSignature:r}=n,i=new Uint8Array,s,a=0;return t&&(i=new Uint8Array(e?r?24:20:r?16:12),s=Ie(i),r&&(a=4,X(s,0,134695760))),{dataDescriptorArray:i,dataDescriptorView:s,dataDescriptorOffset:a}}function qd(n,e){let{signature:t,rawExtraFieldZip64:r,compressedSize:i,uncompressedSize:s,headerInfo:a,dataDescriptorInfo:o}=n,{headerView:l,encrypted:c}=a,{dataDescriptorView:u,dataDescriptorOffset:d}=o,{zip64:h,zip64UncompressedSize:m,zip64CompressedSize:w,zipCrypto:f,dataDescriptor:p}=e;if((!c||f)&&t!==de&&(X(l,10,t),p&&X(u,d,t)),h){let g=Ie(r);ee(g,0,1),ee(g,2,r.length-4);let b=4;m&&(X(l,18,4294967295),Re(g,b,BigInt(s)),b+=8),w&&(X(l,14,4294967295),Re(g,b,BigInt(i))),p&&(Re(u,d+4,BigInt(i)),Re(u,d+12,BigInt(s)))}else X(l,14,i),X(l,18,s),p&&(X(u,d+4,i),X(u,d+8,s))}async function Xd(n,e,t,{zipCrypto:r}){let i=await Kd(e,0,26),s=new DataView(i);return(!n.encrypted||r)&&X(s,14,n.signature),n.zip64?(X(s,18,4294967295),X(s,22,4294967295)):(X(s,18,n.compressedSize),X(s,22,n.uncompressedSize)),await Ke(t,new Uint8Array(i)),e.slice(i.byteLength)}function Gd(n,e){let{rawExtraFieldZip64:t,offset:r,diskNumberStart:i}=n,{zip64UncompressedSize:s,zip64CompressedSize:a,zip64Offset:o,zip64DiskNumberStart:l}=e,c=Ie(t),u=4;s&&(u+=8),a&&(u+=8),o&&(Re(c,u,BigInt(r)),u+=8),l&&X(c,u,i)}async function Zd(n,e,t){let{files:r,writer:i}=n,{diskOffset:s,writable:a}=i,{diskNumber:o}=i,l=0,c=0,u=n.offset-s,d=r.size;for(let[,{rawFilename:_,rawExtraFieldZip64:x,rawExtraFieldAES:S,rawExtraField:v,rawComment:k,rawExtraFieldExtendedTimestamp:A,rawExtraFieldNTFS:T}]of r)c+=46+J(_,k,x,S,A,T,v);let h=new Uint8Array(c),m=Ie(h);await rt(i);let w=0;for(let[_,x]of Array.from(r.values()).entries()){let{offset:S,rawFilename:v,rawExtraFieldZip64:k,rawExtraFieldAES:A,rawExtraFieldNTFS:T,rawExtraField:D,rawComment:R,versionMadeBy:U,headerArray:z,directory:O,zip64:M,zip64UncompressedSize:P,zip64CompressedSize:Z,zip64DiskNumberStart:B,zip64Offset:q,msDosCompatible:H,internalFileAttribute:W,externalFileAttribute:K,extendedTimestamp:V,lastModDate:Y,diskNumberStart:he,uncompressedSize:fe,compressedSize:Te}=x,oe;if(V){oe=new Uint8Array(9);let Ce=Ie(oe);ee(Ce,0,21589),ee(Ce,2,J(oe)-4),Gn(Ce,4,1),X(Ce,5,Math.floor(Y.getTime()/1e3))}else oe=new Uint8Array;let ze=J(k,A,oe,T,D);X(m,l,33639248),ee(m,l+4,U);let _e=Ie(z);P||X(_e,18,fe),Z||X(_e,14,Te),ge(h,z,l+6),ee(m,l+30,ze),ee(m,l+32,J(R)),ee(m,l+34,M&&B?65535:he),ee(m,l+36,W),K?X(m,l+38,K):O&&H&&Gn(m,l+38,16),X(m,l+42,M&&q?4294967295:S),ge(h,v,l+46),ge(h,k,l+46+J(v)),ge(h,A,l+46+J(v,k)),ge(h,oe,l+46+J(v,k,A)),ge(h,T,l+46+J(v,k,A,oe)),ge(h,D,l+46+J(v,k,A,oe,T)),ge(h,R,l+46+J(v)+ze);let Oe=46+J(v,R)+ze;if(l-w>i.availableSize&&(i.availableSize=0,await Ke(a,h.slice(w,l)),w=l),l+=Oe,t.onprogress)try{await t.onprogress(_+1,r.size,new Xn(x))}catch{}}await Ke(a,w?h.slice(w):h);let f=i.diskNumber,{availableSize:p}=i;p<22&&f++;let g=ie(n,t,"zip64");if(u>=4294967295||c>=4294967295||d>=65535||f>=65535){if(g===!1)throw new Error(er);g=!0}let b=new Uint8Array(g?98:22),y=Ie(b);l=0,g&&(X(y,0,101075792),Re(y,4,BigInt(44)),ee(y,12,45),ee(y,14,45),X(y,16,f),X(y,20,o),Re(y,24,BigInt(d)),Re(y,32,BigInt(d)),Re(y,40,BigInt(c)),Re(y,48,BigInt(u)),X(y,56,117853008),Re(y,64,BigInt(u)+BigInt(c)),X(y,72,f+1),ie(n,t,"supportZip64SplitFile",!0)&&(f=65535,o=65535),d=65535,u=4294967295,c=4294967295,l+=56+20),X(y,l,101010256),ee(y,l+4,f),ee(y,l+6,o),ee(y,l+8,d),ee(y,l+10,d),X(y,l+12,c),X(y,l+16,u);let E=J(e);if(E)if(E<=65535)ee(y,l+20,E);else throw new Error(To);await Ke(a,b),E&&await Ke(a,e)}function Kd(n,e,t){return e||t?n.slice(e,t).arrayBuffer():n.arrayBuffer()}async function Ke(n,e){let t=n.getWriter();await t.ready,n.size+=J(e),await t.write(e),t.releaseLock()}function Vr(n){if(n)return(BigInt(n.getTime())+BigInt(116444736e5))*BigInt(1e4)}function ie(n,e,t,r){let i=e[t]===de?n.options[t]:e[t];return i===de?r:i}function Yd(n){return n+5*(Math.floor(n/16383)+1)}function Gn(n,e,t){n.setUint8(e,t)}function ee(n,e,t){n.setUint16(e,t,!0)}function X(n,e,t){n.setUint32(e,t,!0)}function Re(n,e,t){n.setBigUint64(e,t,!0)}function ge(n,e,t){n.set(e,t)}function Ie(n){return new DataView(n.buffer)}function J(...n){let e=0;return n.forEach(t=>t&&(e+=t.length)),e}Yn({Deflate:Yc,Inflate:mu});var Lo={},Jd=function(n,e,t,r,i){var s=new Worker(Lo[e]||(Lo[e]=URL.createObjectURL(new Blob([n+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return s.onmessage=function(a){var o=a.data,l=o.$e$;if(l){var c=new Error(l[0]);c.code=l[1],c.stack=l[2],i(c,null)}else i(null,o)},s.postMessage(t,r),s},te=Uint8Array,ve=Uint16Array,Mt=Uint32Array,Ut=new te([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Bt=new te([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),rn=new te([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),No=function(n,e){for(var t=new ve(31),r=0;r<31;++r)t[r]=e+=1<<n[r-1];for(var i=new Mt(t[30]),r=1;r<30;++r)for(var s=t[r];s<t[r+1];++s)i[s]=s-t[r]<<5|r;return[t,i]},Po=No(Ut,2),Ii=Po[0],ar=Po[1];Ii[28]=258,ar[258]=28;var Ho=No(Bt,0),$o=Ho[0],Si=Ho[1],sn=new ve(32768);for(Q=0;Q<32768;++Q)it=(Q&43690)>>>1|(Q&21845)<<1,it=(it&52428)>>>2|(it&13107)<<2,it=(it&61680)>>>4|(it&3855)<<4,sn[Q]=((it&65280)>>>8|(it&255)<<8)>>>1;var it,Q,Pe=function(n,e,t){for(var r=n.length,i=0,s=new ve(e);i<r;++i)n[i]&&++s[n[i]-1];var a=new ve(e);for(i=0;i<e;++i)a[i]=a[i-1]+s[i-1]<<1;var o;if(t){o=new ve(1<<e);var l=15-e;for(i=0;i<r;++i)if(n[i])for(var c=i<<4|n[i],u=e-n[i],d=a[n[i]-1]++<<u,h=d|(1<<u)-1;d<=h;++d)o[sn[d]>>>l]=c}else for(o=new ve(r),i=0;i<r;++i)n[i]&&(o[i]=sn[a[n[i]-1]++]>>>15-n[i]);return o},at=new te(288);for(Q=0;Q<144;++Q)at[Q]=8;var Q;for(Q=144;Q<256;++Q)at[Q]=9;var Q;for(Q=256;Q<280;++Q)at[Q]=7;var Q;for(Q=280;Q<288;++Q)at[Q]=8;var Q,Ot=new te(32);for(Q=0;Q<32;++Q)Ot[Q]=5;var Q,Wo=Pe(at,9,0),jo=Pe(at,9,1),Vo=Pe(Ot,5,0),qo=Pe(Ot,5,1),nr=function(n){for(var e=n[0],t=1;t<n.length;++t)n[t]>e&&(e=n[t]);return e},Ne=function(n,e,t){var r=e/8|0;return(n[r]|n[r+1]<<8)>>(e&7)&t},rr=function(n,e){var t=e/8|0;return(n[t]|n[t+1]<<8|n[t+2]<<16)>>(e&7)},an=function(n){return(n+7)/8|0},et=function(n,e,t){(e==null||e<0)&&(e=0),(t==null||t>n.length)&&(t=n.length);var r=new(n.BYTES_PER_ELEMENT==2?ve:n.BYTES_PER_ELEMENT==4?Mt:te)(t-e);return r.set(n.subarray(e,t)),r};var Xo=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],j=function(n,e,t){var r=new Error(e||Xo[n]);if(r.code=n,Error.captureStackTrace&&Error.captureStackTrace(r,j),!t)throw r;return r},ur=function(n,e,t){var r=n.length;if(!r||t&&t.f&&!t.l)return e||new te(0);var i=!e||t,s=!t||t.i;t||(t={}),e||(e=new te(r*3));var a=function(Y){var he=e.length;if(Y>he){var fe=new te(Math.max(he*2,Y));fe.set(e),e=fe}},o=t.f||0,l=t.p||0,c=t.b||0,u=t.l,d=t.d,h=t.m,m=t.n,w=r*8;do{if(!u){o=Ne(n,l,1);var f=Ne(n,l+1,3);if(l+=3,f)if(f==1)u=jo,d=qo,h=9,m=5;else if(f==2){var p=Ne(n,l,31)+257,g=Ne(n,l+10,15)+4,b=p+Ne(n,l+5,31)+1;l+=14;for(var y=new te(b),E=new te(19),_=0;_<g;++_)E[rn[_]]=Ne(n,l+_*3,7);l+=g*3;for(var x=nr(E),S=(1<<x)-1,v=Pe(E,x,1),_=0;_<b;){var k=v[Ne(n,l,S)];l+=k&15;var A=k>>>4;if(A<16)y[_++]=A;else{var T=0,D=0;for(A==16?(D=3+Ne(n,l,3),l+=2,T=y[_-1]):A==17?(D=3+Ne(n,l,7),l+=3):A==18&&(D=11+Ne(n,l,127),l+=7);D--;)y[_++]=T}}var R=y.subarray(0,p),U=y.subarray(p);h=nr(R),m=nr(U),u=Pe(R,h,1),d=Pe(U,m,1)}else j(1);else{var A=an(l)+4,z=n[A-4]|n[A-3]<<8,O=A+z;if(O>r){s&&j(0);break}i&&a(c+z),e.set(n.subarray(A,O),c),t.b=c+=z,t.p=l=O*8,t.f=o;continue}if(l>w){s&&j(0);break}}i&&a(c+131072);for(var M=(1<<h)-1,P=(1<<m)-1,Z=l;;Z=l){var T=u[rr(n,l)&M],B=T>>>4;if(l+=T&15,l>w){s&&j(0);break}if(T||j(2),B<256)e[c++]=B;else if(B==256){Z=l,u=null;break}else{var q=B-254;if(B>264){var _=B-257,H=Ut[_];q=Ne(n,l,(1<<H)-1)+Ii[_],l+=H}var W=d[rr(n,l)&P],K=W>>>4;W||j(3),l+=W&15;var U=$o[K];if(K>3){var H=Bt[K];U+=rr(n,l)&(1<<H)-1,l+=H}if(l>w){s&&j(0);break}i&&a(c+131072);for(var V=c+q;c<V;c+=4)e[c]=e[c-U],e[c+1]=e[c+1-U],e[c+2]=e[c+2-U],e[c+3]=e[c+3-U];c=V}}t.l=u,t.p=Z,t.b=c,t.f=o,u&&(o=1,t.m=h,t.d=d,t.n=m)}while(!o);return c==e.length?e:et(e,0,c)},Qe=function(n,e,t){t<<=e&7;var r=e/8|0;n[r]|=t,n[r+1]|=t>>>8},zt=function(n,e,t){t<<=e&7;var r=e/8|0;n[r]|=t,n[r+1]|=t>>>8,n[r+2]|=t>>>16},ir=function(n,e){for(var t=[],r=0;r<n.length;++r)n[r]&&t.push({s:r,f:n[r]});var i=t.length,s=t.slice();if(!i)return[st,0];if(i==1){var a=new te(t[0].s+1);return a[t[0].s]=1,[a,1]}t.sort(function(_,x){return _.f-x.f}),t.push({s:-1,f:25001});var o=t[0],l=t[1],c=0,u=1,d=2;for(t[0]={s:-1,f:o.f+l.f,l:o,r:l};u!=i-1;)o=t[t[c].f<t[d].f?c++:d++],l=t[c!=u&&t[c].f<t[d].f?c++:d++],t[u++]={s:-1,f:o.f+l.f,l:o,r:l};for(var h=s[0].s,r=1;r<i;++r)s[r].s>h&&(h=s[r].s);var m=new ve(h+1),w=or(t[u-1],m,0);if(w>e){var r=0,f=0,p=w-e,g=1<<p;for(s.sort(function(x,S){return m[S.s]-m[x.s]||x.f-S.f});r<i;++r){var b=s[r].s;if(m[b]>e)f+=g-(1<<w-m[b]),m[b]=e;else break}for(f>>>=p;f>0;){var y=s[r].s;m[y]<e?f-=1<<e-m[y]++-1:++r}for(;r>=0&&f;--r){var E=s[r].s;m[E]==e&&(--m[E],++f)}w=e}return[new te(m),w]},or=function(n,e,t){return n.s==-1?Math.max(or(n.l,e,t+1),or(n.r,e,t+1)):e[n.s]=t},Ai=function(n){for(var e=n.length;e&&!n[--e];);for(var t=new ve(++e),r=0,i=n[0],s=1,a=function(l){t[r++]=l},o=1;o<=e;++o)if(n[o]==i&&o!=e)++s;else{if(!i&&s>2){for(;s>138;s-=138)a(32754);s>2&&(a(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(a(i),--s;s>6;s-=6)a(8304);s>2&&(a(s-3<<5|8208),s=0)}for(;s--;)a(i);s=1,i=n[o]}return[t.subarray(0,r),e]},Dt=function(n,e){for(var t=0,r=0;r<e.length;++r)t+=n[r]*e[r];return t},lr=function(n,e,t){var r=t.length,i=an(e+2);n[i]=r&255,n[i+1]=r>>>8,n[i+2]=n[i]^255,n[i+3]=n[i+1]^255;for(var s=0;s<r;++s)n[i+s+4]=t[s];return(i+4+r)*8},Ei=function(n,e,t,r,i,s,a,o,l,c,u){Qe(e,u++,t),++i[256];for(var d=ir(i,15),h=d[0],m=d[1],w=ir(s,15),f=w[0],p=w[1],g=Ai(h),b=g[0],y=g[1],E=Ai(f),_=E[0],x=E[1],S=new ve(19),v=0;v<b.length;++v)S[b[v]&31]++;for(var v=0;v<_.length;++v)S[_[v]&31]++;for(var k=ir(S,7),A=k[0],T=k[1],D=19;D>4&&!A[rn[D-1]];--D);var R=c+5<<3,U=Dt(i,at)+Dt(s,Ot)+a,z=Dt(i,h)+Dt(s,f)+a+14+3*D+Dt(S,A)+(2*S[16]+3*S[17]+7*S[18]);if(R<=U&&R<=z)return lr(e,u,n.subarray(l,l+c));var O,M,P,Z;if(Qe(e,u,1+(z<U)),u+=2,z<U){O=Pe(h,m,0),M=h,P=Pe(f,p,0),Z=f;var B=Pe(A,T,0);Qe(e,u,y-257),Qe(e,u+5,x-1),Qe(e,u+10,D-4),u+=14;for(var v=0;v<D;++v)Qe(e,u+3*v,A[rn[v]]);u+=3*D;for(var q=[b,_],H=0;H<2;++H)for(var W=q[H],v=0;v<W.length;++v){var K=W[v]&31;Qe(e,u,B[K]),u+=A[K],K>15&&(Qe(e,u,W[v]>>>5&127),u+=W[v]>>>12)}}else O=Wo,M=at,P=Vo,Z=Ot;for(var v=0;v<o;++v)if(r[v]>255){var K=r[v]>>>18&31;zt(e,u,O[K+257]),u+=M[K+257],K>7&&(Qe(e,u,r[v]>>>23&31),u+=Ut[K]);var V=r[v]&31;zt(e,u,P[V]),u+=Z[V],V>3&&(zt(e,u,r[v]>>>5&8191),u+=Bt[V])}else zt(e,u,O[r[v]]),u+=M[r[v]];return zt(e,u,O[256]),u+M[256]},Go=new Mt([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),st=new te(0),Zo=function(n,e,t,r,i,s){var a=n.length,o=new te(r+a+5*(1+Math.ceil(a/7e3))+i),l=o.subarray(r,o.length-i),c=0;if(!e||a<8)for(var u=0;u<=a;u+=65535){var d=u+65535;d>=a&&(l[c>>3]=s),c=lr(l,c+1,n.subarray(u,d))}else{for(var h=Go[e-1],m=h>>>13,w=h&8191,f=(1<<t)-1,p=new ve(32768),g=new ve(f+1),b=Math.ceil(t/3),y=2*b,E=function(Ce){return(n[Ce]^n[Ce+1]<<b^n[Ce+2]<<y)&f},_=new Mt(25e3),x=new ve(288),S=new ve(32),v=0,k=0,u=0,A=0,T=0,D=0;u<a;++u){var R=E(u),U=u&32767,z=g[R];if(p[U]=z,g[R]=U,T<=u){var O=a-u;if((v>7e3||A>24576)&&O>423){c=Ei(n,l,0,_,x,S,k,A,D,u-D,c),A=v=k=0,D=u;for(var M=0;M<286;++M)x[M]=0;for(var M=0;M<30;++M)S[M]=0}var P=2,Z=0,B=w,q=U-z&32767;if(O>2&&R==E(u-q))for(var H=Math.min(m,O)-1,W=Math.min(32767,u),K=Math.min(258,O);q<=W&&--B&&U!=z;){if(n[u+P]==n[u+P-q]){for(var V=0;V<K&&n[u+V]==n[u+V-q];++V);if(V>P){if(P=V,Z=q,V>H)break;for(var Y=Math.min(q,V-2),he=0,M=0;M<Y;++M){var fe=u-q+M+32768&32767,Te=p[fe],oe=fe-Te+32768&32767;oe>he&&(he=oe,z=fe)}}}U=z,z=p[U],q+=U-z+32768&32767}if(Z){_[A++]=268435456|ar[P]<<18|Si[Z];var ze=ar[P]&31,_e=Si[Z]&31;k+=Ut[ze]+Bt[_e],++x[257+ze],++S[_e],T=u+P,++v}else _[A++]=n[u],++x[n[u]]}}c=Ei(n,l,s,_,x,S,k,A,D,u-D,c),!s&&c&7&&(c=lr(l,c+1,st))}return et(o,0,r+an(c)+i)},Ko=function(){for(var n=new Int32Array(256),e=0;e<256;++e){for(var t=e,r=9;--r;)t=(t&1&&-306674912)^t>>>1;n[e]=t}return n}(),zi=function(){var n=-1;return{p:function(e){for(var t=n,r=0;r<e.length;++r)t=Ko[t&255^e[r]]^t>>>8;n=t},d:function(){return~n}}},Yo=function(){var n=1,e=0;return{p:function(t){for(var r=n,i=e,s=t.length|0,a=0;a!=s;){for(var o=Math.min(a+2655,s);a<o;++a)i+=r+=t[a];r=(r&65535)+15*(r>>16),i=(i&65535)+15*(i>>16)}n=r,e=i},d:function(){return n%=65521,e%=65521,(n&255)<<24|n>>>8<<16|(e&255)<<8|e>>>8}}},on=function(n,e,t,r,i){return Zo(n,e.level==null?6:e.level,e.mem==null?Math.ceil(Math.max(8,Math.min(13,Math.log(n.length)))*1.5):12+e.mem,t,r,!i)},Jo=function(n,e){var t={};for(var r in n)t[r]=n[r];for(var r in e)t[r]=e[r];return t},Fo=function(n,e,t){for(var r=n(),i=n.toString(),s=i.slice(i.indexOf("[")+1,i.lastIndexOf("]")).replace(/\s+/g,"").split(","),a=0;a<r.length;++a){var o=r[a],l=s[a];if(typeof o=="function"){e+=";"+l+"=";var c=o.toString();if(o.prototype)if(c.indexOf("[native code]")!=-1){var u=c.indexOf(" ",8)+1;e+=c.slice(u,c.indexOf("(",u))}else{e+=c;for(var d in o.prototype)e+=";"+l+".prototype."+d+"="+o.prototype[d].toString()}else e+=c}else t[l]=o}return[e,t]},tr=[],Qd=function(n){var e=[];for(var t in n)n[t].buffer&&e.push((n[t]=new n[t].constructor(n[t])).buffer);return e},eh=function(n,e,t,r){var i;if(!tr[t]){for(var s="",a={},o=n.length-1,l=0;l<o;++l)i=Fo(n[l],s,a),s=i[0],a=i[1];tr[t]=Fo(n[o],s,a)}var c=Jo({},tr[t][1]);return Jd(tr[t][0]+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+e.toString()+"}",t,c,Qd(c),r)},Di=function(){return[te,ve,Mt,Ut,Bt,rn,Ii,$o,jo,qo,sn,Xo,Pe,nr,Ne,rr,an,et,j,ur,ch,Qo,sh]},Li=function(){return[te,ve,Mt,Ut,Bt,rn,ar,Si,Wo,at,Vo,Ot,sn,Go,st,Pe,Qe,zt,ir,or,Ai,Dt,lr,Ei,an,et,Zo,on,lh,Qo]},th=function(){return[el,nl,ne,zi,Ko]},nh=function(){return[tl,ah]},rh=function(){return[rl,ne,Yo]},ih=function(){return[il]},Qo=function(n){return postMessage(n,[n.buffer])},sh=function(n){return n&&n.size&&new te(n.size)};var He=function(n){return n.ondata=function(e,t){return postMessage([e,t],[e.buffer])},function(e){return n.push(e.data[0],e.data[1])}},Nt=function(n,e,t,r,i){var s,a=eh(n,r,i,function(o,l){o?(a.terminate(),e.ondata.call(e,o)):(l[1]&&a.terminate(),e.ondata.call(e,o,l[0],l[1]))});a.postMessage(t),e.push=function(o,l){e.ondata||j(5),s&&e.ondata(j(4,0,1),null,!!l),a.postMessage([o,s=l],[o.buffer])},e.terminate=function(){a.terminate()}},Lt=function(n,e){return n[e]|n[e+1]<<8},Ft=function(n,e){return(n[e]|n[e+1]<<8|n[e+2]<<16|n[e+3]<<24)>>>0},_i=function(n,e){return Ft(n,e)+Ft(n,e+4)*4294967296},ne=function(n,e,t){for(;t;++e)n[e]=t,t>>>=8},el=function(n,e){var t=e.filename;if(n[0]=31,n[1]=139,n[2]=8,n[8]=e.level<2?4:e.level==9?2:0,n[9]=3,e.mtime!=0&&ne(n,4,Math.floor(new Date(e.mtime||Date.now())/1e3)),t){n[3]=8;for(var r=0;r<=t.length;++r)n[r+10]=t.charCodeAt(r)}},tl=function(n){(n[0]!=31||n[1]!=139||n[2]!=8)&&j(6,"invalid gzip data");var e=n[3],t=10;e&4&&(t+=n[10]|(n[11]<<8)+2);for(var r=(e>>3&1)+(e>>4&1);r>0;r-=!n[t++]);return t+(e&2)},ah=function(n){var e=n.length;return(n[e-4]|n[e-3]<<8|n[e-2]<<16|n[e-1]<<24)>>>0},nl=function(n){return 10+(n.filename&&n.filename.length+1||0)},rl=function(n,e){var t=e.level,r=t==0?0:t<6?1:t==9?3:2;n[0]=120,n[1]=r<<6|(r?32-2*r:1)},il=function(n){((n[0]&15)!=8||n[0]>>>4>7||(n[0]<<8|n[1])%31)&&j(6,"invalid zlib data"),n[1]&32&&j(6,"invalid zlib data: preset dictionaries not supported")};function Fi(n,e){return!e&&typeof n=="function"&&(e=n,n={}),this.ondata=e,n}var ot=function(){function n(e,t){!t&&typeof e=="function"&&(t=e,e={}),this.ondata=t,this.o=e||{}}return n.prototype.p=function(e,t){this.ondata(on(e,this.o,0,0,!t),t)},n.prototype.push=function(e,t){this.ondata||j(5),this.d&&j(4),this.d=t,this.p(e,t||!1)},n}(),oh=function(){function n(e,t){Nt([Li,function(){return[He,ot]}],this,Fi.call(this,e,t),function(r){var i=new ot(r.data);onmessage=He(i)},6)}return n}();function lh(n,e){return on(n,e||{},0,0)}var Fe=function(){function n(e){this.s={},this.p=new te(0),this.ondata=e}return n.prototype.e=function(e){this.ondata||j(5),this.d&&j(4);var t=this.p.length,r=new te(t+e.length);r.set(this.p),r.set(e,t),this.p=r},n.prototype.c=function(e){this.d=this.s.i=e||!1;var t=this.s.b,r=ur(this.p,this.o,this.s);this.ondata(et(r,t,this.s.b),this.d),this.o=et(r,this.s.b-32768),this.s.b=this.o.length,this.p=et(this.p,this.s.p/8|0),this.s.p&=7},n.prototype.push=function(e,t){this.e(e),this.c(t)},n}(),sl=function(){function n(e){this.ondata=e,Nt([Di,function(){return[He,Fe]}],this,0,function(){var t=new Fe;onmessage=He(t)},7)}return n}();function ch(n,e){return ur(n,e)}var Oo=function(){function n(e,t){this.c=zi(),this.l=0,this.v=1,ot.call(this,e,t)}return n.prototype.push=function(e,t){ot.prototype.push.call(this,e,t)},n.prototype.p=function(e,t){this.c.p(e),this.l+=e.length;var r=on(e,this.o,this.v&&nl(this.o),t&&8,!t);this.v&&(el(r,this.o),this.v=0),t&&(ne(r,r.length-8,this.c.d()),ne(r,r.length-4,this.l)),this.ondata(r,t)},n}(),s0=function(){function n(e,t){Nt([Li,th,function(){return[He,ot,Oo]}],this,Fi.call(this,e,t),function(r){var i=new Oo(r.data);onmessage=He(i)},8)}return n}();var ki=function(){function n(e){this.v=1,Fe.call(this,e)}return n.prototype.push=function(e,t){if(Fe.prototype.e.call(this,e),this.v){var r=this.p.length>3?tl(this.p):4;if(r>=this.p.length&&!t)return;this.p=this.p.subarray(r),this.v=0}t&&(this.p.length<8&&j(6,"invalid gzip data"),this.p=this.p.subarray(0,-8)),Fe.prototype.c.call(this,t)},n}(),uh=function(){function n(e){this.ondata=e,Nt([Di,nh,function(){return[He,Fe,ki]}],this,0,function(){var t=new ki;onmessage=He(t)},9)}return n}();var Mo=function(){function n(e,t){this.c=Yo(),this.v=1,ot.call(this,e,t)}return n.prototype.push=function(e,t){ot.prototype.push.call(this,e,t)},n.prototype.p=function(e,t){this.c.p(e);var r=on(e,this.o,this.v&&2,t&&4,!t);this.v&&(rl(r,this.o),this.v=0),t&&ne(r,r.length-4,this.c.d()),this.ondata(r,t)},n}(),a0=function(){function n(e,t){Nt([Li,rh,function(){return[He,ot,Mo]}],this,Fi.call(this,e,t),function(r){var i=new Mo(r.data);onmessage=He(i)},10)}return n}();var Ti=function(){function n(e){this.v=1,Fe.call(this,e)}return n.prototype.push=function(e,t){if(Fe.prototype.e.call(this,e),this.v){if(this.p.length<2&&!t)return;this.p=this.p.subarray(2),this.v=0}t&&(this.p.length<4&&j(6,"invalid zlib data"),this.p=this.p.subarray(0,-4)),Fe.prototype.c.call(this,t)},n}(),dh=function(){function n(e){this.ondata=e,Nt([Di,ih,function(){return[He,Fe,Ti]}],this,0,function(){var t=new Ti;onmessage=He(t)},11)}return n}();function al(n,e){return ur((il(n),n.subarray(2,-4)),e)}var hh=function(){function n(e){this.G=ki,this.I=Fe,this.Z=Ti,this.ondata=e}return n.prototype.push=function(e,t){if(this.ondata||j(5),this.s)this.s.push(e,t);else{if(this.p&&this.p.length){var r=new te(this.p.length+e.length);r.set(this.p),r.set(e,this.p.length)}else this.p=e;if(this.p.length>2){var i=this,s=function(){i.ondata.apply(i,arguments)};this.s=this.p[0]==31&&this.p[1]==139&&this.p[2]==8?new this.G(s):(this.p[0]&15)!=8||this.p[0]>>4>7||(this.p[0]<<8|this.p[1])%31?new this.I(s):new this.Z(s),this.s.push(this.p,t),this.p=null}}},n}(),o0=function(){function n(e){this.G=uh,this.I=sl,this.Z=dh,this.ondata=e}return n.prototype.push=function(e,t){hh.prototype.push.call(this,e,t)},n}();var Uo=typeof TextEncoder<"u"&&new TextEncoder,Ci=typeof TextDecoder<"u"&&new TextDecoder,ol=0;try{Ci.decode(st,{stream:!0}),ol=1}catch{}var ll=function(n){for(var e="",t=0;;){var r=n[t++],i=(r>127)+(r>223)+(r>239);if(t+i>n.length)return[e,et(n,t-1)];i?i==3?(r=((r&15)<<18|(n[t++]&63)<<12|(n[t++]&63)<<6|n[t++]&63)-65536,e+=String.fromCharCode(55296|r>>10,56320|r&1023)):i&1?e+=String.fromCharCode((r&31)<<6|n[t++]&63):e+=String.fromCharCode((r&15)<<12|(n[t++]&63)<<6|n[t++]&63):e+=String.fromCharCode(r)}},l0=function(){function n(e){this.ondata=e,ol?this.t=new TextDecoder:this.p=st}return n.prototype.push=function(e,t){if(this.ondata||j(5),t=!!t,this.t){this.ondata(this.t.decode(e,{stream:!0}),t),t&&(this.t.decode().length&&j(8),this.t=null);return}this.p||j(4);var r=new te(this.p.length+e.length);r.set(this.p),r.set(e,this.p.length);var i=ll(r),s=i[0],a=i[1];t?(a.length&&j(8),this.p=null):this.p=a,this.ondata(s,t)},n}(),c0=function(){function n(e){this.ondata=e}return n.prototype.push=function(e,t){this.ondata||j(5),this.d&&j(4),this.ondata(Ri(e),this.d=t||!1)},n}();function Ri(n,e){if(e){for(var t=new te(n.length),r=0;r<n.length;++r)t[r]=n.charCodeAt(r);return t}if(Uo)return Uo.encode(n);for(var i=n.length,s=new te(n.length+(n.length>>1)),a=0,o=function(d){s[a++]=d},r=0;r<i;++r){if(a+5>s.length){var l=new te(a+8+(i-r<<1));l.set(s),s=l}var c=n.charCodeAt(r);c<128||e?o(c):c<2048?(o(192|c>>6),o(128|c&63)):c>55295&&c<57344?(c=65536+(c&1047552)|n.charCodeAt(++r)&1023,o(240|c>>18),o(128|c>>12&63),o(128|c>>6&63),o(128|c&63)):(o(224|c>>12),o(128|c>>6&63),o(128|c&63))}return et(s,0,a)}function fh(n,e){if(e){for(var t="",r=0;r<n.length;r+=16384)t+=String.fromCharCode.apply(null,n.subarray(r,r+16384));return t}else{if(Ci)return Ci.decode(n);var i=ll(n),s=i[0],a=i[1];return a.length&&j(8),s}}var cl=function(n){return n==1?3:n<6?2:n==9?1:0};var ph=function(n,e){for(;Lt(n,e)!=1;e+=4+Lt(n,e+2));return[_i(n,e+12),_i(n,e+4),_i(n,e+20)]},sr=function(n){var e=0;if(n)for(var t in n){var r=n[t].length;r>65535&&j(9),e+=r+4}return e},Bo=function(n,e,t,r,i,s,a,o){var l=r.length,c=t.extra,u=o&&o.length,d=sr(c);ne(n,e,a!=null?33639248:67324752),e+=4,a!=null&&(n[e++]=20,n[e++]=t.os),n[e]=20,e+=2,n[e++]=t.flag<<1|(s<0&&8),n[e++]=i&&8,n[e++]=t.compression&255,n[e++]=t.compression>>8;var h=new Date(t.mtime==null?Date.now():t.mtime),m=h.getFullYear()-1980;if((m<0||m>119)&&j(10),ne(n,e,m<<25|h.getMonth()+1<<21|h.getDate()<<16|h.getHours()<<11|h.getMinutes()<<5|h.getSeconds()>>>1),e+=4,s!=-1&&(ne(n,e,t.crc),ne(n,e+4,s<0?-s-2:s),ne(n,e+8,t.size)),ne(n,e+12,l),ne(n,e+14,d),e+=16,a!=null&&(ne(n,e,u),ne(n,e+6,t.attrs),ne(n,e+10,a),e+=14),n.set(r,e),e+=l,d)for(var w in c){var f=c[w],p=f.length;ne(n,e,+w),ne(n,e+2,p),n.set(f,e+4),e+=4+p}return u&&(n.set(o,e),e+=u),e},gh=function(n,e,t,r,i){ne(n,e,101010256),ne(n,e+8,t),ne(n,e+10,t),ne(n,e+12,r),ne(n,e+16,i)},cr=function(){function n(e){this.filename=e,this.c=zi(),this.size=0,this.compression=0}return n.prototype.process=function(e,t){this.ondata(null,e,t)},n.prototype.push=function(e,t){this.ondata||j(5),this.c.p(e),this.size+=e.length,t&&(this.crc=this.c.d()),this.process(e,t||!1)},n}(),u0=function(){function n(e,t){var r=this;t||(t={}),cr.call(this,e),this.d=new ot(t,function(i,s){r.ondata(null,i,s)}),this.compression=8,this.flag=cl(t.level)}return n.prototype.process=function(e,t){try{this.d.push(e,t)}catch(r){this.ondata(r,null,t)}},n.prototype.push=function(e,t){cr.prototype.push.call(this,e,t)},n}(),d0=function(){function n(e,t){var r=this;t||(t={}),cr.call(this,e),this.d=new oh(t,function(i,s,a){r.ondata(i,s,a)}),this.compression=8,this.flag=cl(t.level),this.terminate=this.d.terminate}return n.prototype.process=function(e,t){this.d.push(e,t)},n.prototype.push=function(e,t){cr.prototype.push.call(this,e,t)},n}(),h0=function(){function n(e){this.ondata=e,this.u=[],this.d=1}return n.prototype.add=function(e){var t=this;if(this.ondata||j(5),this.d&2)this.ondata(j(4+(this.d&1)*8,0,1),null,!1);else{var r=Ri(e.filename),i=r.length,s=e.comment,a=s&&Ri(s),o=i!=e.filename.length||a&&s.length!=a.length,l=i+sr(e.extra)+30;i>65535&&this.ondata(j(11,0,1),null,!1);var c=new te(l);Bo(c,0,e,r,o,-1);var u=[c],d=function(){for(var p=0,g=u;p<g.length;p++){var b=g[p];t.ondata(null,b,!1)}u=[]},h=this.d;this.d=0;var m=this.u.length,w=Jo(e,{f:r,u:o,o:a,t:function(){e.terminate&&e.terminate()},r:function(){if(d(),h){var p=t.u[m+1];p?p.r():t.d=1}h=1}}),f=0;e.ondata=function(p,g,b){if(p)t.ondata(p,g,b),t.terminate();else if(f+=g.length,u.push(g),b){var y=new te(16);ne(y,0,134695760),ne(y,4,e.crc),ne(y,8,f),ne(y,12,e.size),u.push(y),w.c=f,w.b=l+f+16,w.crc=e.crc,w.size=e.size,h&&w.r(),h=1}else h&&d()},this.u.push(w)}},n.prototype.end=function(){var e=this;if(this.d&2){this.ondata(j(4+(this.d&1)*8,0,1),null,!0);return}this.d?this.e():this.u.push({r:function(){e.d&1&&(e.u.splice(-1,1),e.e())},t:function(){}}),this.d=3},n.prototype.e=function(){for(var e=0,t=0,r=0,i=0,s=this.u;i<s.length;i++){var a=s[i];r+=46+a.f.length+sr(a.extra)+(a.o?a.o.length:0)}for(var o=new te(r+22),l=0,c=this.u;l<c.length;l++){var a=c[l];Bo(o,e,a,a.f,a.u,-a.c-2,t,a.o),e+=46+a.f.length+sr(a.extra)+(a.o?a.o.length:0),t+=a.b}gh(o,e,this.u.length,r,t),this.ondata(null,o,!0),this.d=2},n.prototype.terminate=function(){for(var e=0,t=this.u;e<t.length;e++){var r=t[e];r.t()}this.d=2},n}();var mh=function(){function n(){}return n.prototype.push=function(e,t){this.ondata(null,e,t)},n.compression=0,n}(),f0=function(){function n(){var e=this;this.i=new Fe(function(t,r){e.ondata(null,t,r)})}return n.prototype.push=function(e,t){try{this.i.push(e,t)}catch(r){this.ondata(r,null,t)}},n.compression=8,n}(),p0=function(){function n(e,t){var r=this;t<32e4?this.i=new Fe(function(i,s){r.ondata(null,i,s)}):(this.i=new sl(function(i,s,a){r.ondata(i,s,a)}),this.terminate=this.i.terminate)}return n.prototype.push=function(e,t){this.i.terminate&&(e=et(e,0)),this.i.push(e,t)},n.compression=8,n}(),g0=function(){function n(e){this.onfile=e,this.k=[],this.o={0:mh},this.p=st}return n.prototype.push=function(e,t){var r=this;if(this.onfile||j(5),this.p||j(4),this.c>0){var i=Math.min(this.c,e.length),s=e.subarray(0,i);if(this.c-=i,this.d?this.d.push(s,!this.c):this.k[0].push(s),e=e.subarray(i),e.length)return this.push(e,t)}else{var a=0,o=0,l=void 0,c=void 0;this.p.length?e.length?(c=new te(this.p.length+e.length),c.set(this.p),c.set(e,this.p.length)):c=this.p:c=e;for(var u=c.length,d=this.c,h=d&&this.d,m=function(){var g,b=Ft(c,o);if(b==67324752){a=1,l=o,w.d=null,w.c=0;var y=Lt(c,o+6),E=Lt(c,o+8),_=y&2048,x=y&8,S=Lt(c,o+26),v=Lt(c,o+28);if(u>o+30+S+v){var k=[];w.k.unshift(k),a=2;var A=Ft(c,o+18),T=Ft(c,o+22),D=fh(c.subarray(o+30,o+=30+S),!_);A==4294967295?(g=x?[-2]:ph(c,o),A=g[0],T=g[1]):x&&(A=-1),o+=v,w.c=A;var R,U={name:D,compression:E,start:function(){if(U.ondata||j(5),!A)U.ondata(null,st,!0);else{var z=r.o[E];z||U.ondata(j(14,"unknown compression type "+E,1),null,!1),R=A<0?new z(D):new z(D,A,T),R.ondata=function(Z,B,q){U.ondata(Z,B,q)};for(var O=0,M=k;O<M.length;O++){var P=M[O];R.push(P,!1)}r.k[0]==k&&r.c?r.d=R:R.push(st,!0)}},terminate:function(){R&&R.terminate&&R.terminate()}};A>=0&&(U.size=A,U.originalSize=T),w.onfile(U)}return"break"}else if(d){if(b==134695760)return l=o+=12+(d==-2&&8),a=3,w.c=0,"break";if(b==33639248)return l=o-=4,a=3,w.c=0,"break"}},w=this;o<u-4;++o){var f=m();if(f==="break")break}if(this.p=st,d<0){var p=a?c.subarray(0,l-12-(d==-2&&8)-(Ft(c,l-16)==134695760&&4)):c.subarray(0,o);h?h.push(p,!!a):this.k[+(a==2)].push(p)}if(a&2)return this.push(c.subarray(o),t);this.p=c.subarray(o)}t&&(this.c&&j(13),this.p=null)},n.prototype.register=function(e){this.o[e.compression]=e},n}();var{ZipReader:bh,BlobReader:yh,TextWriter:xh,BlobWriter:vh}=vi;Yn({useWebWorkers:!1});var _h=async n=>{let e=new Uint8Array(await n.slice(0,4).arrayBuffer());return e[0]===80&&e[1]===75&&e[2]===3&&e[3]===4},Sh=async n=>{let t=await new bh(new yh(n)).getEntries();n.name&&n.name.endsWith(".zip")&&t.every(c=>c.filename.startsWith(n.name.slice(0,-4)+"/"))&&(t=t.map(c=>(c.filename=c.filename.slice(n.name.length-3),c)));let r=new Map(t.map(l=>[l.filename,l])),i=l=>(c,...u)=>r.has(c)?l(r.get(c),...u):null,s=i(l=>l.getData(new xh)),a=i((l,c)=>l.getData(new vh(c)));return{entries:t,loadText:s,loadBlob:a,getSize:l=>r.get(l)?.uncompressedSize??0}},ul=async n=>n.isFile?n:(await Promise.all(Array.from(await new Promise((e,t)=>n.createReader().readEntries(r=>e(r),r=>t(r))),ul))).flat(),Ah=async n=>{let e=await ul(n),t=await Promise.all(e.map(u=>new Promise((d,h)=>u.file(m=>d([m,u.fullPath]),m=>h(m))))),r=new Map(t.map(([u,d])=>[d.replace(n.fullPath+"/",""),u])),i=new TextDecoder,s=u=>u?i.decode(u):null,a=u=>r.get(u)?.arrayBuffer()??null;return{loadText:async u=>s(await a(u)),loadBlob:u=>r.get(u),getSize:u=>r.get(u)?.size??0}},Eh=({name:n,type:e})=>e==="application/vnd.comicbook+zip"||n.endsWith(".cbz"),kh=({name:n,type:e})=>e==="application/x-fictionbook+xml"||n.endsWith(".fb2"),Th=({name:n,type:e})=>e==="application/x-zip-compressed-fb2"||n.endsWith(".fb2.zip")||n.endsWith(".fbz"),Ch=async(n,e)=>{let t;if(n.isDirectory){let s=await Ah(n);t=await new Vt(s).init()}else if(n.size)if(await _h(n)){let s=await Sh(n);if(Eh(n))t=fs(s,n);else if(Th(n)){let{entries:a}=s,o=a.find(c=>c.filename.endsWith(".fb2")),l=await s.loadBlob((o??a[0]).filename);t=await Er(l)}else t=await new Vt(s).init()}else await Es(n)?t=await new Tn({unzlib:al}).open(n):kh(n)&&(t=await Er(n));else throw new Error("File not found");t||alert("File type not supported");let r=new xn(t,e),i=await r.display();return document.body.append(i),r},Rh=({spacing:n,justify:e,hyphenate:t})=>`
    @namespace epub "http://www.idpf.org/2007/ops";
    html {
        color-scheme: light dark;
    }
    /* https://github.com/whatwg/html/issues/5426 */
    @media (prefers-color-scheme: dark) {
        a:link {
            color: lightblue;
        }
    }
    p, li, blockquote, dd {
        line-height: ${n};
        text-align: ${e?"justify":"start"};
        -webkit-hyphens: ${t?"auto":"manual"};
        hyphens: ${t?"auto":"manual"};
        -webkit-hyphenate-limit-before: 3;
        -webkit-hyphenate-limit-after: 2;
        -webkit-hyphenate-limit-lines: 2;
        hanging-punctuation: allow-end last;
        widows: 2;
    }
    /* prevent the above from overriding the align attribute */
    [align="left"] { text-align: left; }
    [align="right"] { text-align: right; }
    [align="center"] { text-align: center; }
    [align="justify"] { text-align: justify; }

    pre {
        white-space: pre-wrap !important;
    }
    aside[epub|type~="endnote"],
    aside[epub|type~="footnote"],
    aside[epub|type~="note"],
    aside[epub|type~="rearnote"] {
        display: none;
    }
`,Ih="en",zh=new Intl.NumberFormat(Ih,{style:"percent"}),re=document.querySelector.bind(document),Oi=class{#e;style={spacing:1.4,justify:!0,hyphenate:!0};layout={margin:48,gap:48,maxColumnWidth:720};closeSideBar(){re("#dimming-overlay").classList.remove("show"),re("#side-bar").classList.remove("show")}constructor(){re("#side-bar-button").addEventListener("click",()=>{re("#dimming-overlay").classList.add("show"),re("#side-bar").classList.add("show")}),re("#dimming-overlay").addEventListener("click",()=>this.closeSideBar());let e=us([{name:"layout",label:"Layout",type:"radio",items:[["Paginated","paginated"],["Scrolled","scrolled"]],onclick:t=>{this.layout.flow=t,this.setAppearance()}}]);e.element.classList.add("menu"),re("#menu-button").append(e.element),re("#menu-button > button").addEventListener("click",()=>e.element.classList.toggle("show")),e.groups.layout.select("paginated")}async open(e){this.view=await Ch(e,this.#t.bind(this));let{book:t}=this.view;this.setAppearance(),this.view.renderer.next(),re("#header-bar").style.visibility="visible",re("#nav-bar").style.visibility="visible",re("#left-button").addEventListener("click",()=>this.view.goLeft()),re("#right-button").addEventListener("click",()=>this.view.goRight()),re("#left-big-bar").classList.remove("none"),re("#right-big-bar").classList.remove("none"),re("#left-big-bar").addEventListener("click",()=>this.view.goLeft()),re("#right-big-bar").addEventListener("click",()=>this.view.goRight());let r=re("#progress-slider");r.dir=t.dir,r.addEventListener("input",l=>this.view.goToFraction(parseFloat(l.target.value)));let i=t.sections.filter(l=>l.linear!=="no").map(l=>l.size);if(i.length<100){let l=i.reduce((u,d)=>u+d,0),c=0;for(let u of i.slice(0,-1)){c+=u;let d=document.createElement("option");d.value=c/l,re("#tick-marks").append(d)}}document.addEventListener("keydown",this.#n.bind(this));let s=t.metadata?.title??"Untitled Book";document.title=s,re("#side-bar-title").innerText=s;let a=t.metadata?.author;re("#side-bar-author").innerText=typeof a=="string"?a:a?.map(l=>typeof l=="string"?l:l.name)?.join(", ")??"",Promise.resolve(t.getCover?.())?.then(l=>l?re("#side-bar-cover").src=URL.createObjectURL(l):null);let o=t.toc;o&&(this.#e=cs(o,l=>{this.view.goTo(l).catch(c=>{}),this.closeSideBar()}),re("#toc-view").append(this.#e.element))}setAppearance=()=>{this.view?.setAppearance({css:Rh(this.style),layout:this.layout});let e=this.layout.flow==="scrolled";document.documentElement.classList.toggle("scrolled",e)};#t(e){switch(e.type){case"loaded":this.#r(e);break;case"relocated":this.#i(e);break;case"reference":this.#s(e);break;case"external-link":globalThis.open(e.uri,"_blank");break}}#n(e){let t=e.key;t==="ArrowLeft"||t==="h"?this.view.goLeft():(t==="ArrowRight"||t==="l")&&this.view.goRight()}#r({doc:e}){e.addEventListener("keydown",this.#n.bind(this)),document.dispatchEvent(new CustomEvent("immersiveTranslateEbookLoaded"))}#i(e){let{fraction:t,location:r,tocItem:i,pageItem:s}=e,a=zh.format(t),o=s?`Page ${s.label}`:`Loc ${r.current}`,l=re("#progress-slider");l.style.visibility="visible",l.value=t,l.title=`${a} \xB7 ${o}`,i?.href&&this.#e?.setCurrentHref?.(i.href)}#s(e){let{content:t,element:r}=e,{point:i,dir:s}=os(r),a=document.createElement("iframe");a.sandbox="allow-same-origin",a.srcdoc=t,a.onload=()=>{let u=a.contentDocument;u.documentElement.style.colorScheme="light dark",u.body.style.margin="18px"},Object.assign(a.style,{border:"0",width:"100%",height:"100%"});let{popover:o,arrow:l,overlay:c}=hs(300,250,i,s);c.style.zIndex=3,o.style.zIndex=3,l.style.zIndex=3,o.append(a),document.body.append(c),document.body.append(o),document.body.append(l)}},dl=async n=>{document.body.removeChild(re("#drop-target"));let e=new Oi;globalThis.reader=e,await e.open(n)},Dh=n=>n.preventDefault(),Lh=n=>{n.preventDefault();let e=Array.from(n.dataTransfer.items).find(t=>t.kind==="file");if(e){let t=e.webkitGetAsEntry();dl(t.isFile?e.getAsFile():t).catch(r=>{})}},hl=re("#drop-target");hl.addEventListener("drop",Lh);hl.addEventListener("dragover",Dh);re("#file-input").addEventListener("change",n=>dl(n.target.files[0]).catch(e=>{}));re("#file-button").addEventListener("click",()=>{document.querySelector("#file-input").click()});})();
