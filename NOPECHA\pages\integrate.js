(()=>{var o=chrome;var a="https://api.nopecha.com",t="https://www.nopecha.com",n="https://developers.nopecha.com",i={doc:{url:n,automation:{url:`${n}/guides/extension_advanced/#automation-build`}},api:{url:a,recognition:{url:`${a}/recognition`},status:{url:`${a}/status`}},www:{url:t,annoucement:{url:`${t}/json/announcement.json`},demo:{url:`${t}/captcha`,recaptcha:{url:`${t}/captcha/recaptcha`},funcaptcha:{url:`${t}/captcha/funcaptcha`},awscaptcha:{url:`${t}/captcha/awscaptcha`},textcaptcha:{url:`${t}/captcha/textcaptcha`},turnstile:{url:`${t}/captcha/turnstile`},perimeterx:{url:`${t}/captcha/perimeterx`},geetest:{url:`${t}/captcha/geetest`},lemincaptcha:{url:`${t}/captcha/lemincaptcha`}},manage:{url:`${t}/manage`},pricing:{url:`${t}/pricing`},setup:{url:`${t}/setup`}},discord:{url:`${t}/discord`},github:{url:`${t}/github`,release:{url:`${t}/github/release`}}};function c(r){if(document.readyState!=="loading")setTimeout(r,0);else{let e;e=()=>{removeEventListener("DOMContentLoaded",e),r()},addEventListener("DOMContentLoaded",e)}}function u(){document.documentElement.setAttribute("installed","yes"),document.documentElement.setAttribute("data",JSON.stringify({version:o.runtime.getManifest().version}))}c(u);})();
