.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 99999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #888;
  width: 500px;
  border-radius: 12px;
  position: relative;
}

.immersive-translate-close {
  color: #aaaaaa;
  font-size: 28px;
  font-weight: bold;
  position: absolute;
  right: 12px;
  top: 4px;
  display: none;
}

.mobile-hint {
  display: none;
}

@media (max-width: 768px) {
  .immersive-translate-modal-content {
    width: 300px;
  }

  .mobile-hint {
    display: block;
  }
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-content p {
  margin: 0;
  padding: 0;
  margin: 0;
  padding: 0;
  font-size: 16px;
  margin-bottom: 12px;
}

.immersive-translate-btn-warpper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.immersive-translate-btn {
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 5px;
  padding: 10px 20px;
  font-size: 1rem;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: fit-content;
}

.immersive-gary {
  background-color: rgb(89, 107, 120);
}

.immersive-disable {
  background-color: #f1f1f1;
}

.immersive-translate-progress-container {
  width: 100%;
  height: 30px;
  background-color: #f1f1f1;
  border-radius: 8px;
  margin-bottom: 12px;
  display: none;
}

.immersive-translate-progress-bar {
  height: 100%;
  background-color: #4CAF50;
  width: 0%;
  transition: width 0.5s ease-in-out;
  border-radius: 8px;
  margin-bottom: 12px;
}


.image-mode-button {
  padding: 10px 20px;
  font-size: 18px;
  cursor: pointer;
}

.mode-off {
  background-color: red;
  color: white;
}

.mode-on {
  background-color: green;
  color: white;
}

.modal-subttile {
  font-size: 16px;
  margin-bottom: 8px;
}

/* 图片模式 */
.control-button {
  font-size: 12px;
  padding: 5px 10px;
  margin: 0 6px 0 0;
}

.switch-group {
  display: flex;
  flex-direction: column;
}

.switch-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 3px 0;
}

.switch-label {
  margin-left: 4px;
  margin-right: 10px;
  font-size: 14px;
  flex: 1;
}

.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 26px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
}

input:checked+.slider {
  background-color: #ea4c89;
}

input:focus+.slider {
  box-shadow: 0 0 1px #ea4c89;
}

input:checked+.slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}