<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .status-text {
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .status-dot.enabled {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .status-dot.disabled {
            background: #f44336;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }
        
        .toggle-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .toggle-btn.enabled {
            background: #f44336;
            color: white;
        }
        
        .toggle-btn.enabled:hover {
            background: #d32f2f;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }
        
        .toggle-btn.disabled {
            background: #4CAF50;
            color: white;
        }
        
        .toggle-btn.disabled:hover {
            background: #388e3c;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .info-text {
            font-size: 12px;
            line-height: 1.4;
            opacity: 0.9;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ UA Header Remover</h1>
    </div>
    
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <div>加载中...</div>
    </div>
    
    <div id="content" style="display: none;">
        <div class="status-card">
            <div class="status-indicator">
                <span class="status-text" id="statusText">状态检查中...</span>
                <div class="status-dot" id="statusDot"></div>
            </div>
            <button class="toggle-btn" id="toggleBtn">切换状态</button>
        </div>
        
        <div class="info-section">
            <div class="info-title">📋 功能说明</div>
            <div class="info-text">
                此扩展会移除所有HTTP请求中的User-Agent头部信息，帮助保护您的浏览器指纹隐私。启用后将对所有网站生效。
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
