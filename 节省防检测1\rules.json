[{"id": 11, "priority": 2, "action": {"type": "allow"}, "condition": {"urlFilter": "https://www.google.com/recaptcha*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 1, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*.google.com/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 2, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*.clarity.ms/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 3, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*.doubleclick.net/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 4, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*googletagmanager.com/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 5, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*google-analytics.com/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 6, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*widget.trustpilot.com/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 7, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*facebook.net/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 8, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*facebook.com/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 10, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*bam.nr-data.net*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 12, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://dc.qmee.com*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}, {"id": 13, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://qubed-session-checks.prod.qubed.ai/fingerprint*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "webtransport", "webbundle", "other"]}}]