import re
from urllib.parse import parse_qs, urlparse

def extract_offer_info(file_path):
    """从文件中提取offer_id和offer_name"""
    offers = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式查找所有Location URL
        location_pattern = r'Location: (https?://[^\r\n]+)'
        matches = re.findall(location_pattern, content)
        
        for url in matches:
            try:
                # 解析URL并获取查询参数
                parsed_url = urlparse(url)
                query_params = parse_qs(parsed_url.query)
                
                # 提取offer_id和offer_name
                if 'offer_id' in query_params and 'offer_name' in query_params:
                    offer_id = query_params['offer_id'][0]
                    offer_name = query_params['offer_name'][0]
                    
                    # 处理URL编码
                    offer_name = offer_name.replace('%28', '(').replace('%29', ')')
                    offers.append((offer_id, offer_name))
            except Exception as e:
                print(f"处理URL时出错: {url}")
                print(f"错误信息: {str(e)}")
                
    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        
    return offers

def save_offers_to_file(offers, output_file):
    """将offer信息保存到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入表头
            f.write("offer_id\toffer_name\n")
            f.write("\n")  # 空行
            
            # 写入数据
            for offer_id, offer_name in offers:
                f.write(f"{offer_id}\t{offer_name}\n")
                
        print(f"已将结果保存到: {output_file}")
    except Exception as e:
        print(f"保存文件时出错: {str(e)}")

if __name__ == "__main__":
    input_file = r'd:\插件\随机选第三版\aff_id=1&offer_id=(0-2000)US.txt'
    output_file = r'd:\插件\随机选第三版\extracted_offers.txt'
    
    # 提取offer信息
    offers = extract_offer_info(input_file)
    
    if offers:
        print(f"找到 {len(offers)} 个offer")
        # 保存结果
        save_offers_to_file(offers, output_file)
    else:
        print("未找到任何offer信息")
