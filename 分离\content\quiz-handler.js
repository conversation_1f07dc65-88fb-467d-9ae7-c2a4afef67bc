import { isElementVisible, hasTextInput, isSubmitElement } from './utils.js';
import { enhancedHumanClick, getClickableElement } from './human-interaction.js';

// 分组与类型检测
function getDomainGroupingAttribute() {
  const hostname = window.location.hostname;
  if (hostname.includes('sb.ktrmr.com')) return 'questionname';
  if (hostname.includes('surveys.lifepointspanel.com') || hostname.includes('ts.dlr.de') || hostname.includes('uva.fra1.qualtrics.com') || hostname.includes('stgsurvey.nielsen.com')) return 'name';
  if (hostname.includes('wwdadawdwadadawdaw') || hostname.includes('forms.microsoft.com')) return 'id';
  if (hostname.includes('docs.google.com/forms')) return 'class';
  return 'default';
}

function groupElements(elements) {
  const tempGroups = new Map();
  const finalGroups = new Map();
  const defaultGroup = [];
  const dropdownGroup = [];
  const invalidGroup = []; // 新增无效组，用于存放不应处理的元素
  const groupingAttribute = getDomainGroupingAttribute();
  console.log(`当前网站使用 ${groupingAttribute} 属性进行分组`);
  
  elements.forEach(el => {
    // 检查是否为UI元素或带有UI标记
    if (el.closest('[data-auto-answer-ui="true"]') || el.hasAttribute('data-auto-answer-ui')) {
      invalidGroup.push(el);
      console.log(`跳过UI元素: ${el.id || el.tagName}`);
      return;
    }
    
    // 检查是否为提交元素
    if (isSubmitElement(el)) {
      invalidGroup.push(el);
      console.log(`跳过提交元素: ${el.id || el.tagName}`);
      return;
    }
    
    // 检查是否有文本输入框
    if (hasTextInput(el)) {
      invalidGroup.push(el);
      console.log(`跳过带输入框的元素: ${el.id || el.tagName}`);
      return;
    }
    
    let groupKey;
    if (el.tagName === 'SELECT') {
      dropdownGroup.push(el);
      return;
    }
    
    // 原有分组逻辑保持不变
    if (groupingAttribute === 'default') {
      if (el.hasAttribute('questionname')) {
        groupKey = el.getAttribute('questionname');
      } else if (el.name) {
        groupKey = el.name;
      } else {
        groupKey = `ungrouped_${Math.random()}`;
      }
    } else if (el.hasAttribute(groupingAttribute)) {
      groupKey = el.getAttribute(groupingAttribute);
    } else if (el.name) {
      groupKey = el.name;
    } else {
      const parent = el.closest(`[${groupingAttribute}]`);
      if (parent) {
        groupKey = parent.getAttribute(groupingAttribute);
      } else {
        groupKey = `ungrouped_${Math.random()}`;
      }
    }
    
    if (!tempGroups.has(groupKey)) {
      tempGroups.set(groupKey, []);
    }
    tempGroups.get(groupKey).push(el);
  });
  
  // 原有分组处理逻辑保持不变
  for (const [key, group] of tempGroups) {
    if (group.length === 1) {
      defaultGroup.push(...group);
    } else {
      finalGroups.set(key, group);
    }
  }
  
  if (defaultGroup.length > 0) {
    finalGroups.set('default', defaultGroup);
  }
  
  if (dropdownGroup.length > 0) {
    finalGroups.set('dropdown_group', dropdownGroup);
  }
  
  return finalGroups;
}

function hasSingleSelect(group) {
  return group.every(el => el.type === 'radio');
}

function hasMultiSelect(group) {
  return group.every(el => el.type === 'checkbox');
}

function hasDropdownSelect(group) {
  return group.every(el => el.tagName === 'SELECT');
}

// 处理选择
async function handleBasicSelection(elements, fastMode = false) {
  if (elements.length === 0) return;
  const types = new Set(elements.map(el => el.type || el.tagName));
  if (types.has('radio') && types.has('checkbox')) {
    const checkboxElements = elements.filter(el => el.type === 'checkbox');
    if (checkboxElements.length > 0) {
      const clickableCheckboxes = checkboxElements
        .map(el => getClickableElement(el,fastMode))
        .filter(el => el);
      if (clickableCheckboxes.length > 0) {
        let selectedCount = 0;
        const lastIndex = clickableCheckboxes.length - 1;
        const shuffledCheckboxes = clickableCheckboxes.sort(() => Math.random() - 0.5);
        for (const [index, el] of shuffledCheckboxes.entries()) {
          if ((index === lastIndex && selectedCount === 0) ||
              (Math.random() > 0.5 && selectedCount < 3)) {
            await enhancedHumanClick(el, fastMode);
            selectedCount++;
          }
          if (selectedCount >= 3) break;
        }
      }
    }
    return;
  }
  if (hasSingleSelect(elements)) {
    if (elements.length === 1) return;
    const clickable = elements
      .map(el => getClickableElement(el,fastMode))
      .filter(el => el);
    if (clickable.length === 0) return;
    const target = clickable[Math.floor(Math.random() * clickable.length)];
    await enhancedHumanClick(target, fastMode);
    return;
  }
  if (hasMultiSelect(elements)) {
    if (elements.length === 1) return;
    const clickable = elements
      .filter(el => el.type === 'checkbox')
      .map(el => getClickableElement(el,fastMode))
      .filter(el => el);
    if (clickable.length === 0) return;
    let selectedCount = 0;
    const lastIndex = clickable.length - 1;
    const shuffledClickable = clickable.sort(() => Math.random() - 0.5);
    for (const [index, el] of shuffledClickable.entries()) {
      if ((index === lastIndex && selectedCount === 0) ||
          (Math.random() > 0.5 && selectedCount < 3)) {
        await enhancedHumanClick(el, fastMode);
        selectedCount++;
      }
      if (selectedCount >= 3) break;
    }
    return;
  }
  if (hasDropdownSelect(elements)) {
    for (const select of elements) {
      if (!isElementVisible(select)) continue;
      const options = Array.from(select.options).filter(opt =>
        !opt.disabled &&
        !opt.selected &&
        opt.value !== ''
      );
      if (options.length === 0) continue;
      const targetOption = options[Math.floor(Math.random() * options.length)];
      select.value = targetOption.value;
      if (!fastMode) {
        await enhancedHumanClick(select, fastMode);
      }
      select.dispatchEvent(new Event('change', { bubbles: true }));
    }
  }
}

// 特殊处理函数
async function handleKtrmrSurvey(fastMode) {
  if (!window.location.hostname.includes('sb.ktrmr.com') || !fastMode) {
    return false;
  }

  // 注入请求拦截
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    const [resource, config] = args;
    
    // 检查是否是目标请求
    if (resource.includes('/mrIWeb/mrIWeb.srf')) {
      const formData = new URLSearchParams(config.body);
      const newFormData = new URLSearchParams();
      
      // 保持原有的系统字段
      const systemFields = ['I.Engine', 'I.Project', 'I.Session', 'I.SavePoint', 'I.Renderer'];
      for (const [key, value] of formData.entries()) {
        if (systemFields.some(field => key.startsWith(field))) {
          newFormData.append(key, value);
        }
      }
      
      // 获取所有radio和checkbox
      const inputs = document.querySelectorAll('input[type="radio"], input[type="checkbox"]');
      inputs.forEach(input => {
        if (isElementVisible(input)) {
          newFormData.append(input.name, input.value);
        }
      });
      
      // 添加 _NNext
      newFormData.append('_NNext', formData.get('_NNext') || '');
      
      // 修改请求配置
      const newConfig = {
        ...config,
        body: newFormData.toString()
      };
      
      // 发送修改后的请求
      return originalFetch(resource, newConfig);
    }
    
    // 非目标请求直接放行
    return originalFetch.apply(this, args);
  };
  
  // 直接触发表单提交
  const submitBtn = document.querySelector('input[type="submit"], button[type="submit"]');
  if (submitBtn) {
    submitBtn.click();
  }

  return true;
}

// 自动提交
async function handleAutoSubmit(fastMode = false) {
  const submitBtn = document.querySelector(
    'button[type="submit"], input[type="submit"], ' +
    '[id*="submit" i], [class*="submit" i], ' +
    '[id*="next" i], [class*="next" i], ' +
    '[id*="continue" i], [class*="continue" i]'
  );
  if (submitBtn) {
    await enhancedHumanClick(submitBtn, fastMode);
  } else {
    console.log('未找到提交按钮');
  }
}

// 处理问卷
async function handleUniversalQuestions(fastMode = false) {
  const interactiveElements = document.querySelectorAll(`input[type="radio"]:not([disabled]),
    input[type="checkbox"]:not([disabled]),
    select:not([disabled])`);
  const groups = groupElements(interactiveElements);
  for (const [name, elements] of groups) {
    console.log(`${fastMode ? '快速' : ''}处理组: ${name}, 选项数: ${elements.length}`);
    await handleBasicSelection(elements, fastMode);
    if (!fastMode) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
}

export {
  groupElements,
  handleBasicSelection,
  handleKtrmrSurvey,
  handleAutoSubmit,
  handleUniversalQuestions
};