// humanTyping.js
let isProcessing = false;

// 添加键盘快捷键监听 - 使用 Ctrl+B
document.addEventListener('keydown', function(event) {
  // 检测 Ctrl+B 组合键
  if (event.ctrlKey && event.key === 'b') {
    if (!isProcessing) {
      handlePasteAsHumanTyping();
    }
  }
});

// 保留原有的消息监听功能，以便兼容性
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "pasteAsHumanTyping") {
    if (isProcessing) {
      sendResponse({ status: "忽略" });
      return;
    }
    isProcessing = true;
    handlePasteAsHumanTyping().then(() => {
      isProcessing = false;
      sendResponse({ status: "已完成" });
    }).catch((error) => {
      isProcessing = false;
      sendResponse({ status: "失败" });
    });
    return true;
  }
  sendResponse({ status: "未知操作" });
});

async function simulateHumanTyping(element, text) {
  element.focus();

  const getRandomDelay = (prevChar, currChar) => {
    let baseDelay = 100 + Math.random() * 150;
    if (/[A-Z]/.test(currChar)) {
      baseDelay += 30;
    } else if (/[.,!?]/.test(currChar)) {
      baseDelay += 150 + Math.random() * 200;
    } else if (/\s/.test(currChar) && prevChar && !/\s/.test(prevChar)) {
      baseDelay += 100 + Math.random() * 150;
    }
    if (Math.random() < 0.1) {
      baseDelay += 500 + Math.random() * 700;
    }
    return baseDelay;
  };

  const dispatchKeyEvent = (type, char) => {
    const keyCode = char.charCodeAt(0);
    const event = new KeyboardEvent(type, {
      bubbles: true,
      cancelable: true,
      key: char,
      code: `Key${char.toUpperCase()}`,
      keyCode: keyCode,
      which: keyCode
    });
    element.dispatchEvent(event);
  };

  let prevChar = null;
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    if (element.isContentEditable) {
      const range = document.getSelection().getRangeAt(0);
      const textNode = document.createTextNode(char);
      range.insertNode(textNode);
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
    } else {
      element.value += char;
    }

    dispatchKeyEvent('keydown', char);
    dispatchKeyEvent('keypress', char);
    dispatchKeyEvent('keyup', char);
    element.dispatchEvent(new Event('input', { bubbles: true }));

    await new Promise(resolve => setTimeout(resolve, getRandomDelay(prevChar, char)));
    prevChar = char;
  }

  element.dispatchEvent(new Event('change', { bubbles: true }));
}

async function handlePasteAsHumanTyping() {
  try {
    isProcessing = true;
    const targetElement = document.activeElement;
    if (!targetElement ||
        (targetElement.tagName !== 'INPUT' &&
         targetElement.tagName !== 'TEXTAREA' &&
         !targetElement.isContentEditable)) {
      isProcessing = false;
      return;
    }

    if (targetElement.disabled || targetElement.readOnly) {
      isProcessing = false;
      return;
    }

    const text = await navigator.clipboard.readText();
    if (!text) {
      isProcessing = false;
      return;
    }

    await simulateHumanTyping(targetElement, text);
    isProcessing = false;
  } catch (error) {
    isProcessing = false;
    throw error;
  }
}
