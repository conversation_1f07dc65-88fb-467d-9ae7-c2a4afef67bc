(()=>{function A(){if("ancestorOrigins"in location){let t=location.ancestorOrigins,n=t[1]??t[0];if(n)return n.split("/")[2]}let e=document.referrer;return e?e.split("/")[2]:location.origin}var b=chrome;var M="https://api.nopecha.com",r="https://www.nopecha.com",D="https://developers.nopecha.com",Be={doc:{url:D,automation:{url:`${D}/guides/extension_advanced/#automation-build`}},api:{url:M,recognition:{url:`${M}/recognition`},status:{url:`${M}/status`}},www:{url:r,annoucement:{url:`${r}/json/announcement.json`},demo:{url:`${r}/captcha`,recaptcha:{url:`${r}/captcha/recaptcha`},funcaptcha:{url:`${r}/captcha/funcaptcha`},awscaptcha:{url:`${r}/captcha/awscaptcha`},textcaptcha:{url:`${r}/captcha/textcaptcha`},turnstile:{url:`${r}/captcha/turnstile`},perimeterx:{url:`${r}/captcha/perimeterx`},geetest:{url:`${r}/captcha/geetest`},lemincaptcha:{url:`${r}/captcha/lemincaptcha`}},manage:{url:`${r}/manage`},pricing:{url:`${r}/pricing`},setup:{url:`${r}/setup`}},discord:{url:`${r}/discord`},github:{url:`${r}/github`,release:{url:`${r}/github/release`}}};function O(e){let t=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(n=>n.charCodeAt(0));return V(t)}var q=new Uint32Array(256);for(let e=256;e--;){let t=e;for(let n=8;n--;)t=t&1?3988292384^t>>>1:t>>>1;q[e]=t}function V(e){let t=-1;for(let n of e)t=t>>>8^q[t&255^n];return(t^-1)>>>0}async function d(e,t){let n=""+[+new Date,performance.now(),Math.random()],[o,a]=await new Promise(i=>{b.runtime.sendMessage([n,e,...t],i)});if(o===O(n))return a}function y(){let e;return t=>e||(e=t().finally(()=>e=void 0),e)}var de=y(),p;function N(){return de(async()=>(p||(p=await d("settings::get",[])),p))}function W(e){p&&(p={...p,...e},U(p))}function C(){return p}function l(e){return new Promise(t=>setTimeout(t,e))}var j=[];function E(e,t){e.timedout=!1,j.push(e);let n,o=setInterval(async()=>{await z(e,C())||(clearTimeout(n),clearInterval(o))},400);t&&(n=setTimeout(()=>clearInterval(o),t),e.timedout=!0)}async function z(e,t){if(e.timedout)return!1;let n=e.condition(t);if(n===e.running())return!1;if(!n&&e.running())return e.quit(),!1;if(n&&!e.running()){for(;!e.ready();)await l(200);return e.start(),!1}}function U(e){j.forEach(t=>z(t,e))}function F(){b.runtime.connect({name:"stream"}).onMessage.addListener(t=>{t.event==="settingsUpdate"&&W(t.settings)})}function $(e){if(document.readyState!=="loading")setTimeout(e,0);else{let t;t=()=>{removeEventListener("DOMContentLoaded",t),e()},addEventListener("DOMContentLoaded",t)}}var Xe=y();function J(e){postMessage({source:"nopecha",...e})}function _(e){J(e)}var T,L,H=!1;function Q(){return!!document.querySelector(".recaptcha-checkbox")}function Y(){H=!0,T=new MutationObserver(t=>{t.length===2&&X(),t.length&&t[0].target.classList.contains("recaptcha-checkbox-expired")&&window.location.reload()}),T.observe(document.querySelector(".recaptcha-checkbox"),{attributes:!0});let e=!1;L=new IntersectionObserver(()=>{e||(e=!0,X())},{threshold:0}),L.observe(document.body)}function G(){T.disconnect(),L.disconnect(),H=!1}function K(){return H}async function X(){await l(400),_({action:"click",selector:".recaptcha-checkbox"})}function ge(e,t){let n=document.createElement("canvas");return n.width=e,n.height=t,n}function Z(e){return e.toDataURL("image/jpeg").replace(/data:image\/[a-z]+;base64,/g,"")}function pe(e){try{e.getContext("2d").getImageData(0,0,1,1)}catch{return!0}return!1}async function ee(e,t,n=1e4){if(!t&&!e.complete&&!await new Promise(u=>{let c=setTimeout(()=>{u(!1)},n);e.addEventListener("load",()=>{clearTimeout(c),u(!0)})}))return;let o=ge(e.naturalWidth||t?.clientWidth,e.naturalHeight||t?.clientHeight);return o.getContext("2d").drawImage(e,0,0),!pe(o)&&o}function fe(e,t,n,o){let a=(o*t+n)*4;return[e[a],e[a+1],e[a+2]]}function he(e,t){return e.every(n=>n<=t)}function be(e,t){return e.every(n=>n>=t)}function te(e,t=0,n=230,o=.99){let a=e.getContext("2d"),i=a.canvas.width,u=a.canvas.height;if(i===0||u===0)return!0;let c=a.getImageData(0,0,i,u).data,m=0;for(let v=0;v<u;v++)for(let f=0;f<i;f++){let g=fe(c,i,f,v);(he(g,t)||be(g,n))&&m++}return m/(i*u)>o}function ne(){return[]}function oe(e){return new Promise(t=>{e.push(t)})}function k(e){e.forEach(t=>t()),e.splice(0)}async function ae(e,t){let n={v:b.runtime.getManifest().version,key:_e(e)};return n.url=await d("tab::getURL",[]),n}function _e(e){return!e.keys||!e.keys.length?e.key:e.keys[Math.floor(Math.random()*e.keys.length)]}var ve,S=ne(),R,w=!1;function ie(){return!!document.querySelector(".rc-imageselect, .rc-imageselect-target")}function ce(){w=!0,k(S);let e;R=new MutationObserver(()=>{clearTimeout(e),e=setTimeout(()=>k(S),200)}),R.observe(document.body,{childList:!0,subtree:!0}),ke()}function se(){R.disconnect(),w=!1,k(S)}function le(){return w}function we(){return document.querySelector(".rc-doscaptcha-header")}function xe(){let e=document.querySelector("#recaptcha-verify-button");return e&&e.getAttribute("disabled")}var ye={[1]:1,[0]:3,[2]:4};async function Ce(){for(;;){await l(1e3);let e=document.querySelector(".rc-imageselect-instructions");if(!e)continue;let t=e.innerText.split(`
`),n=t.slice(0,2).join(" ").replace(/\s+/g," ").trim(),o=[...document.querySelectorAll("table tr td")];if(o.length!==9&&o.length!==16)continue;let a=o.map(c=>c.querySelector("img")).filter(c=>c).filter(c=>c.src.trim());if(a.length!==9&&a.length!==16)continue;let i=o.length===16?2:a.some(c=>c.classList.contains("rc-image-tile-11"))?1:0,u=t.length===3&&i!==2;return{task:n,type:i,cells:o,images:a,waitAfterSolve:u}}}var re=!1;async function ke(){if(!re){for(re=!0;w&&(we()||xe());)await l(1e3);for(;w;){let{task:e,type:t,cells:n,images:o,waitAfterSolve:a}=await Ce(),i=C(),u=new Date().valueOf(),c=[...n];t!==1&&(o=[o[0]]);let m=await Promise.all(o.map(s=>ee(s)));if(t===1){let s=[],x=[];for(let[B,h]of m.entries())h.width!==100||h.height!==100||(s.push(c[B]),x.push(h));c=s,m=x}if(m.length===0){_({action:"click",selector:"#recaptcha-verify-button"}),await l(3e3);continue}let I=!1;for(let s of m)if(te(s)){I=!0;break}if(I){await l(3e3);continue}let v=m.map(Z),f=ye[t],g=await d("api::recognition",[{type:"recaptcha",task:e,image_data:v,grid:[f,f].join("x"),...await ae(i)}]);if(!g||"error"in g){console.warn(`[@nope/recaptcha/${ve}]`,"api error",g),await l(2e3);continue}let ue=new Date().valueOf();if(i.recaptcha_solve_delay){let s=i.recaptcha_solve_delay_time-ue+u;s>0&&await l(s)}let P=t===2?4:3;for(c.forEach((s,x)=>{let B=s.classList.contains("rc-imageselect-tileselected"),h=n.indexOf(s);g.data[x]!==B&&_({action:"click",selector:`tr:nth-child(${Math.floor(h/P)+1}) td:nth-child(${h%P+1})`})}),(!a||!g.data.some(s=>s))&&(await l(200),_({action:"click",selector:"#recaptcha-verify-button"})),await oe(S);document.querySelectorAll(".rc-imageselect-dynamic-selected").length>0;)await l(1e3)}}}async function Se(){F(),await N(),await d("tab::registerDetectedCaptcha",["recaptcha"]);let e=A();location.pathname.endsWith("/anchor")?E({name:"recaptcha/auto-open",condition:t=>t.enabled&&t.recaptcha_auto_open&&!t.disabled_hosts.includes(e),ready:Q,start:Y,quit:G,running:K}):E({name:"recaptcha/auto-solve",condition:t=>t.enabled&&t.recaptcha_auto_solve&&!t.disabled_hosts.includes(e),ready:ie,start:ce,quit:se,running:le})}$(Se);})();
