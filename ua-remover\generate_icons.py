#!/usr/bin/env python3
"""
生成不同尺寸的图标文件
需要安装: pip install Pillow cairosvg
"""

import os
from PIL import Image, ImageDraw, ImageFont
import io

def create_icon(size, enabled=True):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 计算尺寸比例
    scale = size / 128
    
    # 背景圆形
    center = size // 2
    radius = int(60 * scale)
    
    if enabled:
        # 启用状态 - 蓝紫色渐变
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=(102, 126, 234, 255), outline=(255, 255, 255, 255), width=max(1, int(2*scale)))
    else:
        # 禁用状态 - 灰色
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=(128, 128, 128, 255), outline=(255, 255, 255, 255), width=max(1, int(2*scale)))
    
    # 盾牌形状
    shield_points = [
        (center, int(20*scale)),  # 顶部
        (center - int(20*scale), int(30*scale)),  # 左上
        (center - int(20*scale), int(60*scale)),  # 左下
        (center, int(95*scale)),  # 底部
        (center + int(20*scale), int(60*scale)),  # 右下
        (center + int(20*scale), int(30*scale)),  # 右上
    ]
    draw.polygon(shield_points, fill=(255, 255, 255, 230))
    
    # 禁止符号
    if enabled:
        # 红色禁止符号
        circle_radius = int(25 * scale)
        draw.ellipse([center-circle_radius, center-int(10*scale)-circle_radius, 
                     center+circle_radius, center-int(10*scale)+circle_radius], 
                    outline=(244, 67, 54, 255), width=max(2, int(4*scale)))
        
        # 斜线
        line_start = (center - int(19*scale), center - int(25*scale))
        line_end = (center + int(19*scale), center + int(5*scale))
        draw.line([line_start, line_end], fill=(244, 67, 54, 255), width=max(2, int(4*scale)))
    
    # UA文字
    try:
        font_size = max(8, int(16 * scale))
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    text = "UA"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = center - text_width // 2
    text_y = center - int(10*scale) - text_height // 2
    
    draw.text((text_x, text_y), text, fill=(51, 51, 51, 255), font=font)
    
    return img

def main():
    """生成所有尺寸的图标"""
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        # 启用状态图标
        icon = create_icon(size, enabled=True)
        icon.save(f'icons/icon{size}.png', 'PNG')
        print(f'生成 icon{size}.png')
        
        # 禁用状态图标
        icon_disabled = create_icon(size, enabled=False)
        icon_disabled.save(f'icons/icon{size}-disabled.png', 'PNG')
        print(f'生成 icon{size}-disabled.png')

if __name__ == '__main__':
    main()
