<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LifePoints Registration Simulator</title>
    <style>
        body {
            width: 300px;
            font-family: Arial, sans-serif;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .container {
            text-align: center;
        }
        select, input, button {
            margin: 5px 0;
            padding: 8px;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>LifePoints 自动注册</h2>
        <select id="countrySelect">
            <option value="en_US">美国 (US)</option>
            <option value="en_GB">英国 (UK)</option>
            <option value="fr_FR">法国 (FR)</option>
            <option value="de_DE">德国 (DE)</option>
            <option value="ja_JP">日本 (JP)</option>
            <option value="en_IE">爱尔兰 (IE)</option>
            <!-- 其他国家选项 -->
        </select>
        <input type="email" id="emailInput" placeholder="输入邮箱" required>
        <input type="password" id="passwordInput" placeholder="输入密码" required>
        <button id="createBookmarksOnly">生成资料</button>
    </div>
    <script src="popup.js"></script>
</body>
</html>