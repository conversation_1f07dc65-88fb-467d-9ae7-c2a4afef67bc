(function() {
    // 将代码注入页面作用域
    const script = document.createElement('script');
    script.textContent = '(' + function() {
        'use strict';
        const FIELD_NAMES = ['answer-timings', 'answer_timings'];

        function clearInputs() {
            FIELD_NAMES.forEach(name => {
                const input = document.querySelector(`input[name="${name}"], #${name}`);
                if (input && input.value !== '') input.value = '';
            });
        }

        // MutationObserver 监听 input 动态生成
        const observer = new MutationObserver(clearInputs);
        observer.observe(document, { childList: true, subtree: true });

        // input/input/change 事件监听
        FIELD_NAMES.forEach(name => {
            document.addEventListener('input', e => {
                if (e.target && (e.target.name === name || e.target.id === name)) e.target.value = '';
            }, true);
            document.addEventListener('change', e => {
                if (e.target && (e.target.name === name || e.target.id === name)) e.target.value = '';
            }, true);
        });

        // submit 事件监听
        document.addEventListener('submit', function() {
            clearInputs();
        }, true);

        // 拦截 FormData
        const OriginalFormData = window.FormData;
        window.FormData = function(form) {
            const fd = new OriginalFormData(form);
            FIELD_NAMES.forEach(name => {
                if (fd.has(name)) fd.set(name, '');
            });
            return fd;
        };
        window.FormData.prototype = OriginalFormData.prototype;
    } + ')();';
    document.documentElement.appendChild(script);
})();