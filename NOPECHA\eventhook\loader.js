(()=>{function g(){let i=new WeakMap,m=50+Math.floor(Math.random()*1e3),d=50+Math.floor(Math.random()*2e3);function y(e,n,t,r){return{x:e,y:n,clientX:e,clientY:n,layerX:e,layerY:n,offsetX:e-1,offsetY:n-1,pageX:e,pageY:n,screenX:t+e,screenY:r+n}}function w(e,n){i.get(e).filter(([r])=>r===n.type).forEach(([r,o])=>{o(n)})}let b={click:({selector:e,offset:n,screenOffset:t,events:r})=>{let o=document.querySelector(e);if(!o||!i.has(o))return;let a=o.getBoundingClientRect(),u=n?n[0]:Math.floor(Math.random()*a.width),s=n?n[1]:Math.floor(Math.random()*a.height),p=t?t[0]:m,c=t?t[1]:d,l=r??["click"];for(let f of l){let M=h("PointerEvent",f,{...y(u,s,p,c),composed:!0,pointerId:1,pointerType:"mouse",srcElement:o,target:o},{target:o});w(o,M)}},mousedata:({data:e,timeOffset:n})=>{let t=Date.now,r=0;Date.now=()=>r;let o=[["mm","mousemove"],["md","mousedown"],["mu","mouseup"]],a=document.body;o.forEach(([u,s])=>{e[u].forEach(([p,c,l])=>{let f=h("MouseEvent",s,{...y(p,c,0,0),composed:!0,pointerId:1,pointerType:"mouse",srcElement:a,target:a},{target:a,timestamp:n+l-performance.timeOrigin});r=Math.floor(n+l),w(a,f)})}),Date.now=t}};function h(e,n,t,r){{let o=new window[e](n,t),a={get(u,s,p){if(s==="isTrusted")return!0;if(s in r)return r[s];let c=o[s];return c instanceof Function?c.bind(o):c}};return new Proxy(o,a)}}function v(e){typeof e=="string"&&(e=JSON.parse(e)),b[e.action](e)}function k(e,n){i.has(e)||i.set(e,[]),i.get(e).push(n)}{let e=Element.prototype;e.addEventListener=new Proxy(e.addEventListener,{apply(n,t,r){return k(t,r),n.apply(t,r)}})}return v}function E(){let i=g();addEventListener("message",m=>{let d=m.data;typeof d!="object"||d.source!=="nopecha"||(m.stopImmediatePropagation(),i(d))})}E();})();
