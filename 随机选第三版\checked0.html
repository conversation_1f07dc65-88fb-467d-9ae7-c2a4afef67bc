<!DOCTYPE html>
<!-- saved from url=(0074)https://surveys.lifepointspanel.com/survey/bor/v3/EMEA/92209613/92209613_D -->
<html lang="en-US" class=" fullscreen requestanimationframe raf csscalc supports cssfilters flexwrap csspositionsticky csstransforms3d csstransitions placeholder matchmedia"><!--<![endif]--><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="robots" content="noindex, nofollow, noarchive">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

<!-- respview.mobile.meta -->


<!-- respview.client.meta -->
            <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
            <link rel="stylesheet" href="./checked0_files/css">

<title>Survey --- 调查</title>


<!-- respview.css -->
<link rel="stylesheet" href="./checked0_files/jquery-ui-1.9.2.custom.min.css">
<link rel="stylesheet" href="./checked0_files/jquery-ui-1.9.2.beacon.css">

<!-- generated from: static/support/font-awesome-4.2.0/less/font-awesome.less, static/support/select2-4.0.1/select2.min.css, static/survey.respondent-post151.less, bor/v3/EMEA/92209613/92209613_D/static/theme.less, static/local/qarts/template/v5.1/qarts.less and 0 themevars -->
<link rel="stylesheet" href="./checked0_files/less-compiled.css">


<!-- extra CSS -->



<!-- respview.mobile.css -->
<link rel="stylesheet" href="./checked0_files/failsafe.css" type="text/css" charset="utf-8">

<!-- respview.client.css -->

<style>.autosave-restart{display:none;}</style>

<!-- client.css -->
<style media="screen">

</style>

<!-- JQuery -->
<!--
/s/jquery183.all.js
-->
<script src="./checked0_files/caaa730ed462680b3c3fc324ca591ac5.js.下载"></script>


<!-- respview.js -->

<!--[if lte IE 8]><script src="/s/support/respond/respond-1.4.2.min.js"></script><script src="/s/support/rem-unit-polyfill/rem.min.js"></script><![endif]-->


<!--
/s/support/jquery-ui-1.9.2.custom/js/jquery-ui-1.9.2.custom.min.js
/s/common.js
/s/respview-post132.js
/s/survey.respondent-post143.js
-->
<script src="./checked0_files/696fc7f735e0c44fc0da11231b264c0e.js.下载"></script>


<!-- extra JS -->






<!-- respview.mobile.js -->
<script type="text/javascript" src="./checked0_files/failsafe.js.下载" charset="utf-8"></script>

<!-- respview.client.js -->


<!-- client.javascript -->


<script>
function goForward(){window.history.forward();}
goForward();
addHandler(goForward);
window.onpageshow=function(evt){if(evt.persisted) goForward();}
window.onpopstate=function(){goForward();}
window.onunload=function(){void(0);}
</script>

<!-- extra Head -->


        <script src="./checked0_files/vendors~main.js.下载"></script>
        <script src="./checked0_files/app_dev.js.下载"></script><style type="text/css" data-glamor=""></style>
        <script type="text/javascript">
          QArts.config = {server: "",defStyleVer: "v2364"};

        </script>
        <script src="./checked0_files/config.min.js.下载"></script>
        <script src="./checked0_files/qarts.min.js.下载"></script>

          <style type="text/css">span.qaCode {display: inline-block;background-color: fd0707;border: 3px solid fff;border-radius: 15%;-moz-border-radius: 15%;-webkit-border-radius: 15%;padding: 5px;color: fff;font-size: 11px;font-weight: 700;margin: 2px 0;left: 0px;top: -5px;box-shadow: 0px 0px 2px 342828;} .survey-buttons {visibility: hidden;}
              sq-QARTS-container-QBAR .hidden {display: none;} .QA_QTQI { -webkit-transition: max-height 5s;-moz-transition: max-height 5s;-ms-transition: max-height 5s;-o-transition: max-height 5s;transition: max-height 5s; } .QA_QTQI .question-text {margin-top: 0;} .minheight { overflow: hidden;max-height: 10px;} .normalheight {overflow: auto;max-height: auto;}
          </style>
          <script type="text/javascript">
              var DQ={device:{'family': 'iPhone', 'releaseYear': '0', 'osName': 'iOS', 'browserWidth': '640', 'touch': '1', 'jqm': 'A-Grade', 'aacSupport': '0', 'markupMemoryLimit': '', 'scriptingSupport': '', 'cookieSupport': '1', 'isBlackberry': '0', 'isBada': '0', 'isSmartPhone': '1', 'isOSX': '1', 'category': 'smartphone', 'screenWidth': '640', 'uaQueried': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/********* Mobile/15E148 Safari/604.1', 'isSymbian': '0', 'tablet': '0', 'eReader': '0', 'inputDevices': '', 'markupSupport': '5.0', 'colorDepth': '24', 'isiOS': '1', 'hardwareVersion': '', 'vendor': 'Apple', 'inches': '0.0', 'isRIM': '0', 'phone': '1', 'game': '0', 'httpsSupport': '1', 'osVersion': '17.6', 'cssSupport': '', 'isProprietary': '0', 'isWindows': '0', 'mp3Support': '0', 'os': 'iOS', 'isAndroid': '0', 'isLinux': '0', 'screenHeight': '960', 'isMobileDevice': '1', 'model': 'iPhone', 'browserHeight': '960'},questions:{length: 0}};DQ.device.isChrome=(window.chrome);var BAK={questions:{length:0}};BAK.device=jQuery.extend(true,{},DQ.device);
          </script>
          <style data-id="immersive-translate-default-injected-css">:root {
  --immersive-translate-theme-underline-borderColor: #72ece9;
  --immersive-translate-theme-nativeUnderline-borderColor: #72ece9;
  --immersive-translate-theme-nativeDashed-borderColor: #72ece9;
  --immersive-translate-theme-nativeDotted-borderColor: #72ece9;
  --immersive-translate-theme-highlight-backgroundColor: #ffff00;
  --immersive-translate-theme-dashed-borderColor: #59c1bd;
  --immersive-translate-theme-blockquote-borderColor: #cc3355;
  --immersive-translate-theme-thinDashed-borderColor: #ff374f;
  --immersive-translate-theme-dashedBorder-borderColor: #94a3b8;
  --immersive-translate-theme-dashedBorder-borderRadius: 0;
  --immersive-translate-theme-solidBorder-borderColor: #94a3b8;
  --immersive-translate-theme-solidBorder-borderRadius: 0;
  --immersive-translate-theme-dotted-borderColor: #94a3b8;
  --immersive-translate-theme-wavy-borderColor: #72ece9;
  --immersive-translate-theme-dividingLine-borderColor: #94a3b8;
  --immersive-translate-theme-grey-textColor: #2f4f4f;
  --immersive-translate-theme-marker-backgroundColor: #fbda41;
  --immersive-translate-theme-marker-backgroundColor-rgb: 251, 218, 65;
  --immersive-translate-theme-marker2-backgroundColor: #ffff00;
  --immersive-translate-theme-opacity-opacity: 10;
}

.immersive-translate-target-translation-pre-whitespace {
  white-space: pre-wrap !important;
}

.immersive-translate-pdf-target-container {
  position: absolute;
  background-color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    sans-serif;
  top: 0;
  width: 600px;
  height: 100%;
  z-index: 2;
  line-height: 1.3;
  font-size: 16px;
}

.immersive-translate-pdf-target-container .immersive-translate-target-wrapper {
  color: rgb(0, 0, 0);
  white-space: normal;
  position: absolute;
}

.immersive-translate-pdf-target-container .immersive-translate-target-wrapper font {
  color: inherit;
  white-space: inherit;
  position: unset;
}

.immersive-translate-state-dual>br {
  display: none;
}

.immersive-translate-target-translation-block-wrapper {
  margin: 8px 0 !important;
  display: inline-block;
}

.immersive-translate-target-translation-pdf-block-wrapper {
  margin: 0 !important;
  display: block;
}

.immersive-translate-target-translation-theme-grey-inner {
  color: var(--immersive-translate-theme-grey-textColor);
}

.immersive-translate-target-translation-inline-wrapper {}

.immersive-translate-target-translation-theme-underline-inner {
  border-bottom: 1px solid var(--immersive-translate-theme-underline-borderColor) !important;
}

.immersive-translate-target-translation-theme-nativeUnderline-inner {
  text-decoration: underline var(--immersive-translate-theme-nativeUnderline-borderColor) !important;
}

.immersive-translate-target-translation-block-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-dashedBorder-borderRadius) !important;
  padding: 6px;
  margin-top: 2px;
  display: block;
}

.immersive-translate-target-translation-inline-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-dashedBorder-borderRadius) !important;
  padding: 2px;
}

.immersive-translate-target-translation-block-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-solidBorder-borderRadius) !important;
  padding: 6px;
  margin-top: 2px;
  display: block;
}

.immersive-translate-target-translation-inline-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(--immersive-translate-theme-solidBorder-borderRadius) !important;
  padding: 2px;
}

.immersive-translate-target-translation-theme-nativeDashed-inner {
  text-decoration: dashed underline var(--immersive-translate-theme-nativeDashed-borderColor) !important;
}

.immersive-translate-target-translation-theme-thinDashed-inner {
  border-bottom: 1px dashed var(--immersive-translate-theme-thinDashed-borderColor) !important;
}

.immersive-translate-target-translation-theme-dotted-inner {
  background-repeat: repeat-x;
  background-image: linear-gradient(to right,
      var(--immersive-translate-theme-dotted-borderColor) 30%,
      rgba(255, 255, 255, 0) 0%);
  background-position: bottom;
  background-size: 5px 1px;
  background-repeat: repeat-x;
  padding-bottom: 3px;
}

.immersive-translate-target-translation-theme-nativeDotted-inner {
  text-decoration: dotted underline var(--immersive-translate-theme-nativeDotted-borderColor) !important;
}

.immersive-translate-target-translation-theme-wavy-inner {
  text-decoration: wavy underline var(--immersive-translate-theme-wavy-borderColor) !important;
}

.immersive-translate-target-translation-theme-dashed-inner {
  background-repeat: repeat-x !important;
  background: linear-gradient(to right,
      var(--immersive-translate-theme-dashed-borderColor) 0%,
      var(--immersive-translate-theme-dashed-borderColor) 50%,
      transparent 50%,
      transparent 100%) repeat-x left bottom;
  background-size: 8px 2px;
  padding-bottom: 2px;
}

.immersive-translate-target-translation-block-wrapper-theme-dividingLine::before {
  content: "";
  display: block;
  max-width: 80px;
  width: 10%;
  border-top: 1px dashed var(--immersive-translate-theme-dividingLine-borderColor);
  padding-top: 8px;
}

.immersive-translate-target-translation-inline-wrapper-theme-dividingLine::before {
  content: "";
  border-left: 1px dashed var(--immersive-translate-theme-dividingLine-borderColor);
  max-height: 16px;
  height: 16px;
  padding-left: 8px;
}

.immersive-translate-target-translation-theme-highlight-inner {
  background: var(--immersive-translate-theme-highlight-backgroundColor);
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

.immersive-translate-target-translation-block-wrapper-theme-marker {
  line-height: 1.5em;
}

.immersive-translate-target-translation-theme-marker2-inner {
  font-weight: bold;
  text-shadow: 10px 0px 3px var(--immersive-translate-theme-marker2-backgroundColor),
    16px 3px 9px var(--immersive-translate-theme-marker2-backgroundColor),
    2px 0px 6px var(--immersive-translate-theme-marker2-backgroundColor),
    -12px 0px 12px var(--immersive-translate-theme-marker2-backgroundColor) !important;
}

.immersive-translate-target-translation-theme-marker-inner {
  /* TODO: add more texture */
  background: linear-gradient(to right,
      rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.1),
      rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 3%,
      rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 35%,
      rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 70%,
      rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.8) 95%,
      rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.3));
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

.immersive-translate-target-translation-theme-weakening {
  opacity: 0.618 !important;
}

.immersive-translate-target-translation-theme-italic {
  font-style: italic !important;
}

.immersive-translate-target-translation-theme-bold {
  font-weight: bold !important;
}

.immersive-translate-target-translation-block-wrapper-theme-paper {
  margin: 8px 0;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  padding: 16px 32px;
  display: block;
}

.immersive-translate-target-translation-block-wrapper-theme-blockquote {
  border-left: 4px solid var(--immersive-translate-theme-blockquote-borderColor) !important;
  padding-left: 12px !important;
  margin-top: 4px;
  margin-bottom: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  display: block;
}

.immersive-translate-target-translation-theme-mask-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
}

[data-immersive-translate-root-translation-theme="none"] .immersive-translate-target-translation-theme-mask-inner {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="mask"] .immersive-translate-target-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
}

.immersive-translate-target-translation-theme-mask-inner:hover {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="mask"] .immersive-translate-target-inner:hover {
  filter: none !important;
}

/* opacity theme start */

.immersive-translate-target-translation-theme-opacity-inner {
  filter: opacity(calc(var(--immersive-translate-theme-opacity-opacity) * 1%)) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
}

[data-immersive-translate-root-translation-theme="none"] .immersive-translate-target-translation-theme-opacity-inner {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="opacity"] .immersive-translate-target-inner {
  filter: opacity(calc(var(--immersive-translate-theme-opacity-opacity) * 1%)) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
}

.immersive-translate-target-translation-theme-opacity-inner:hover {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="opacity"] .immersive-translate-target-inner:hover {
  filter: none !important;
}

/* opacity theme end */

/* vertical css , please remain it in the last one. */
.immersive-translate-target-translation-vertical-block-wrapper {
  margin: 0px 8px !important;
}

.immersive-translate-text {
  font-size: 15px !important;
}

.immersive-translate-error-toast {
  position: fixed;
  top: 5%;
  z-index: 99999999;
  left: 0;
  right: 0;
  margin: auto;
  max-width: 300px;
  padding: 16px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

@media all and (min-width: 750px) {
  .immersive-translate-error-toast {
    max-width: 400px;
  }
}

.immersive-translate-error {}

.immersive-translate-clickable-button {
  cursor: pointer;
}

.immersive-translate-help-button {
  cursor: help;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

.immersive-translate-loading-text:before {
  content: "...";
}

.immersive-translate-loading-none {}

/* dark mode for loading */

@media only screen and (prefers-color-scheme: dark) {
  .immersive-translate-loading {
    border: 2px rgba(255, 255, 255, 0.25) solid !important;
    border-top: 2px rgba(255, 255, 255, 1) solid !important;
  }
}

.immersive-translate-error-wrapper {
  position: relative;
  display: inline-flex;
  padding: 6px;
  margin: 0 12px;
}

.immersive-translate-tooltip {
  position: relative;
  display: inline-flex;
  /* little indicater to indicate it's hoverable */
}

.immersive-translate-tooltip:hover .immersive-translate-tooltip-content {
  display: block;
}

.immersive-translate-tooltip:hover+.immersive-translate-tooltip-content {
  display: block;
}

.immersive-translate-tooltip-content-table {
  left: unset !important;
  bottom: unset !important;
  transform: translate(-10%, 50%) !important;
}

.immersive-translate-tooltip-content {
  /* here's the magic */
  position: absolute;
  z-index: 100000000000;

  left: 50%;
  bottom: 0;
  transform: translate(-50%, 110%);
  line-height: 1;
  /* and add a small left margin */

  /* basic styles */
  width: max-content;
  max-width: 250px;
  word-wrap: break-word;
  white-space: pre-line;
  padding: 10px;
  border-radius: 10px;
  background: #000C;
  color: #fff;
  text-align: center;
  font-size: 14px;
  display: none;
  /* hide by default */
}

.immersive-translate-tooltip:hover:before {
  display: block;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}</style><style data-id="immersive-translate-dynamic-injected-css">
</style><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}


.immersive-translate-search-recomend {
  border: 1px solid #dadce0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
  font-size: 16px;
}
.immersive-translate-search-enhancement-en-title {
  color: #4d5156;
}
/* dark */
@media (prefers-color-scheme: dark) {
  .immersive-translate-search-recomend {
    border: 1px solid #3c4043;
  }
  .immersive-translate-close-action svg {
    fill: #bdc1c6;
  }

.immersive-translate-search-enhancement-en-title {
  color: #bdc1c6;
}
}


.immersive-translate-search-settings {
  position: absolute;
  top: 16px;
  right: 16px;
  cursor: pointer;
}

.immersive-translate-search-recomend::before {
  /* content: " "; */
  /* width: 20px; */
  /* height: 20px; */
  /* top: 16px; */
  /* position: absolute; */
  /* background: center / contain url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAxlBMVEUAAADpTInqTIjpSofnSIfqS4nfS4XqS4nqTIjsTYnrTInqTIroS4jvQIDqTIn////+/v7rSYjpTIn8/v7uaZzrTIr9/f3wfansWJL88/b85e73qc39+/v3xNnylrvrVI/98fb62Obva5/8+fr76vH4y9zpSIj74e353Oj1ocTzm77xhK/veKbtYpjsXJTqU47oTInxjrXyh7L99fj40eH2ttH1udD3sc31ssz1rMnykLXucqPtbqD85e/1xdn2u9DzqcXrUY6FaJb8AAAADnRSTlMA34BgIM8Q37/fz7+/EGOHcVQAAAGhSURBVDjLhZPncuowEEZFTW7bXVU7xsYYTO/p7bb3f6lICIOYJOT4h7/VnFmvrBFjrF3/CR/SajBHswafctG0Qg3O8O0Xa8BZ6uw7eLjqr30SofCDVSkemMinfL1ecy20r5ygR5zz3ArcAqJExPTPKhDENEmS30Q9+yo4lEQkqVTiIEAHCT10xWERRdH0Bq0aCOPZNDV3s0xaYce1lHEoDHU8wEh3qRJypNcTAeKUIjgKMeGLDoRCLVLTVf+Ownj8Kk6H9HM6QXPgYjQSB0F00EJEu10ILQrs/QeP77BSSr0MzLOyuJJQbnUoOOIUI/A8EeJk9E4YUHUWiRyTVKGgQUB8/3e/NpdGlfI+FMQyWsCBWyz4A/ZyHXyiiz0Ne5aGZssoxRmcChw8/EFKQ5JwwkUo3FRT5yXS7q+Y/rHDZmFktzpGMvO+5QofA4FPpEmGw+EWRCFvnaof7Zhe8NuYSLR0xErKLThUSs8gnODh87ssy6438yzbLzxl012HS19vfCf3CNhnbWOL1eEsDda+gDPUvri8tSZzNFrwIZf1NmNvqC1I/t8j7nYAAAAASUVORK5CYII='); */
}

.immersive-translate-search-title {}

.immersive-translate-search-title-wrapper {}

.immersive-translate-search-time {
  font-size: 12px;
  margin: 4px 0 24px;
  color: #70757a;
}

.immersive-translate-expand-items {
  display: none;
}

.immersive-translate-search-more {
  margin-top: 16px;
  font-size: 14px;
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 1000000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #888;
  border-radius: 10px;
  width: 80%;
  max-width: 500px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
}

.immersive-translate-modal-title {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 20px;
  color: hsl(205, 20%, 32%);
}

.immersive-translate-modal-body {
  color: hsl(205, 20%, 32%)
}

.immersive-translate-close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  margin-top: 20px;
}

.immersive-translate-btn {
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 5px;
  padding: 10px 20px;
  font-size: 1rem;
  border-radius: 5px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}


.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #007bff;
  -webkit-tap-highlight-color: rgba(0, 0, 0, .1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px 16px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}
</style></head><body class="survey-page touch" data-device="smartphone" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div class="coverpage hidden" style="display: none;"><div class="spinner-container"><div class="loading-container"><div id="floatingCirclesG">	<div class="f_circleG" id="frotateG_01"></div><div class="f_circleG" id="frotateG_02"></div><div class="f_circleG" id="frotateG_03"></div><div class="f_circleG" id="frotateG_04"></div><div class="f_circleG" id="frotateG_05"></div><div class="f_circleG" id="frotateG_06"></div><div class="f_circleG" id="frotateG_07"></div><div class="f_circleG" id="frotateG_08"></div></div></div></div></div>
                  <script type="text/javascript" src="./checked0_files/default.js.下载"></script>
        <script type="text/javascript" src="./checked0_files/rp_text.js.下载"></script>






<div id="survey" class="survey-container" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<div class="survey-section" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div class="page-header"></div>
<div class="page-cover" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div>


<div id="surveyHeader" class="survey-header group" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
</div>
<div class="survey-section" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<div class="survey-body" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<div class="survey-header" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">

<div class="infoBar" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<div class="survey-progress-container" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<!--div class="text progress-text" style="@if not gv.survey.root.styles.ss.hideProgressBar visibility:hidden; @endif">progress bar : 94%</div-->
<div class="text progress-text" style="@if not gv.survey.root.styles.ss.hideProgressBar visibility:hidden; @endif" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">94%</div>
</div>
</div>
<!-- /.progress-bar -->
<div class="progress-bar progress-top" title="progress bar - 94% complete" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<div class="progress-box-outer" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span class="progress-box-completed" style="width: 94%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></span></div>
</div>
<!-- /.surveyProgressBar -->
</div>
<!-- /.survey-header -->

<form id="primary" name="primary" method="post" action="https://surveys.lifepointspanel.com/survey/bor/v3/EMEA/92209613/92209613_D" enctype="application/x-www-form-urlencoded" novalidate="" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<input type="hidden" name="_v2_counter" value="57">
<!-- @@XYZZY@@ -->
<div class="survey-error" role="alert" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<h1 class="survey-error-text" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">There were problems with some of the data you entered in the survey. You will find the questions with errors below; please follow the instructions attached to each question.<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">您在调查中输入的某些数据存在问题。您将在下面找到带有错误的问题;请按照每个问题所附的说明进行作。</font></font></font></h1>
</div>
<!-- /.survey-error --><div class="QA_QTQI" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><h1 title="question" class="question-text" id="question_text_QBAR" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">What’s stopping you buying more Dairy?<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">是什么阻止您购买更多的乳制品？</font></font></font></h1>
<!-- /.question-text --> <h2 title="instructions" class="instruction-text" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></h2>
<!-- /.instruction-text --></div><div class="question-error" role="alert" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<h2 id="QBAR_error" class="question-error-text" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Please check at least 1 box in this column (you checked 0).<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">请至少选中此列中的 1 个框（您选中了 0）。</font></font></font></h2>
</div>
<!-- /.question-error --><div id="sq-QARTS-container-QBAR" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div id="sq-QARTS-container-QBAR_qartstool" class="qartstool" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-test="main-contain" class="_rowpicker" dir="ltr" style="box-sizing: border-box; line-height: normal; white-space: normal; position: relative; display: block; width: 100%; margin: 0px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div class="__qarts_flexgrid_column" dir="ltr" style="box-sizing: border-box; position: relative; display: inline-block; width: 100%; flex-wrap: wrap; padding: 30px; -webkit-box-pack: start; justify-content: flex-start; -webkit-box-align: stretch; align-items: stretch; vertical-align: top;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: top; width: 50%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 14; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="0" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Cost<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">成本</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 13; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="1" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Just not thinking about it<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">只是不去想</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 12; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="2" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Not available<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">不可用</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 11; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="3" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Not enough choice<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">选择不够</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 10; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="4" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Not inspired to cook with it<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">没有灵感用它来做饭</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 9; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="5" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Health<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">健康</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div></div><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: top; width: 50%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 14; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="6" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Sustainability<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">可持续性</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 13; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="7" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Welfare<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">福利</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 12; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="8" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Prefer the taste of other proteins<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">更喜欢其他蛋白质的味道</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 11; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="9" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Trying to eat less meat / dairy<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">尝试少吃肉/奶制品</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 10; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="10" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Inconvenient<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">不便</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inline-block; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 9; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><div for="11" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Not confident cooking with it<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><br><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-block-wrapper-theme-none immersive-translate-target-translation-block-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">没有信心用它做饭</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div></div><div style="box-sizing: border-box; position: relative; width: 100%; display: flex; flex-wrap: wrap;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 14; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(189, 189, 189);"><path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(189, 189, 189);"><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"></path></svg></div><label for="12" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; z-index: 5000; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">Other –<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">其他–</font></font></font></span></label></div></div><div dir="ltr" style="font: inherit; white-space: normal; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; overflow: hidden; padding-top: 15px; margin-top: 10px; z-index: 888; text-align: left;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; width: 100%; line-height: 1.38;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><label style="z-index: -1; -webkit-font-smoothing: antialiased; transform-origin: left top; transition: transform 250ms, width 250ms, opacity 100ms; box-sizing: border-box; position: absolute; left: 0px; right: 0px; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; font-style: inherit; font-variant: inherit; font-weight: 400; font-stretch: inherit; font-size: 18px; line-height: 1.5; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; opacity: 1; transform: none; bottom: 2px; width: 100%; color: rgb(158, 158, 158);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">please specify<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">请注明</font></font></font></label><input id="12" type="text" autocomplete="off" step="any" value="" style="position: relative; display: inline-block; vertical-align: bottom; width: 100%; padding: 0px; margin: 0px; outline: none; border: none; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: 18px; line-height: 1.5; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; color: rgb(33, 33, 33); background: rgba(255, 255, 255, 0); cursor: auto;"><div style="position: relative; width: 100%; height: 2px;"><div style="transition: 250ms; position: absolute; top: 0px; left: 0px; right: 0px; width: 100%; border-bottom: 1px solid rgb(158, 158, 158); border-top-color: rgb(158, 158, 158); border-right-color: rgb(158, 158, 158); border-left-color: rgb(158, 158, 158);"></div><div style="opacity: 0; position: absolute; top: 0px; left: 0px; right: 0px; width: 100%; border-bottom: 2px solid rgb(255, 152, 0); transition: opacity 250ms, transform 250ms; transform: scale3d(0, 1, 1); border-top-color: rgb(255, 152, 0); border-right-color: rgb(255, 152, 0); border-left-color: rgb(255, 152, 0);"></div></div></div></div></div><div dir="ltr" tabindex="-1" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div><div style="transition: none; box-sizing: border-box; position: relative; display: inherit; width: 100%; opacity: 1; vertical-align: inherit; padding: 0px; text-align: left; z-index: 13; transform: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="width: 100%; display: inherit;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div dir="ltr" style="box-sizing: border-box; position: relative; vertical-align: middle; width: 100%; outline: rgb(238, 238, 238) solid 1px; background-color: rgb(255, 255, 255); line-height: normal; white-space: normal; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; box-shadow: none; min-height: auto; display: flex;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: background-color 250ms; box-sizing: border-box; position: relative; display: inherit; vertical-align: middle; width: 100%; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; -webkit-box-direction: normal; -webkit-box-orient: vertical; flex-direction: column; background-color: rgb(255, 255, 255);" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: opacity 250ms; box-sizing: border-box; position: relative; width: 100%; padding: 16px 12px; display: inherit; vertical-align: middle; -webkit-box-orient: vertical; -webkit-box-direction: normal; flex-direction: column; -webkit-box-pack: center; justify-content: center; -webkit-box-flex: 1; flex: 1 0 auto; min-width: 0px; min-height: 0px; opacity: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; background-color: transparent;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="transition: 500ms; box-sizing: border-box; display: inline-block; width: 100%; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: normal; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; background-color: inherit; white-space: nowrap; padding-left: 25px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; transition: opacity 250ms 100ms, position 250ms 100ms, -webkit-transform 250ms; opacity: 1; margin-left: -25px; width: 31px; height: 31px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; opacity: 0; fill: rgb(255, 152, 0);"><path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zm0-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"></path></svg><svg focusable="false" viewBox="0 0 24 24" style="display: inline-block; max-height: 100%; max-width: 100%; user-select: none; transition: 250ms; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; fill: rgb(255, 152, 0);"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"></path></svg></div><div for="13" style="width: inherit; max-width: 100%; box-sizing: border-box; position: relative; display: inline-block; vertical-align: middle; font-style: inherit; font-variant: inherit; font-weight: inherit; font-stretch: inherit; font-size: inherit; line-height: 1.25; font-family: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; white-space: normal; text-align: left; padding-left: 5px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span style="display: block; transition: color 250ms; width: inherit; font-size: 18px; color: rgb(33, 33, 33); font-weight: 500; overflow-wrap: break-word; text-size-adjust: 100%;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">None of these<font class="notranslate immersive-translate-target-wrapper" lang="zh-CN" data-immersive-translate-translation-element-mark="1"><font class="notranslate" data-immersive-translate-translation-element-mark="1">&nbsp;</font><font class="notranslate immersive-translate-target-translation-theme-none immersive-translate-target-translation-inline-wrapper-theme-none immersive-translate-target-translation-inline-wrapper" data-immersive-translate-translation-element-mark="1"><font class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-none-inner" data-immersive-translate-translation-element-mark="1">这些都不是</font></font></font></span></div></div></div></div><div dir="ltr" tabindex="0" style="user-select: none; -webkit-tap-highlight-color: transparent; transition: -webkit-box-shadow 250ms; box-sizing: border-box; position: absolute; display: inline-block; vertical-align: middle; width: 100%; padding: 0px; outline: none; background-color: rgba(255, 255, 255, 0); touch-action: manipulation; cursor: pointer; inset: 0px; z-index: 1;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div style="user-select: none; position: absolute; inset: 0px; display: block; overflow: hidden; background: rgba(255, 255, 255, 0); pointer-events: none;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div></div></div></div></div></div></div><div data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><span data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></span></div></div></div><div class="hidden answers"><div class="answers answers-table">

<table class="grid grid-table-mode setWidth" data-settings="force-desktop desktop-mode single-col table-mode" data-height="" summary="This table contains form elements to answer the survey question">
<tbody><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.0" id="ans10236.0.0" value="1" class="input checkbox QBAR_r1_" aria-labelledby="QBAR_r1_right QBAR_" aria-invalid="true" qartsid="QBAR_r1_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.0">Cost</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r1_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Cost
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.1" id="ans10236.0.1" value="1" class="input checkbox QBAR_r2_" aria-labelledby="QBAR_r2_right QBAR_" aria-invalid="true" qartsid="QBAR_r2_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.1">Just not thinking about it</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r2_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Just not thinking about it
</th>
</tr><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.2" id="ans10236.0.2" value="1" class="input checkbox QBAR_r3_" aria-labelledby="QBAR_r3_right QBAR_" aria-invalid="true" qartsid="QBAR_r3_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.2">Not available</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r3_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Not available
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.3" id="ans10236.0.3" value="1" class="input checkbox QBAR_r4_" aria-labelledby="QBAR_r4_right QBAR_" aria-invalid="true" qartsid="QBAR_r4_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.3">Not enough choice</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r4_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Not enough choice
</th>
</tr><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.4" id="ans10236.0.4" value="1" class="input checkbox QBAR_r5_" aria-labelledby="QBAR_r5_right QBAR_" aria-invalid="true" qartsid="QBAR_r5_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.4">Not inspired to cook with it</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r5_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Not inspired to cook with it
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.5" id="ans10236.0.5" value="1" class="input checkbox QBAR_r6_" aria-labelledby="QBAR_r6_right QBAR_" aria-invalid="true" qartsid="QBAR_r6_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.5">Health</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r6_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Health
</th>
</tr><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.6" id="ans10236.0.6" value="1" class="input checkbox QBAR_r7_" aria-labelledby="QBAR_r7_right QBAR_" aria-invalid="true" qartsid="QBAR_r7_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.6">Sustainability</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r7_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Sustainability
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.7" id="ans10236.0.7" value="1" class="input checkbox QBAR_r8_" aria-labelledby="QBAR_r8_right QBAR_" aria-invalid="true" qartsid="QBAR_r8_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.7">Welfare</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r8_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Welfare
</th>
</tr><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.8" id="ans10236.0.8" value="1" class="input checkbox QBAR_r9_" aria-labelledby="QBAR_r9_right QBAR_" aria-invalid="true" qartsid="QBAR_r9_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.8">Prefer the taste of other proteins</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r9_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Prefer the taste of other proteins
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.9" id="ans10236.0.9" value="1" class="input checkbox QBAR_r10_" aria-labelledby="QBAR_r10_right QBAR_" aria-invalid="true" qartsid="QBAR_r10_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.9">Trying to eat less meat / dairy</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r10_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Trying to eat less meat / dairy
</th>
</tr><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.10" id="ans10236.0.10" value="1" class="input checkbox QBAR_r11_" aria-labelledby="QBAR_r11_right QBAR_" aria-invalid="true" qartsid="QBAR_r11_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.10">Inconvenient</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r11_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Inconvenient
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.11" id="ans10236.0.11" value="1" class="input checkbox QBAR_r12_" aria-labelledby="QBAR_r12_right QBAR_" aria-invalid="true" qartsid="QBAR_r12_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.11">Not confident cooking with it</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r12_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Not confident cooking with it
</th>
</tr><tr class="row row-elements even hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.12" id="ans10236.0.12" value="1" class="input checkbox QBAR_r13_" aria-labelledby="QBAR_r13_right QBAR_" aria-invalid="true" qartsid="QBAR_r13_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.12">Other –</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r13_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
Other – <input type="text" name="oe10236.0" id="oe10236.0" value="" data-cell="QBAR_r13" size="25" class="input text-input oe oe-right QBAR_r13_OE" qartsid="QBAR_r13_OE" qartsqname="QBAR">
<script type="text/javascript">Survey.handleOpen("oe10236.0", "ans10236.0.12", "12");</script>
</th>
</tr><tr class="row row-elements odd hasError colCount-1">

<td class="cell nonempty element indent-1 desktop border-collapse groupingCols OneColumnEl hasError    clickableCell">
<span class="cell-sub-wrapper cell-legend-right">
<span class="cell-input cell-sub-column">
<input type="checkbox" name="ans10236.0.13" id="ans10236.0.13" value="1" class="exclusive input checkbox QBAR_r14_" aria-labelledby="QBAR_r14_right QBAR_" aria-invalid="true" qartsid="QBAR_r14_" qartsqname="QBAR">
</span>
<span class="cell-text cell-sub-column"><label for="ans10236.0.13">None of these</label> </span>
</span>
</td>
<th scope="row" id="QBAR_r14_right" class="cell nonempty legend row-legend row-legend-right row-legend-basic legend-level-1 desktop border-collapse ">
None of these
</th>
</tr></tbody>
</table>
<!-- /.grid -->
</div>
<!-- /.answers -->
</div>
<!-- /.question --><script type="text/javascript">
         if (! DQ.questions["QBAR"] ) {
            DQ.questions.QBAR = {label: "QBAR", rules: {},container: "sq-QARTS-container-QBAR",q: {"comment": "", "qa:winstamp": "https://surveys.lifepointspanel.com/s/local/qarts/images/gr/win_new.png", "qa:hreflink": "", "uid": 10236, "totalColCount": 2, "qa:nextbuttontext": "Continue", "cols": [{"index": 0, "qa:sldrleftendlabel": "", "uid": 10251, "open": 0, "exclusive": 0, "text": "", "qa:hreftitle": "", "qa:cdescription": "", "qa:rplaceholder": "", "label": "", "qa": ["", ""], "amount": 0, "qa:rdescription": "", "qa:sldrrightendlabel": "", "qa:rsubtitle": "", "qa:hreflink": "", "qa:csubtitle": "", "optional": 0, "openOptional": 0, "qa:rerror": ""}], "qa:ver": "v2364", "qa:remainstr": "", "qa:rowctz": false, "qa:leftendlabel": "", "qa:sldrrightendlabel": "", "qa:tool": "rp", "qa:mandatory": true, "qa:losestamp": "https://surveys.lifepointspanel.com/s/local/qarts/images/gr/lose_new.png", "rows": [{"exclusive": 0, "qa:rdelabelrt": "", "uid": 10237, "text": "Cost", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 0, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r1", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10238, "text": "Just not thinking about it", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 1, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r2", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10239, "text": "Not available", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 2, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r3", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10240, "text": "Not enough choice", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 3, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r4", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10241, "text": "Not inspired to cook with it", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 4, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r5", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10242, "text": "Health", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 5, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r6", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10243, "text": "Sustainability", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 6, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r7", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10244, "text": "Welfare", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 7, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r8", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10245, "text": "Prefer the taste of other proteins", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 8, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r9", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10246, "text": "Trying to eat less meat / dairy", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 9, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r10", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10247, "text": "Inconvenient", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 10, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r11", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10248, "text": "Not confident cooking with it", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 11, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r12", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 0, "qa:rdelabelrt": "", "uid": 10249, "text": "Other \u2013", "openOptional": 0, "openIndex": 0, "qa:hreflink": "", "open": 1, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 12, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r13", "openName": "oe10236.0", "qa:rplaceholder": "please specify", "qa:rsubtitle": "", "openValue": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}, {"exclusive": 1, "qa:rdelabelrt": "", "uid": 10250, "text": "None of these", "openOptional": 0, "qa:hreflink": "", "open": 0, "qa:rgrouptitle": "", "qa:rgroupimage": "", "index": 13, "qa:sldrrightendlabel": "", "qa:hreftitle": "", "amount": 0, "label": "r14", "qa:rplaceholder": "", "qa:rsubtitle": "", "qa:rdescription": "", "qa:rerror": "", "qa:rdelabellt": "", "optional": 0, "rightLegend": "", "qa:sldrleftendlabel": "", "qa": ["", ""], "qa:rgroupsubtitle": ""}], "qa:showrowdesc": true, "title": "What\u2019s stopping you buying more Dairy?", "qa:topstamp": "https://surveys.lifepointspanel.com/s/local/qarts/images/gr/most-popular_new.png", "qa:disliketext": "", "qa:hreftitle": "", "qa:collapseopen": true, "label": "QBAR", "qa:scrolltop": "1", "qa:rplaceholder": "", "noanswers": [], "qa:inputmandatory": true, "qa:sldrprefix": "", "haveLeftLegend": false, "qa:style": "g:auto", "type": "checkbox", "qa:rsubtitle": "", "qa:enableinput": false, "qa:colctz": false, "qa:stylesver": "v2364", "qa:showcoldesc": true, "errors": [["Please check at least 1 box in this column (you checked 0).", "col", 0]], "verifiers": {}, "qa:rerror": "", "device": {"category": "smartphone", "smartphone": true, "tablet": false, "otherMobile": false, "mobileDevice": true, "desktop": false, "width": "640", "iphone": true, "featurephone": false, "android": false, "height": "960", "blackberry": false}, "qa:autosubmit": true, "qa:liketext": "", "haveRightLegend": true, "qa:sldrsuffix": "", "qa:sldrleftendlabel": "", "qa:rightendlabel": "", "qa:timerexpiremsg": "", "qa:usedeciphergroups": "1", "qa:barfill": false, "choices": [{"index": 0, "qa:sldrleftendlabel": "", "uid": 10252, "open": 0, "exclusive": 0, "text": "", "qa:hreftitle": "", "qa:rplaceholder": "", "label": "", "qa": ["", ""], "amount": 0, "qa:rdescription": "", "qa:sldrrightendlabel": "", "qa:rsubtitle": "", "qa:hreflink": "", "optional": 0, "openOptional": 0, "qa:rerror": ""}], "qa:rdescription": "", "qa:atype": "text", "qa:rulesver": "v2364", "grouping": "cols"} ,debug: false};DQ.questions.QBAR.q.isQText = (DQ.questions.QBAR.q.type == "text");DQ.questions.QBAR.q.tool = "rp";DQ.questions.QBAR.q.toolsave = "rp";
                 DQ.questions.QBAR.q.atleast = "1";
              DQ.questions.QBAR.q["qa:usechecker"] = 0
              DQ.questions.QBAR.q["qa:gpalette"] = "g:default";DQ.questions.QBAR.q["qa:showbreakpoints"] = "0";DQ.questions.QBAR.q.optional = 0;DQ.questions.QBAR.q.tname = "qa";BAK.questions.QBAR = $ .extend(true,{},DQ.questions.QBAR);++DQ.questions.length;DQ.questions.QBAR.q.rowgroups = [];DQ.questions.QBAR.q.colgroups = [];
            Survey.question.qarts.setup(DQ.questions.QBAR);
            function setupAnswerMe() {
                var answerme = "btn_randomize";
                if ($ (answerme).length) {
                    $ (answerme).bind("mouseup", function () {
                        $ (".next2").remove();
                        $ (".back2").remove();
                        window.setTimeout(function () {
                            delete DQ.questions;
                            DQ.questions = {};
                            for ( x in BAK.questions ) {
                                if (typeof BAK.questions[x] === "object" ) {
                                    $ ("" + BAK.questions[x].container + "_qartstool").empty();
                                    var qn = BAK.questions[x].label;
                                    DQ.questions[x] = $ .extend(true,{},BAK.questions[x]); ;
                                    Survey.question.qarts.setup(DQ.questions[x]);
                                }
                            }
                        }, 50);
                    });
                } else {
                }
            }
            $ (function() {
              //console.log( "ready!",DQ.questions.QBAR );
              //Setup Answer me button
              if ( DQ.questions.QBAR.debug ) {
                  setupAnswerMe();                  
              }
            });
         }
     </script><script type="text/javascript">
setupExclusive('cols','ans10236.0.13');
</script>
<!-- #surveyButtons -->
<div class="survey-buttons" style="visibility: visible;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">
<input type="submit" name="continue" id="btn_continue" class="button continue survey-button interface-btn" value="Continue »" onclick="var i = document.createElement(&#39;input&#39;); i.setAttribute(&#39;type&#39;, &#39;hidden&#39;);i.setAttribute(&#39;value&#39;, &#39;1&#39;);i.setAttribute(&#39;name&#39;, &#39;__has_javascript&#39;);document.forms.primary.appendChild(i);" style="visibility: hidden; display: none;"><input type="button" name="continue" id="btn_continue_QBAR" class="button continue survey-button interface-btn next2" value="Continue »" style="display: none; opacity: 1;">

</div><input type="hidden" name="state" value="220daffb-8d40-4dd3-8790-08690f225a76">
<input type="hidden" name="start_time" value="1744037961">
<input type="hidden" name="_ptime" value="0">
<script>addHandler(Survey.capturePreciseTime)</script><script>
$ (window).on("load", function() { setTimeout(function() { Survey.recordPageTiming("bor/v3/EMEA/92209613/92209613_D", "ssg5ua1hbccezyku", "a3pxnuyrnsewbnn5");}, 100); });
</script>



</div></form>
<!-- /.survey-body -->
</div>
<div class="survey-section" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><div id="surveyFooter" class="survey-footer" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88">

<div class="footer survey-footer-text" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div></div></div>
</div>
<div id="detectBreakpoint" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"></div>
<!-- /.survey-container -->


</div></div><div id="quickSelectPanel" style="position: fixed; top: 100px; right: 10px; z-index: 99999; width: 40px; height: 40px;" data-immersive-translate-effect="1" data-immersive_translate_walked="cd51fbb0-75dc-4c3c-8326-3dcd546d8e88"><button style="width: 100%; height: 100%; background: rgb(255, 105, 180); border: none; border-radius: 50%; cursor: pointer; padding: 0px; display: flex; align-items: center; justify-content: center; box-shadow: rgba(0, 0, 0, 0.2) 0px 2px 5px;"><img src="chrome-extension://iohbbndikndbefcfdihphjbahkblbdjg/icons/icon.svg" style="width: 24px; height: 24px;"></button></div></body><div id="immersive-translate-popup" style="all: initial"><template shadowrootmode="open"><style>@charset "UTF-8";
/*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
/**
 * Theme: default
 */
#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}
@media (min-width: 576px) {
  #mount {
    --font-size: 17px;
  }
}
@media (min-width: 768px) {
  #mount {
    --font-size: 18px;
  }
}
@media (min-width: 992px) {
  #mount {
    --font-size: 19px;
  }
}
@media (min-width: 1200px) {
  #mount {
    --font-size: 20px;
  }
}

@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3);
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3.5);
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 4);
  }
}

@media (min-width: 576px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}
@media (min-width: 992px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.75);
  }
}
@media (min-width: 1200px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 2);
  }
}

dialog > article {
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
}
@media (min-width: 576px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 3);
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}

a {
  --text-decoration: none;
}
a.secondary,
a.contrast {
  --text-decoration: underline;
}

small {
  --font-size: 0.875em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  --font-weight: 700;
}

h1 {
  --font-size: 2rem;
  --typography-spacing-vertical: 3rem;
}

h2 {
  --font-size: 1.75rem;
  --typography-spacing-vertical: 2.625rem;
}

h3 {
  --font-size: 1.5rem;
  --typography-spacing-vertical: 2.25rem;
}

h4 {
  --font-size: 1.25rem;
  --typography-spacing-vertical: 1.874rem;
}

h5 {
  --font-size: 1.125rem;
  --typography-spacing-vertical: 1.6875rem;
}

[type="checkbox"],
[type="radio"] {
  --border-width: 2px;
}

[type="checkbox"][role="switch"] {
  --border-width: 3px;
}

thead th,
thead td,
tfoot th,
tfoot td {
  --border-width: 3px;
}

:not(thead, tfoot) > * > td {
  --font-size: 0.875em;
}

pre,
code,
kbd,
samp {
  --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
    "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

kbd {
  --font-weight: bolder;
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --background-color: #fff;
  --color: hsl(205deg, 20%, 32%);
  --h1-color: hsl(205deg, 30%, 15%);
  --h2-color: #24333e;
  --h3-color: hsl(205deg, 25%, 23%);
  --h4-color: #374956;
  --h5-color: hsl(205deg, 20%, 32%);
  --h6-color: #4d606d;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: hsl(205deg, 20%, 94%);
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 90%, 32%);
  --primary-focus: rgba(16, 149, 193, 0.125);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 20%, 32%);
  --secondary-focus: rgba(89, 107, 120, 0.125);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 30%, 15%);
  --contrast-hover: #000;
  --contrast-focus: rgba(89, 107, 120, 0.125);
  --contrast-inverse: #fff;
  --mark-background-color: #fff2ca;
  --mark-color: #543a26;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: transparent;
  --form-element-border-color: hsl(205deg, 14%, 68%);
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: transparent;
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
  --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #c62828;
  --form-element-invalid-active-border-color: #d32f2f;
  --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
  --form-element-valid-border-color: #388e3c;
  --form-element-valid-active-border-color: #43a047;
  --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
  --switch-background-color: hsl(205deg, 16%, 77%);
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: hsl(205deg, 18%, 86%);
  --range-active-border-color: hsl(205deg, 16%, 77%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: #f6f8f9;
  --code-background-color: hsl(205deg, 20%, 94%);
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 40%, 50%);
  --code-property-color: hsl(185deg, 40%, 40%);
  --code-value-color: hsl(40deg, 20%, 50%);
  --code-comment-color: hsl(205deg, 14%, 68%);
  --accordion-border-color: var(--muted-border-color);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: var(--background-color);
  --card-border-color: var(--muted-border-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
    0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
    0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
  --card-sectionning-background-color: #fbfbfc;
  --dropdown-background-color: #fbfbfc;
  --dropdown-border-color: #e1e6eb;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
  --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
  --progress-background-color: hsl(205deg, 18%, 86%);
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  color-scheme: light;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --background-color: #11191f;
    --color: hsl(205deg, 16%, 77%);
    --h1-color: hsl(205deg, 20%, 94%);
    --h2-color: #e1e6eb;
    --h3-color: hsl(205deg, 18%, 86%);
    --h4-color: #c8d1d8;
    --h5-color: hsl(205deg, 16%, 77%);
    --h6-color: #afbbc4;
    --muted-color: hsl(205deg, 10%, 50%);
    --muted-border-color: #1f2d38;
    --primary: hsl(195deg, 85%, 41%);
    --primary-hover: hsl(195deg, 80%, 50%);
    --primary-focus: rgba(16, 149, 193, 0.25);
    --primary-inverse: #fff;
    --secondary: hsl(205deg, 15%, 41%);
    --secondary-hover: hsl(205deg, 10%, 50%);
    --secondary-focus: rgba(115, 130, 140, 0.25);
    --secondary-inverse: #fff;
    --contrast: hsl(205deg, 20%, 94%);
    --contrast-hover: #fff;
    --contrast-focus: rgba(115, 130, 140, 0.25);
    --contrast-inverse: #000;
    --mark-background-color: #d1c284;
    --mark-color: #11191f;
    --ins-color: #388e3c;
    --del-color: #c62828;
    --blockquote-border-color: var(--muted-border-color);
    --blockquote-footer-color: var(--muted-color);
    --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --form-element-background-color: #11191f;
    --form-element-border-color: #374956;
    --form-element-color: var(--color);
    --form-element-placeholder-color: var(--muted-color);
    --form-element-active-background-color: var(
      --form-element-background-color
    );
    --form-element-active-border-color: var(--primary);
    --form-element-focus-color: var(--primary-focus);
    --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
    --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
    --form-element-disabled-opacity: 0.5;
    --form-element-invalid-border-color: #b71c1c;
    --form-element-invalid-active-border-color: #c62828;
    --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
    --form-element-valid-border-color: #2e7d32;
    --form-element-valid-active-border-color: #388e3c;
    --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
    --switch-background-color: #374956;
    --switch-color: var(--primary-inverse);
    --switch-checked-background-color: var(--primary);
    --range-border-color: #24333e;
    --range-active-border-color: hsl(205deg, 25%, 23%);
    --range-thumb-border-color: var(--background-color);
    --range-thumb-color: var(--secondary);
    --range-thumb-hover-color: var(--secondary-hover);
    --range-thumb-active-color: var(--primary);
    --table-border-color: var(--muted-border-color);
    --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
    --code-background-color: #18232c;
    --code-color: var(--muted-color);
    --code-kbd-background-color: var(--contrast);
    --code-kbd-color: var(--contrast-inverse);
    --code-tag-color: hsl(330deg, 30%, 50%);
    --code-property-color: hsl(185deg, 30%, 50%);
    --code-value-color: hsl(40deg, 10%, 50%);
    --code-comment-color: #4d606d;
    --accordion-border-color: var(--muted-border-color);
    --accordion-active-summary-color: var(--primary);
    --accordion-close-summary-color: var(--color);
    --accordion-open-summary-color: var(--muted-color);
    --card-background-color: #141e26;
    --card-border-color: var(--card-background-color);
    --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
      0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
      0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
      0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
      0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
      0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
    --card-sectionning-background-color: #18232c;
    --dropdown-background-color: hsl(205deg, 30%, 15%);
    --dropdown-border-color: #24333e;
    --dropdown-box-shadow: var(--card-box-shadow);
    --dropdown-color: var(--color);
    --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
    --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
    --progress-background-color: #24333e;
    --progress-color: var(--primary);
    --loading-spinner-opacity: 0.5;
    --tooltip-background-color: var(--contrast);
    --tooltip-color: var(--contrast-inverse);
    --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
    --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    color-scheme: dark;
  }
}
[data-theme="dark"] {
  --background-color: #11191f;
  --color: hsl(205deg, 16%, 77%);
  --h1-color: hsl(205deg, 20%, 94%);
  --h2-color: #e1e6eb;
  --h3-color: hsl(205deg, 18%, 86%);
  --h4-color: #c8d1d8;
  --h5-color: hsl(205deg, 16%, 77%);
  --h6-color: #afbbc4;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: #1f2d38;
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 80%, 50%);
  --primary-focus: rgba(16, 149, 193, 0.25);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 10%, 50%);
  --secondary-focus: rgba(115, 130, 140, 0.25);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 20%, 94%);
  --contrast-hover: #fff;
  --contrast-focus: rgba(115, 130, 140, 0.25);
  --contrast-inverse: #000;
  --mark-background-color: #d1c284;
  --mark-color: #11191f;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: #11191f;
  --form-element-border-color: #374956;
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: var(--form-element-background-color);
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
  --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #b71c1c;
  --form-element-invalid-active-border-color: #c62828;
  --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
  --form-element-valid-border-color: #2e7d32;
  --form-element-valid-active-border-color: #388e3c;
  --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
  --switch-background-color: #374956;
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: #24333e;
  --range-active-border-color: hsl(205deg, 25%, 23%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
  --code-background-color: #18232c;
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 30%, 50%);
  --code-property-color: hsl(185deg, 30%, 50%);
  --code-value-color: hsl(40deg, 10%, 50%);
  --code-comment-color: #4d606d;
  --accordion-border-color: var(--muted-border-color);
  --accordion-active-summary-color: var(--primary);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: #141e26;
  --card-border-color: var(--card-background-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
    0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
  --card-sectionning-background-color: #18232c;
  --dropdown-background-color: hsl(205deg, 30%, 15%);
  --dropdown-border-color: #24333e;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
  --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
  --progress-background-color: #24333e;
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  color-scheme: dark;
}

progress,
[type="checkbox"],
[type="radio"],
[type="range"] {
  accent-color: var(--primary);
}

/**
 * Document
 * Content-box & Responsive typography
 */
*,
*::before,
*::after {
  box-sizing: border-box;
  background-repeat: no-repeat;
}

::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

:where(#mount) {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  background-color: var(--background-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  overflow-wrap: break-word;
  cursor: default;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}

/**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
main {
  display: block;
}

#mount {
  width: 100%;
  margin: 0;
}
#mount > header,
#mount > main,
#mount > footer {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
}
@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 700px;
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    max-width: 1130px;
  }
}

/**
* Container
*/
.container,
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--spacing);
  padding-left: var(--spacing);
}

@media (min-width: 576px) {
  .container {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 700px;
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1130px;
  }
}

/**
 * Section
 * Responsive spacings for section
 */
section {
  margin-bottom: var(--block-spacing-vertical);
}

/**
* Grid
* Minimal grid system with auto-layout columns
*/
.grid {
  grid-column-gap: var(--grid-spacing-horizontal);
  grid-row-gap: var(--grid-spacing-vertical);
  display: grid;
  grid-template-columns: 1fr;
  margin: 0;
}
@media (min-width: 992px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
  }
}
.grid > * {
  min-width: 0;
}

/**
 * Horizontal scroller (<figure>)
 */
figure {
  display: block;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}
figure figcaption {
  padding: calc(var(--spacing) * 0.5) 0;
  color: var(--muted-color);
}

/**
 * Typography
 */
b,
strong {
  font-weight: bolder;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

address,
blockquote,
dl,
figure,
form,
ol,
p,
pre,
table,
ul {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: var(--font-size);
}

a,
[role="link"] {
  --color: var(--primary);
  --background-color: transparent;
  outline: none;
  background-color: var(--background-color);
  color: var(--color);
  -webkit-text-decoration: var(--text-decoration);
  text-decoration: var(--text-decoration);
  transition: background-color var(--transition), color var(--transition),
    box-shadow var(--transition), -webkit-text-decoration var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition),
    -webkit-text-decoration var(--transition);
}
a:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --color: var(--primary-hover);
  --text-decoration: underline;
}
a:focus,
[role="link"]:focus {
  --background-color: var(--primary-focus);
}
a.secondary,
[role="link"].secondary {
  --color: var(--secondary);
}
a.secondary:is([aria-current], :hover, :active, :focus),
[role="link"].secondary:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}
a.secondary:focus,
[role="link"].secondary:focus {
  --background-color: var(--secondary-focus);
}
a.contrast,
[role="link"].contrast {
  --color: var(--contrast);
}
a.contrast:is([aria-current], :hover, :active, :focus),
[role="link"].contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}
a.contrast:focus,
[role="link"].contrast:focus {
  --background-color: var(--contrast-focus);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  font-family: var(--font-family);
}

h1 {
  --color: var(--h1-color);
}

h2 {
  --color: var(--h2-color);
}

h3 {
  --color: var(--h3-color);
}

h4 {
  --color: var(--h4-color);
}

h5 {
  --color: var(--h5-color);
}

h6 {
  --color: var(--h6-color);
}

:where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
  ~ :is(h1, h2, h3, h4, h5, h6) {
  margin-top: var(--typography-spacing-vertical);
}

hgroup,
.headings {
  margin-bottom: var(--typography-spacing-vertical);
}
hgroup > *,
.headings > * {
  margin-bottom: 0;
}
hgroup > *:last-child,
.headings > *:last-child {
  --color: var(--muted-color);
  --font-weight: unset;
  font-size: 1rem;
  font-family: unset;
}

p {
  margin-bottom: var(--typography-spacing-vertical);
}

small {
  font-size: var(--font-size);
}

:where(dl, ol, ul) {
  padding-right: 0;
  padding-left: var(--spacing);
  -webkit-padding-start: var(--spacing);
  padding-inline-start: var(--spacing);
  -webkit-padding-end: 0;
  padding-inline-end: 0;
}
:where(dl, ol, ul) li {
  margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
}

:where(dl, ol, ul) :is(dl, ol, ul) {
  margin: 0;
  margin-top: calc(var(--typography-spacing-vertical) * 0.25);
}

ul li {
  list-style: square;
}

mark {
  padding: 0.125rem 0.25rem;
  background-color: var(--mark-background-color);
  color: var(--mark-color);
  vertical-align: baseline;
}

blockquote {
  display: block;
  margin: var(--typography-spacing-vertical) 0;
  padding: var(--spacing);
  border-right: none;
  border-left: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
  border-inline-start: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-end: none;
  border-inline-end: none;
}
blockquote footer {
  margin-top: calc(var(--typography-spacing-vertical) * 0.5);
  color: var(--blockquote-footer-color);
}

abbr[title] {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}

ins {
  color: var(--ins-color);
  text-decoration: none;
}

del {
  color: var(--del-color);
}

::-moz-selection {
  background-color: var(--primary-focus);
}

::selection {
  background-color: var(--primary-focus);
}

/**
 * Embedded content
 */
:where(audio, canvas, iframe, img, svg, video) {
  vertical-align: middle;
}

audio,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

:where(iframe) {
  border-style: none;
}

img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

:where(svg:not([fill])) {
  fill: currentColor;
}

svg:not(#mount) {
  overflow: hidden;
}

/**
 * Button
 */
button {
  margin: 0;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing);
}

[role="button"] {
  display: inline-block;
  text-decoration: none;
}

button,
input[type="submit"],
input[type="button"],
input[type="reset"],
[role="button"] {
  --background-color: var(--primary);
  --border-color: var(--primary);
  --color: var(--primary-inverse);
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
button:is([aria-current], :hover, :active, :focus),
input[type="submit"]:is([aria-current], :hover, :active, :focus),
input[type="button"]:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus),
[role="button"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--primary-hover);
  --border-color: var(--primary-hover);
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  --color: var(--primary-inverse);
}
button:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
[role="button"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--primary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
input[type="reset"] {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  cursor: pointer;
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
input[type="reset"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--secondary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
  --background-color: var(--contrast);
  --border-color: var(--contrast);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--contrast-hover);
  --border-color: var(--contrast-hover);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--contrast-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
input[type="reset"].outline {
  --background-color: transparent;
  --color: var(--primary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --background-color: transparent;
  --color: var(--primary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
input[type="reset"].outline {
  --color: var(--secondary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
  --color: var(--contrast);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}

:where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
:where(fieldset[disabled])
  :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
a[role="button"]:not([href]) {
  opacity: 0.5;
  pointer-events: none;
}

/**
 * Form elements
 */
input,
optgroup,
select,
textarea {
  margin: 0;
  font-size: 1rem;
  line-height: var(--line-height);
  font-family: inherit;
  letter-spacing: inherit;
}

input {
  overflow: visible;
}

select {
  text-transform: none;
}

legend {
  max-width: 100%;
  padding: 0;
  color: inherit;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type="checkbox"],
[type="radio"] {
  padding: 0;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

:-moz-focusring {
  outline: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-ms-expand {
  display: none;
}

[type="file"],
[type="range"] {
  padding: 0;
  border-width: 0;
}

input:not([type="checkbox"], [type="radio"], [type="range"]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
}

fieldset {
  margin: 0;
  margin-bottom: var(--spacing);
  padding: 0;
  border: 0;
}

label,
fieldset legend {
  display: block;
  margin-bottom: calc(var(--spacing) * 0.25);
  font-weight: var(--form-label-font-weight, var(--font-weight));
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  width: 100%;
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
}

input,
select,
textarea {
  --background-color: var(--form-element-background-color);
  --border-color: var(--form-element-border-color);
  --color: var(--form-element-color);
  --box-shadow: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --background-color: var(--form-element-active-background-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --border-color: var(--form-element-active-border-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
select:focus,
textarea:focus {
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}

input:not([type="submit"], [type="button"], [type="reset"])[disabled],
select[disabled],
textarea[disabled],
:where(fieldset[disabled])
  :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
  --background-color: var(--form-element-disabled-background-color);
  --border-color: var(--form-element-disabled-border-color);
  opacity: var(--form-element-disabled-opacity);
  pointer-events: none;
}

:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
  padding-right: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
  padding-inline-start: var(--form-element-spacing-horizontal) !important;
  -webkit-padding-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-inline-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
  background-image: var(--icon-valid);
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
  background-image: var(--icon-invalid);
}
:where(input, select, textarea)[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
:where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
  --border-color: var(--form-element-valid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
}
:where(input, select, textarea)[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}
:where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
  --border-color: var(--form-element-invalid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width)
    var(--form-element-invalid-focus-color) !important;
}

[dir="rtl"]
  :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
  background-position: center left 0.75rem;
}

input::placeholder,
input::-webkit-input-placeholder,
textarea::placeholder,
textarea::-webkit-input-placeholder,
select:invalid {
  color: var(--form-element-placeholder-color);
  opacity: 1;
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  margin-bottom: var(--spacing);
}

select::-ms-expand {
  border: 0;
  background-color: transparent;
}
select:not([multiple], [size]) {
  padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal);
  padding-inline-start: var(--form-element-spacing-horizontal);
  -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  background-image: var(--icon-chevron);
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}

[dir="rtl"] select:not([multiple], [size]) {
  background-position: center left 0.75rem;
}

:where(input, select, textarea) + small {
  display: block;
  width: 100%;
  margin-top: calc(var(--spacing) * -0.75);
  margin-bottom: var(--spacing);
  color: var(--muted-color);
}

label > :where(input, select, textarea) {
  margin-top: calc(var(--spacing) * 0.25);
}

/**
 * Form elements
 * Checkboxes & Radios
 */
[type="checkbox"],
[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 1.25em;
  height: 1.25em;
  margin-top: -0.125em;
  margin-right: 0.375em;
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0.375em;
  margin-inline-end: 0.375em;
  border-width: var(--border-width);
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
}
[type="checkbox"]::-ms-check,
[type="radio"]::-ms-check {
  display: none;
}
[type="checkbox"]:checked,
[type="checkbox"]:checked:active,
[type="checkbox"]:checked:focus,
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-checkbox);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}
[type="checkbox"] ~ label,
[type="radio"] ~ label {
  display: inline-block;
  margin-right: 0.375em;
  margin-bottom: 0;
  cursor: pointer;
}

[type="checkbox"]:indeterminate {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-minus);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}

[type="radio"] {
  border-radius: 50%;
}
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary-inverse);
  border-width: 0.35em;
  background-image: none;
}

[type="checkbox"][role="switch"] {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
  --color: var(--switch-color);
  width: 2.25em;
  height: 1.25em;
  border: var(--border-width) solid var(--border-color);
  border-radius: 1.25em;
  background-color: var(--background-color);
  line-height: 1.25em;
}
[type="checkbox"][role="switch"]:focus {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
}
[type="checkbox"][role="switch"]:checked {
  --background-color: var(--switch-checked-background-color);
  --border-color: var(--switch-checked-background-color);
}
[type="checkbox"][role="switch"]:before {
  display: block;
  width: calc(1.25em - (var(--border-width) * 2));
  height: 100%;
  border-radius: 50%;
  background-color: var(--color);
  content: "";
  transition: margin 0.1s ease-in-out;
}
[type="checkbox"][role="switch"]:checked {
  background-image: none;
}
[type="checkbox"][role="switch"]:checked::before {
  margin-left: calc(1.125em - var(--border-width));
  -webkit-margin-start: calc(1.125em - var(--border-width));
  margin-inline-start: calc(1.125em - var(--border-width));
}

[type="checkbox"][aria-invalid="false"],
[type="checkbox"]:checked[aria-invalid="false"],
[type="radio"][aria-invalid="false"],
[type="radio"]:checked[aria-invalid="false"],
[type="checkbox"][role="switch"][aria-invalid="false"],
[type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
[type="checkbox"][aria-invalid="true"],
[type="checkbox"]:checked[aria-invalid="true"],
[type="radio"][aria-invalid="true"],
[type="radio"]:checked[aria-invalid="true"],
[type="checkbox"][role="switch"][aria-invalid="true"],
[type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}

/**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
[type="color"]::-moz-focus-inner {
  padding: 0;
}
[type="color"]::-webkit-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}
[type="color"]::-moz-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  --icon-position: 0.75rem;
  --icon-width: 1rem;
  padding-right: calc(var(--icon-width) + var(--icon-position));
  background-image: var(--icon-date);
  background-position: center right var(--icon-position);
  background-size: var(--icon-width) auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
  background-image: var(--icon-time);
}

[type="date"]::-webkit-calendar-picker-indicator,
[type="datetime-local"]::-webkit-calendar-picker-indicator,
[type="month"]::-webkit-calendar-picker-indicator,
[type="time"]::-webkit-calendar-picker-indicator,
[type="week"]::-webkit-calendar-picker-indicator {
  width: var(--icon-width);
  margin-right: calc(var(--icon-width) * -1);
  margin-left: var(--icon-position);
  opacity: 0;
}

[dir="rtl"]
  :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  text-align: right;
}

[type="file"] {
  --color: var(--muted-color);
  padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
  border: 0;
  border-radius: 0;
  background: none;
}
[type="file"]::file-selector-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::file-selector-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-webkit-file-upload-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-ms-browse {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  margin-inline-start: 0;
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-ms-browse:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}

[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 1.25rem;
  background: none;
}
[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -webkit-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-moz-range-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -moz-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-ms-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -ms-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -moz-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]:hover,
[type="range"]:focus {
  --range-border-color: var(--range-active-border-color);
  --range-thumb-color: var(--range-thumb-hover-color);
}
[type="range"]:active {
  --range-thumb-color: var(--range-thumb-active-color);
}
[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-moz-range-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-ms-thumb {
  transform: scale(1.25);
}

input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  border-radius: 5rem;
  background-image: var(--icon-search);
  background-position: center left 1.125rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  -webkit-padding-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  padding-inline-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  background-position: center left 1.125rem, center right 0.75rem;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
  background-image: var(--icon-search), var(--icon-valid);
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
  background-image: var(--icon-search), var(--icon-invalid);
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  display: none;
}

[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  background-position: center right 1.125rem;
}
[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  background-position: center right 1.125rem, center left 0.75rem;
}

/**
 * Table
 */
:where(table) {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0;
}

th,
td {
  padding: calc(var(--spacing) / 2) var(--spacing);
  border-bottom: var(--border-width) solid var(--table-border-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  text-align: left;
  text-align: start;
}

tfoot th,
tfoot td {
  border-top: var(--border-width) solid var(--table-border-color);
  border-bottom: 0;
}

table[role="grid"] tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripped-background-color);
}

/**
 * Code
 */
pre,
code,
kbd,
samp {
  font-size: 0.875em;
  font-family: var(--font-family);
}

pre {
  -ms-overflow-style: scrollbar;
  overflow: auto;
}

pre,
code,
kbd {
  border-radius: var(--border-radius);
  background: var(--code-background-color);
  color: var(--code-color);
  font-weight: var(--font-weight);
  line-height: initial;
}

code,
kbd {
  display: inline-block;
  padding: 0.375rem 0.5rem;
}

pre {
  display: block;
  margin-bottom: var(--spacing);
  overflow-x: auto;
}
pre > code {
  display: block;
  padding: var(--spacing);
  background: none;
  font-size: 14px;
  line-height: var(--line-height);
}

code b {
  color: var(--code-tag-color);
  font-weight: var(--font-weight);
}
code i {
  color: var(--code-property-color);
  font-style: normal;
}
code u {
  color: var(--code-value-color);
  text-decoration: none;
}
code em {
  color: var(--code-comment-color);
  font-style: normal;
}

kbd {
  background-color: var(--code-kbd-background-color);
  color: var(--code-kbd-color);
  vertical-align: baseline;
}

/**
 * Miscs
 */
hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--muted-border-color);
  color: inherit;
}

[hidden],
template {
  display: none !important;
}

canvas {
  display: inline-block;
}

/**
 * Accordion (<details>)
 */
details {
  display: block;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  border-bottom: var(--border-width) solid var(--accordion-border-color);
}
details summary {
  line-height: 1rem;
  list-style-type: none;
  cursor: pointer;
  transition: color var(--transition);
}
details summary:not([role]) {
  color: var(--accordion-close-summary-color);
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::marker {
  display: none;
}
details summary::-moz-list-bullet {
  list-style-type: none;
}
details summary::after {
  display: block;
  width: 1rem;
  height: 1rem;
  -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
  margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
  float: right;
  transform: rotate(-90deg);
  background-image: var(--icon-chevron);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
  transition: transform var(--transition);
}
details summary:focus {
  outline: none;
}
details summary:focus:not([role="button"]) {
  color: var(--accordion-active-summary-color);
}
details summary[role="button"] {
  width: 100%;
  text-align: left;
}
details summary[role="button"]::after {
  height: calc(1rem * var(--line-height, 1.5));
  background-image: var(--icon-chevron-button);
}
details summary[role="button"]:not(.outline).contrast::after {
  background-image: var(--icon-chevron-button-inverse);
}
details[open] > summary {
  margin-bottom: calc(var(--spacing));
}
details[open] > summary:not([role]):not(:focus) {
  color: var(--accordion-open-summary-color);
}
details[open] > summary::after {
  transform: rotate(0);
}

[dir="rtl"] details summary {
  text-align: right;
}
[dir="rtl"] details summary::after {
  float: left;
  background-position: left center;
}

/**
 * Card (<article>)
 */
article {
  margin: var(--block-spacing-vertical) 0;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
  border-radius: var(--border-radius);
  background: var(--card-background-color);
  box-shadow: var(--card-box-shadow);
}
article > header,
article > footer {
  margin-right: calc(var(--block-spacing-horizontal) * -1);
  margin-left: calc(var(--block-spacing-horizontal) * -1);
  padding: calc(var(--block-spacing-vertical) * 0.66)
    var(--block-spacing-horizontal);
  background-color: var(--card-sectionning-background-color);
}
article > header {
  margin-top: calc(var(--block-spacing-vertical) * -1);
  margin-bottom: var(--block-spacing-vertical);
  border-bottom: var(--border-width) solid var(--card-border-color);
  border-top-right-radius: var(--border-radius);
  border-top-left-radius: var(--border-radius);
}
article > footer {
  margin-top: var(--block-spacing-vertical);
  margin-bottom: calc(var(--block-spacing-vertical) * -1);
  border-top: var(--border-width) solid var(--card-border-color);
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

/**
 * Modal (<dialog>)
 */
#mount {
  --scrollbar-width: 0px;
}

dialog {
  display: flex;
  z-index: 999;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: inherit;
  min-width: 100%;
  height: inherit;
  min-height: 100%;
  padding: var(--spacing);
  border: 0;
  -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
  backdrop-filter: var(--modal-overlay-backdrop-filter);
  background-color: var(--modal-overlay-background-color);
  color: var(--color);
}
dialog article {
  max-height: calc(100vh - var(--spacing) * 2);
  overflow: auto;
}
@media (min-width: 576px) {
  dialog article {
    max-width: 510px;
  }
}
@media (min-width: 768px) {
  dialog article {
    max-width: 700px;
  }
}
dialog article > header,
dialog article > footer {
  padding: calc(var(--block-spacing-vertical) * 0.5)
    var(--block-spacing-horizontal);
}
dialog article > header .close {
  margin: 0;
  margin-left: var(--spacing);
  float: right;
}
dialog article > footer {
  text-align: right;
}
dialog article > footer [role="button"] {
  margin-bottom: 0;
}
dialog article > footer [role="button"]:not(:first-of-type) {
  margin-left: calc(var(--spacing) * 0.5);
}
dialog article p:last-of-type {
  margin: 0;
}
dialog article .close {
  display: block;
  width: 1rem;
  height: 1rem;
  margin-top: calc(var(--block-spacing-vertical) * -0.5);
  margin-bottom: var(--typography-spacing-vertical);
  margin-left: auto;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}
dialog article .close:is([aria-current], :hover, :active, :focus) {
  opacity: 1;
}
dialog:not([open]),
dialog[open="false"] {
  display: none;
}

.modal-is-open {
  padding-right: var(--scrollbar-width, 0px);
  overflow: hidden;
  pointer-events: none;
}
.modal-is-open dialog {
  pointer-events: auto;
}

:where(.modal-is-opening, .modal-is-closing) dialog,
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-duration: 0.2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
:where(.modal-is-opening, .modal-is-closing) dialog {
  animation-duration: 0.8s;
  animation-name: modal-overlay;
}
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-delay: 0.2s;
  animation-name: modal;
}

.modal-is-closing dialog,
.modal-is-closing dialog > article {
  animation-delay: 0s;
  animation-direction: reverse;
}

@keyframes modal-overlay {
  from {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background-color: transparent;
  }
}
@keyframes modal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
}
/**
 * Nav
 */
:where(nav li)::before {
  float: left;
  content: "​";
}

nav,
nav ul {
  display: flex;
}

nav {
  justify-content: space-between;
}
nav ol,
nav ul {
  align-items: center;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
nav ol:first-of-type,
nav ul:first-of-type {
  margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav ol:last-of-type,
nav ul:last-of-type {
  margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav li {
  display: inline-block;
  margin: 0;
  padding: var(--nav-element-spacing-vertical)
    var(--nav-element-spacing-horizontal);
}
nav li > * {
  --spacing: 0;
}
nav :where(a, [role="link"]) {
  display: inline-block;
  margin: calc(var(--nav-link-spacing-vertical) * -1)
    calc(var(--nav-link-spacing-horizontal) * -1);
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
  border-radius: var(--border-radius);
  text-decoration: none;
}
nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
  text-decoration: none;
}
nav[aria-label="breadcrumb"] {
  align-items: center;
  justify-content: start;
}
nav[aria-label="breadcrumb"] ul li:not(:first-child) {
  -webkit-margin-start: var(--nav-link-spacing-horizontal);
  margin-inline-start: var(--nav-link-spacing-horizontal);
}
nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  position: absolute;
  width: calc(var(--nav-link-spacing-horizontal) * 2);
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
  content: "/";
  color: var(--muted-color);
  text-align: center;
}
nav[aria-label="breadcrumb"] a[aria-current] {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
  pointer-events: none;
}
nav [role="button"] {
  margin-right: inherit;
  margin-left: inherit;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}

aside nav,
aside ol,
aside ul,
aside li {
  display: block;
}
aside li {
  padding: calc(var(--nav-element-spacing-vertical) * 0.5)
    var(--nav-element-spacing-horizontal);
}
aside li a {
  display: block;
}
aside li [role="button"] {
  margin: inherit;
}

[dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  content: "\\";
}

/**
 * Progress
 */
progress {
  display: inline-block;
  vertical-align: baseline;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  margin-bottom: calc(var(--spacing) * 0.5);
  overflow: hidden;
  border: 0;
  border-radius: var(--border-radius);
  background-color: var(--progress-background-color);
  color: var(--progress-color);
}
progress::-webkit-progress-bar {
  border-radius: var(--border-radius);
  background: none;
}
progress[value]::-webkit-progress-value {
  background-color: var(--progress-color);
}
progress::-moz-progress-bar {
  background-color: var(--progress-color);
}
@media (prefers-reduced-motion: no-preference) {
  progress:indeterminate {
    background: var(--progress-background-color)
      linear-gradient(
        to right,
        var(--progress-color) 30%,
        var(--progress-background-color) 30%
      )
      top left/150% 150% no-repeat;
    animation: progress-indeterminate 1s linear infinite;
  }
  progress:indeterminate[value]::-webkit-progress-value {
    background-color: transparent;
  }
  progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  }
}

@media (prefers-reduced-motion: no-preference) {
  [dir="rtl"] progress:indeterminate {
    animation-direction: reverse;
  }
}

@keyframes progress-indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
/**
 * Dropdown ([role="list"])
 */
details[role="list"],
li[role="list"] {
  position: relative;
}

details[role="list"] summary + ul,
li[role="list"] > ul {
  display: flex;
  z-index: 99;
  position: absolute;
  top: auto;
  right: 0;
  left: 0;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: var(--border-width) solid var(--dropdown-border-color);
  border-radius: var(--border-radius);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  background-color: var(--dropdown-background-color);
  box-shadow: var(--card-box-shadow);
  color: var(--dropdown-color);
  white-space: nowrap;
}
details[role="list"] summary + ul li,
li[role="list"] > ul li {
  width: 100%;
  margin-bottom: 0;
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  list-style: none;
}
details[role="list"] summary + ul li:first-of-type,
li[role="list"] > ul li:first-of-type {
  margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li:last-of-type,
li[role="list"] > ul li:last-of-type {
  margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li a,
li[role="list"] > ul li a {
  display: block;
  margin: calc(var(--form-element-spacing-vertical) * -0.5)
    calc(var(--form-element-spacing-horizontal) * -1);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  overflow: hidden;
  color: var(--dropdown-color);
  text-decoration: none;
  text-overflow: ellipsis;
}
details[role="list"] summary + ul li a:hover,
li[role="list"] > ul li a:hover {
  background-color: var(--dropdown-hover-background-color);
}

details[role="list"] summary::after,
li[role="list"] > a::after {
  display: block;
  width: 1rem;
  height: calc(1rem * var(--line-height, 1.5));
  -webkit-margin-start: 0.5rem;
  margin-inline-start: 0.5rem;
  float: right;
  transform: rotate(0deg);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
}

details[role="list"] {
  padding: 0;
  border-bottom: none;
}
details[role="list"] summary {
  margin-bottom: 0;
}
details[role="list"] summary:not([role]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--form-element-border-color);
  border-radius: var(--border-radius);
  background-color: var(--form-element-background-color);
  color: var(--form-element-placeholder-color);
  line-height: inherit;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
details[role="list"] summary:not([role]):active,
details[role="list"] summary:not([role]):focus {
  border-color: var(--form-element-active-border-color);
  background-color: var(--form-element-active-background-color);
}
details[role="list"] summary:not([role]):focus {
  box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}
details[role="list"][open] summary {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
details[role="list"][open] summary::before {
  display: block;
  z-index: 1;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: none;
  content: "";
  cursor: default;
}

nav details[role="list"] summary,
nav li[role="list"] a {
  display: flex;
  direction: ltr;
}

nav details[role="list"] summary + ul,
nav li[role="list"] > ul {
  min-width: -moz-fit-content;
  min-width: fit-content;
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul li a,
nav li[role="list"] > ul li a {
  border-radius: 0;
}

nav details[role="list"] summary,
nav details[role="list"] summary:not([role]) {
  height: auto;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}
nav details[role="list"][open] summary {
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul {
  margin-top: var(--outline-width);
  -webkit-margin-start: 0;
  margin-inline-start: 0;
}
nav details[role="list"] summary[role="link"] {
  margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
  line-height: var(--line-height);
}
nav details[role="list"] summary[role="link"] + ul {
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
}

li[role="list"]:hover > ul,
li[role="list"] a:active ~ ul,
li[role="list"] a:focus ~ ul {
  display: flex;
}
li[role="list"] > ul {
  display: none;
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
  margin-inline-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
}
li[role="list"] > a::after {
  background-image: var(--icon-chevron);
}

/**
 * Loading ([aria-busy=true])
 */
[aria-busy="true"] {
  cursor: progress;
}

[aria-busy="true"]:not(input, select, textarea)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 0.1875em solid currentColor;
  border-radius: 1em;
  border-right-color: transparent;
  content: "";
  vertical-align: text-bottom;
  vertical-align: -0.125em;
  animation: spinner 0.75s linear infinite;
  opacity: var(--loading-spinner-opacity);
}
[aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
  margin-right: calc(var(--spacing) * 0.5);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) * 0.5);
  margin-inline-end: calc(var(--spacing) * 0.5);
}
[aria-busy="true"]:not(input, select, textarea):empty {
  text-align: center;
}

button[aria-busy="true"],
input[type="submit"][aria-busy="true"],
input[type="button"][aria-busy="true"],
input[type="reset"][aria-busy="true"],
a[aria-busy="true"] {
  pointer-events: none;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/**
 * Tooltip ([data-tooltip])
 */
[data-tooltip] {
  position: relative;
}
[data-tooltip]:not(a, button, input) {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}
[data-tooltip][data-placement="top"]::before,
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::before,
[data-tooltip]::after {
  display: block;
  z-index: 99;
  position: absolute;
  bottom: 100%;
  left: 50%;
  padding: 0.25rem 0.5rem;
  overflow: hidden;
  transform: translate(-50%, -0.25rem);
  border-radius: var(--border-radius);
  background: var(--tooltip-background-color);
  content: attr(data-tooltip);
  color: var(--tooltip-color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
}
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::after {
  padding: 0;
  transform: translate(-50%, 0rem);
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-left: 0.3rem solid transparent;
  border-radius: 0;
  background-color: transparent;
  content: "";
  color: var(--tooltip-background-color);
}
[data-tooltip][data-placement="bottom"]::before,
[data-tooltip][data-placement="bottom"]::after {
  top: 100%;
  bottom: auto;
  transform: translate(-50%, 0.25rem);
}
[data-tooltip][data-placement="bottom"]:after {
  transform: translate(-50%, -0.3rem);
  border: 0.3rem solid transparent;
  border-bottom: 0.3rem solid;
}
[data-tooltip][data-placement="left"]::before,
[data-tooltip][data-placement="left"]::after {
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translate(-0.25rem, -50%);
}
[data-tooltip][data-placement="left"]:after {
  transform: translate(0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-left: 0.3rem solid;
}
[data-tooltip][data-placement="right"]::before,
[data-tooltip][data-placement="right"]::after {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 100%;
  transform: translate(0.25rem, -50%);
}
[data-tooltip][data-placement="right"]:after {
  transform: translate(-0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-right: 0.3rem solid;
}
[data-tooltip]:focus::before,
[data-tooltip]:focus::after,
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
}
@media (hover: hover) and (pointer: fine) {
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::before,
  [data-tooltip]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::after {
    animation-name: tooltip-caret-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::before,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-bottom;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-name: tooltip-caret-slide-bottom;
  }
  [data-tooltip][data-placement="left"]:focus::before,
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::before,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-left;
  }
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-name: tooltip-caret-slide-left;
  }
  [data-tooltip][data-placement="right"]:focus::before,
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::before,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-right;
  }
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-name: tooltip-caret-slide-right;
  }
}
@keyframes tooltip-slide-top {
  from {
    transform: translate(-50%, 0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-top {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.25rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-bottom {
  from {
    transform: translate(-50%, -0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-bottom {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.5rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.3rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-left {
  from {
    transform: translate(0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-left {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.3rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-slide-right {
  from {
    transform: translate(-0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-right {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.3rem, -50%);
    opacity: 1;
  }
}

/**
 * Accessibility & User interaction
 */
[aria-controls] {
  cursor: pointer;
}

[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}

[aria-hidden="false"][hidden] {
  display: initial;
}

[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation;
}

[dir="rtl"] {
  direction: rtl;
}

/**
* Reduce Motion Features
*/
@media (prefers-reduced-motion: reduce) {
  *:not([aria-busy="true"]),
  :not([aria-busy="true"])::before,
  :not([aria-busy="true"])::after {
    background-attachment: initial !important;
    animation-duration: 1ms !important;
    animation-delay: -1ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}

#mount#mount {
  /* --primary: rgb(227, 59, 126); */
  --primary: #ea4c89;
  --primary-hover: #f082ac;
  --icon-xia: url("data:image/svg+xml,%3Csvg%20width%3D%2222%22%20height%3D%2214%22%20viewBox%3D%220%200%2022%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M9.4392%2013.1554L1.18789%203.96259C0.85064%203.5869%200.664086%203.09986%200.664062%202.59501C0.664062%201.46382%201.58164%200.546875%202.71353%200.546875H19.2161C19.7212%200.546847%2020.2085%200.733262%2020.5846%201.07037C21.4272%201.82567%2021.4976%203.12055%2020.7418%203.96256L12.4905%2013.1554C12.441%2013.2106%2012.3885%2013.263%2012.3333%2013.3124C11.4907%2014.0678%2010.195%2013.9974%209.4392%2013.1554Z%22%20fill%3D%22%23666666%22%2F%3E%3C%2Fsvg%3E");

}

li.select-link.select-link:hover > ul {
  display: none;
}
li.select-link.select-link > ul {
  display: none;
}
li.select-link.select-link a:focus ~ ul {
  display: none;
}

li.select-link.select-link a:active ~ ul {
  display: none;
}
li.select-link-active.select-link-active > ul {
  display: flex;
}
li.select-link-active.select-link-active:hover > ul {
  display: flex;
}

li.select-link-active.select-link-active a:focus ~ ul {
  display: flex;
}

li.select-link-active.select-link-active a:active ~ ul {
  display: flex;
}
ul.select-link-ul.select-link-ul {
  right: 0px;
  left: auto;
}

a.select-link-selected {
  background-color: var(--primary-focus);
}
.immersive-translate-no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

/* li[role="list"].no-arrow > a::after { */
/*   background-image: none; */
/*   width: 0; */
/*   color: var(--color); */
/* } */
li[role="list"].no-arrow {
  margin-left: 8px;
  padding-right: 0;
}
li[role="list"] > a::after {
  -webkit-margin-start: 0.2rem;
  margin-inline-start: 0.2rem;
}

li[role="list"].no-arrow > a,
li[role="list"].no-arrow > a:link,
li[role="list"].no-arrow > a:visited {
  color: var(--secondary);
}

select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 4px;
  max-width: 128px;
  overflow: hidden;
  color: var(--primary);
  font-size: 14px;
  border: none;
  padding: 0;
  padding-right: 20px;
  padding-left: 8px;
  text-overflow: ellipsis;
  color: var(--color);

}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.muted {
  color: var(--muted-color);
}

.select.button-select {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
  cursor: pointer;
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 16px;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
  -webkit-appearance: button;
  margin: 0;
  margin-bottom: 0px;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

html {
  font-size: 17px;
  --font-size: 17px;
}

body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}
.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}
.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}
#mount {
  min-width: 268px;
}
.main-button {
  font-size: 14px;
  vertical-align: middle;
  border-radius: 8px;
}

.pt-4 {
  padding-top: 24px;
}
.p-2 {
  padding: 8px;
}
.pl-5 {
  padding-left: 48px;
}
.p-0 {
  padding: 0;
}
.pl-2 {
  padding-left: 8px;
}
.pl-4 {
  padding-left: 24px;
}
.pt-2 {
  padding-top: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pr-5 {
  padding-right: 48px;
}
.text-sm {
  font-size: 14px;
}
.w-full {
  width: 100%;
}

.flex {
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-end {
  justify-content: flex-end;
}
.flex-grow {
  flex-grow: 1;
}
.justify-between {
  justify-content: space-between;
}

.mb-2 {
  margin-bottom: 8px;
}
.inline-block {
  display: inline-block;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2-5 {
  padding-top: 6px;
  padding-bottom: 6px;
}
.mt-2 {
  margin-top: 8px;
}
.mt-0 {
  margin-top: 0;
}

.mb-1 {
  margin-bottom: 4px;
}
.ml-4 {
  margin-left: 24px;
}
.ml-3 {
  margin-left: 16px;
}
.ml-2 {
  margin-left: 8px;
}
.ml-1 {
  margin-left: 4px;
}
.mr-1 {
  margin-right: 4px;
}
.mr-3 {
  margin-right: 16px;
}
.pl-3 {
  padding-left: 12px;
}
.pr-3 {
  padding-right: 12px;
}
.p-3 {
  padding: 12px;
}
.px-3{
  padding-left: 12px;
  padding-right: 12px;
}
.pt-3{
  padding-top: 12px;
}
.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.mt-4 {
  margin-top: 24px;
}
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.left-auto {
  left: auto !important;
}

.max-h-28 {
  max-height: 112px;
}
.max-h-30 {
  max-height: 120px;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.text-xs {
  font-size: 12px;
}

.flex-1 {
  flex: 1;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.flex-2 {
  flex: 2;
}
.mt-3 {
  margin-top: 16px;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-baseline {
  align-items: baseline;
}

.my-5 {
  margin-top: 48px;
  margin-bottom: 48px;
}
.my-4 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.pt-3 {
  padding-top: 12px;
}
.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}
.pt-2 {
  padding-top: 8px;
}
.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}
.pt-1 {
  padding-top: 4px;
}
.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}
.pb-2 {
  padding-bottom: 8px;
}
.justify-end {
  justify-content: flex-end;
}
.w-auto {
  width: auto;
}


select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex:1;
  overflow: hidden;
  color: var(--primary);
  font-size: 14px;
  border: none;
  border-radius: 8px;
  padding: 6px 24px 6px 16px;
  background-color: #F5F7F9;
  background-position: center right 12px;
  background-size: 8px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--color);

}
.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
}
.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}
@media (prefers-color-scheme: dark) {
  select.min-select {
    background-color: #141e26;
  }
}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}
.popup-footer  {
  background-color: #F5F7F9;
}



.clickable {
  cursor: pointer;
}

.close {
    cursor: pointer;
    width: 16px;
    height: 16px;
    background-image: var(--icon-close);
    background-position: center;
    background-size: auto 1rem;
    background-repeat: no-repeat;
    opacity: 0.5;
    transition: opacity var(--transition);
}

.padding-two-column {
  padding-left: 40px;
  padding-right: 40px;
}

.muted {
  color: #999;
}
.text-label {
  color: #6b7280;
}




select.text-label {
  color: #6b7280;
}
.display-none {
  display: none;
}


/* dark use #18232c */

@media (prefers-color-scheme: dark) {
  .popup-footer  {
    background-color: #141e26;
  }

  .text-label{
    color: #9ca3af;
  }
select.text-label {

    color: #9ca3af;
}
}

html {
  font-size: 17px;
}
.immersive-translate-popup-container {
  position: fixed;
  padding: 0;
  z-index: 2147483647;
  right: 0;
  top: 335px;
  width: 36px;
}
.immersive-translate-popup-btn {
  display: inline-block;
  background-color: #ea4c89;
  font-size: 18px;
  opacity: 0.5;
  width: 36px;
  height: 36px;
  border-radius: 100%;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
  border: none;
  padding: 0;
}
.immersive-translate-popup-btn > svg {
}
#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}
</style><div><div id="immersive-translate-popup-container" class="immersive-translate-popup-container" style="right:0px;top:335px;">
  <button id="immersive-translate-popup-btn" class="immersive-translate-popup-btn" style="opacity: 0.4; transform: translateX(40%);">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
      <path fill="none" d="M0 0h24v24H0z"></path>
      <path d="M5 15v2a2 2 0 0 0 1.85 1.995L7 19h3v2H7a4 4 0 0 1-4-4v-2h2zm13-5l4.4 11h-2.155l-1.201-3h-4.09l-1.199 3h-2.154L16 10h2zm-1 2.885L15.753 16h2.492L17 12.885zM8 2v2h4v7H8v3H6v-3H2V4h4V2h2zm9 1a4 4 0 0 1 4 4v2h-2V7a2 2 0 0 0-2-2h-3V3h3zM6 6H4v3h2V6zm4 0H8v3h2V6z" fill="rgba(255,255,255,1)"></path>
    </svg>
  </button>
  <div class="immersive-translate-popup-mount" id="mount"></div>
</div>
</div></template></div></html>