// 格式化时间
function formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  }
  
  // JWT 解码函数
  function decodeJWT(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (e) {
      console.error('JWT解码错误:', e);
      return null;
    }
  }
  
  // 元素可见性检测
  function isElementVisible(element) {
    const isHidden = (el) =>
      el.style.display === 'none' ||
      el.style.visibility === 'hidden' ||
      el.style.opacity === '0' ||
      el.style.value === 'NA';
    let current = element;
    while (current) {
      if (isHidden(current)) return false;
      current = current.parentElement;
    }
    return true;
  }
  
  // 提交元素检测
  function isSubmitElement(element) {
    const submitKeywords = ['submit', 'next', 'continue'];
    const tag = element.tagName.toLowerCase();
    const type = (element.getAttribute('type') || '').toLowerCase();
    const id = (element.id || '').toLowerCase();
    const className = (element.className || '').toLowerCase();
    const role = element.getAttribute('role');
    const isInputType = tag === 'input' && (type === 'submit' || type === 'button');
    const isButtonType = tag === 'button' && (type === 'submit' || !type);
    if (type === 'checkbox' || type === 'radio') return false;
    return (
      (isInputType || isButtonType) &&
      (submitKeywords.some(k => id.includes(k)) ||
       submitKeywords.some(k => className.includes(k)) ||
       role === 'button')
    );
  }
  
  // 判断是否有文本输入
  function hasTextInput(element) {
    const closestInput = element.closest('td, tr');
    const textInputs = closestInput?.querySelectorAll('input[type="text"], textarea');
    return textInputs && Array.from(textInputs).some(input => isElementVisible(input));
  }
  
  // 修改获取 surveyId 函数
  async function getSurveyIdFromUrl() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['capturedToken'], (data) => {
        if (data.capturedToken) {
          const decoded = decodeJWT(data.capturedToken);
          const surveyId = decoded?.survey_id || decoded?.surveyId || null;
          resolve(typeof surveyId === 'string' ? surveyId : null);
          chrome.storage.local.remove('capturedToken');
        } else {
          resolve(null);
        }
      });
    });
  }
  
  export {
    formatTime,
    decodeJWT,
    isElementVisible,
    isSubmitElement,
    hasTextInput,
    getSurveyIdFromUrl
  };