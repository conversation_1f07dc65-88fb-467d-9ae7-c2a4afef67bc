import re
from collections import Counter

def find_most_common_offer_name(file_path):
    offer_names = []
    offer_name_regex = re.compile(r'offer_name=([^&\n]+)')

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                match = offer_name_regex.search(line)
                if match:
                    offer_names.append(match.group(1))

        if not offer_names:
            print("No offer_name found in the file.")
            return

        counts = Counter(offer_names)
        most_common = counts.most_common(1)

        output_file = r'd:\PythonProject\socks5bp\result.txt'
        with open(output_file, 'w', encoding='utf-8') as out_f:
            if most_common:
                out_f.write(f"The most common offer_name is: {most_common[0][0]}\n")
                out_f.write(f"It appeared {most_common[0][1]} times.\n")
            else:
                out_f.write("Could not determine the most common offer_name.\n")

    except FileNotFoundError:
        with open(output_file, 'w', encoding='utf-8') as out_f:
            out_f.write(f"Error: File not found at {file_path}\n")
    except Exception as e:
        with open(output_file, 'w', encoding='utf-8') as out_f:
            out_f.write(f"An error occurred: {e}\n")

if __name__ == '__main__':
    file_to_process = r'd:\PythonProject\socks5bp\aff_id=1-621.txt'
    find_most_common_offer_name(file_to_process)