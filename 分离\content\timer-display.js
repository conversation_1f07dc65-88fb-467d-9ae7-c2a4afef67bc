import { formatTime, getSurveyIdFromUrl } from './utils.js';

// 更新计时器显示
async function updateTimerDisplay(timerDisplay) {
  try {
    chrome.storage.local.get(['activeSurveyId'], async (data) => {
      const currentSurveyId = (await getSurveyIdFromUrl()) || '空闲';
      const storedSurveyId = data.activeSurveyId;
      const surveyIdToUse = currentSurveyId !== '空闲' ? currentSurveyId : (storedSurveyId || '空闲');

      if (!chrome.runtime?.id) {
        console.error('扩展上下文已失效，无法发送消息');
        timerDisplay.textContent = `${surveyIdToUse}\n00:00\n扩展失效`;
        return;
      }

      chrome.runtime.sendMessage({ action: 'updateSurveyId', surveyId: surveyIdToUse }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('消息发送失败:', chrome.runtime.lastError.message);
          timerDisplay.textContent = `${surveyIdToUse}\n00:00\n错误`;
        } else if (response && response.surveyId) {
          timerDisplay.textContent = `${response.surveyId.toString()}\n${formatTime(response.elapsedTime)}`;
        }
      });
    });
  } catch (error) {
    console.error('更新计时器显示时出错:', error);
  }
}

// 启动定时更新
function startTimerUpdates(timerDisplay) {
  // 初始更新
  updateTimerDisplay(timerDisplay);
  
  // 定时更新计时器
  const intervalId = setInterval(() => {
    // 检查扩展上下文是否有效
    if (!chrome.runtime?.id) {
      console.error('扩展上下文已失效，停止计时器');
      timerDisplay.textContent = '空闲\n00:00\n扩展失效';
      clearInterval(intervalId); // 停止定时器
      return;
    }

    chrome.storage.local.get(['activeSurveyId'], async (data) => {
      const currentSurveyId = data.activeSurveyId || (await getSurveyIdFromUrl()) || '空闲';
      chrome.runtime.sendMessage({ action: 'getTimer', surveyId: currentSurveyId }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('获取计时失败:', chrome.runtime.lastError.message);
          timerDisplay.textContent = `${currentSurveyId}\n00:00\n错误`;
        } else if (response && response.elapsedTime !== undefined) {
          timerDisplay.textContent = `${response.surveyId?.toString() || '空闲'}\n${formatTime(response.elapsedTime)}`;
        }
      });
    });
  }, 1000);
  
  return intervalId;
}

// 设置消息监听
function setupMessageListener(timerDisplay) {
  chrome.runtime.onMessage.addListener((message) => {
    if (message.action === 'timerUpdate' && message.surveyId) {
      timerDisplay.textContent = `${message.surveyId.toString()}\n${formatTime(message.elapsedTime)}`;
    }
  });
}

export {
  updateTimerDisplay,
  startTimerUpdates,
  setupMessageListener
};