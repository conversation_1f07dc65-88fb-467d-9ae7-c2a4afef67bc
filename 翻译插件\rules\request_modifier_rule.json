[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "set", "value": "https://httpstat.us/429"}, {"header": "origin", "operation": "set", "value": "https://httpstat.us/429"}, {"header": "DNT", "operation": "set", "value": "1"}]}, "condition": {"urlFilter": "https://httpstat.us/429", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 2, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "set", "value": "https://www.deepl.com/"}, {"header": "origin", "operation": "set", "value": "https://www.deepl.com"}, {"header": "DNT", "operation": "set", "value": "1"}, {"header": "cookie", "operation": "remove"}]}, "condition": {"urlFilter": "https://www2.deepl.com/jsonrpc*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 200, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "set", "value": "https://www.deepl.com/"}, {"header": "origin", "operation": "set", "value": "chrome-extension://cofdbpoegempjloogbagkncekinflcnj"}, {"header": "DNT", "operation": "set", "value": "1"}]}, "condition": {"urlFilter": "https://api.deepl.com/jsonrpc*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 201, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "<PERSON><PERSON><PERSON>", "operation": "set", "value": "https://www.deepl.com/"}, {"header": "origin", "operation": "set", "value": "chrome-extension://cofdbpoegempjloogbagkncekinflcnj"}]}, "condition": {"urlFilter": "https://w.deepl.com/oidc/token", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 3, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "origin", "operation": "set", "value": "chrome-extension://lkjkfecdnfjopaeaibboihfkmhdjmanm"}]}, "condition": {"urlFilter": "https://transmart.qq.com/api/imt", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 4, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "origin", "operation": "set", "value": "chrome-extension://lkjkfecdnfjopaeaibboihfkmhdjmanm"}]}, "condition": {"urlFilter": "https://translate.volcengine.com/crx/translate/v1/", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 5, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"}]}, "condition": {"urlFilter": "https://edge.microsoft.com/translate/auth", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 6, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "User-Agent", "operation": "set", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"}]}, "condition": {"urlFilter": "https://api-edge.cognitive.microsofttranslator.com/translate", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 301, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://www.pixiv.net/"}]}, "condition": {"urlFilter": "https://i.pximg.net/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 302, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://newtoki341.com/"}]}, "condition": {"urlFilter": "https://img1.newtoki21*.org/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 303, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://newtoki341.com/"}]}, "condition": {"urlFilter": "https://img1.newtoki21.org/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 304, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://2.bp.blogspot.com"}]}, "condition": {"urlFilter": "https://2.bp.blogspot.com/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 305, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://japanreader.com"}]}, "condition": {"urlFilter": "https://japanreader.com/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 306, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://mangabuddy.com/"}]}, "condition": {"urlFilter": "https://s*.(mbbcdn.com|mbcdn*.org)/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 307, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://sl.mangafuna.xyz/"}]}, "condition": {"urlFilter": "https://sl.mangafuna.xyz/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 308, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://toonily.me"}]}, "condition": {"urlFilter": "https://s*.toonilycdnv2.xyz/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 309, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://readcomiconline.li"}]}, "condition": {"urlFilter": "https://*.whatsnew*.net/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 310, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://yymanhua.com"}]}, "condition": {"urlFilter": "https://image.yymanhua.com/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 311, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://klz9.com"}]}, "condition": {"urlFilter": "https://*.(klimv1|jfimv2).xyz/images*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 312, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://manhwato.com"}]}, "condition": {"urlFilter": "https://stcdn.manhwato.com/images/manga/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 313, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://www.comemh8.com"}]}, "condition": {"urlFilter": "https://*.kingwar.cn/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 314, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://weibo.com/"}]}, "condition": {"urlFilter": "https://*.sinaimg.cn/", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 315, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "origin", "operation": "set", "value": "http://127.0.0.1:11434"}]}, "condition": {"urlFilter": "http://*:11434", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 316, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "origin", "operation": "set", "value": "http://127.0.0.1:1234"}]}, "condition": {"urlFilter": "http://*:1234", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 317, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://jestful.net"}]}, "condition": {"urlFilter": "https://*.jfimv2.xyz/*", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}, {"id": 318, "priority": 1, "action": {"type": "modifyHeaders", "requestHeaders": [{"header": "referer", "operation": "set", "value": "https://viewer.championcross.jp"}]}, "condition": {"urlFilter": "https://viewer.championcross.jp", "resourceTypes": ["xmlhttprequest"], "domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["cfhamdkdjgoelclgllcoikbckcfpaklj", "bpoadfkcbjbfhfodiogcnhhhpibjhbnh", "amkbmndfnliijdhojkpoglbnaaahippg"]}}]