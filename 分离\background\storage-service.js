// 从存储加载数据
async function loadFromStorage() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['timers', 'activeSurveyId'], (data) => {
        resolve({
          timers: data.timers || {},
          activeSurveyId: data.activeSurveyId || null
        });
      });
    });
  }
  
  // 保存数据到存储
  async function saveToStorage(timers, activeSurveyId) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ timers, activeSurveyId }, () => {
        resolve(true);
      });
    });
  }
  
  // 获取自动提交设置
  async function getAutoSubmitEnabled() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['autoSubmitEnabled'], (data) => {
        resolve(data.autoSubmitEnabled || false);
      });
    });
  }
  
  // 设置自动提交设置
  async function setAutoSubmitEnabled(enabled) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ autoSubmitEnabled: enabled }, () => {
        resolve(true);
      });
    });
  }
  
  // 导出函数
  export {
    loadFromStorage,
    saveToStorage,
    getAutoSubmitEnabled,
    setAutoSubmitEnabled
  };