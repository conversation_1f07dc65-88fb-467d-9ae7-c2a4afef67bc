// 后台脚本 - 管理UA移除功能
class UARemover {
    constructor() {
        this.isEnabled = true;
        this.ruleId = 1;
        this.init();
    }

    async init() {
        // 从存储中恢复状态
        const result = await chrome.storage.local.get(['uaRemoverEnabled']);
        this.isEnabled = result.uaRemoverEnabled !== false; // 默认启用
        
        // 初始化规则
        await this.updateRules();
        
        // 监听存储变化
        chrome.storage.onChanged.addListener((changes) => {
            if (changes.uaRemoverEnabled) {
                this.isEnabled = changes.uaRemoverEnabled.newValue;
                this.updateRules();
            }
        });
        
        // 监听扩展图标点击
        chrome.action.onClicked.addListener(() => {
            this.toggle();
        });
    }

    async updateRules() {
        try {
            // 清除现有规则
            const existingRules = await chrome.declarativeNetRequest.getDynamicRules();
            const ruleIds = existingRules.map(rule => rule.id);
            if (ruleIds.length > 0) {
                await chrome.declarativeNetRequest.updateDynamicRules({
                    removeRuleIds: ruleIds
                });
            }

            if (this.isEnabled) {
                // 添加移除UA头的规则
                const rule = {
                    id: this.ruleId,
                    priority: 1,
                    action: {
                        type: "modifyHeaders",
                        requestHeaders: [
                            {
                                header: "user-agent",
                                operation: "remove"
                            }
                        ]
                    },
                    condition: {
                        urlFilter: "*",
                        resourceTypes: [
                            "main_frame",
                            "sub_frame", 
                            "xmlhttprequest",
                            "fetch",
                            "websocket"
                        ]
                    }
                };

                await chrome.declarativeNetRequest.updateDynamicRules({
                    addRules: [rule]
                });
                
                console.log('UA移除规则已启用');
            } else {
                console.log('UA移除规则已禁用');
            }
            
            // 更新图标状态
            this.updateIcon();
            
        } catch (error) {
            console.error('更新规则失败:', error);
        }
    }

    async toggle() {
        this.isEnabled = !this.isEnabled;
        await chrome.storage.local.set({ uaRemoverEnabled: this.isEnabled });
        await this.updateRules();
        
        // 通知popup更新状态
        try {
            chrome.runtime.sendMessage({
                action: 'statusChanged',
                enabled: this.isEnabled
            });
        } catch (error) {
            // popup可能未打开，忽略错误
        }
    }

    updateIcon() {
        const iconPath = this.isEnabled ? 'icons/icon32.png' : 'icons/icon32-disabled.png';
        const title = this.isEnabled ? 'UA Header Remover - 已启用' : 'UA Header Remover - 已禁用';
        
        chrome.action.setIcon({ path: iconPath });
        chrome.action.setTitle({ title: title });
    }
}

// 初始化UA移除器
const uaRemover = new UARemover();

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
        case 'getStatus':
            sendResponse({ enabled: uaRemover.isEnabled });
            break;
        case 'toggle':
            uaRemover.toggle();
            sendResponse({ enabled: uaRemover.isEnabled });
            break;
    }
});
