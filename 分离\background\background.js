import * as TimerService from './timer-service.js';
import * as StorageService from './storage-service.js';

// 初始化
async function initialize() {
  const data = await StorageService.loadFromStorage();
  TimerService.setTimers(data.timers);
  TimerService.setActiveSurveyId(data.activeSurveyId);
  console.log('后台脚本初始化，加载计时数据:', data.timers, '活跃 surveyId:', data.activeSurveyId);
}

// 初始化后台
initialize();

// 向所有标签页广播消息
function broadcastToAllTabs(message) {
  chrome.tabs.query({}, (tabs) => {
    for (const tab of tabs) {
      try {
        chrome.tabs.sendMessage(tab.id, message, (response) => {
          if (chrome.runtime.lastError) {
            console.log(`向标签页 ${tab.id} 发送消息失败:`, chrome.runtime.lastError.message);
          } else if (response) {
            console.log(`标签页 ${tab.id} 响应:`, response);
          }
        });
      } catch (e) {
        console.error(`向标签页 ${tab.id} 发送消息时出错:`, e);
      }
    }
  });
}

// 每秒更新计时并广播
setInterval(async () => {
  const activeSurveyId = TimerService.getActiveSurveyId();
  const timers = TimerService.getTimers();
  
  if (activeSurveyId && timers[activeSurveyId]) {
    const elapsedTime = TimerService.calculateElapsedTime(activeSurveyId);
    
    if (elapsedTime >= 60 * 60 * 1000) { // 60分钟重置
      TimerService.resetTimer(activeSurveyId);
      console.log(`计时超过60分钟，已重置: ${activeSurveyId}`);
    }
    
    // 使用广播函数向所有标签页发送更新
    broadcastToAllTabs({
      action: 'timerUpdate',
      surveyId: activeSurveyId,
      elapsedTime: elapsedTime
    });
  }
  
  // 备份到 local 存储
  await StorageService.saveToStorage(timers, activeSurveyId);
}, 1000);

// 监听内容脚本消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message);
  
  if (message.action === 'getTimer') {
    const surveyId = message.surveyId;
    TimerService.getTimer(surveyId);
    TimerService.setActiveSurveyId(surveyId.toString()); // 强制转换为字符串
    
    const elapsedTime = TimerService.calculateElapsedTime(surveyId);
    StorageService.saveToStorage(TimerService.getTimers(), TimerService.getActiveSurveyId());
    
    sendResponse({ 
      surveyId: TimerService.getActiveSurveyId(), 
      elapsedTime: elapsedTime 
    });
  } 
  else if (message.action === 'resetTimer') {
    const surveyId = message.surveyId;
    TimerService.resetTimer(surveyId);
    TimerService.setActiveSurveyId(surveyId);
    
    StorageService.saveToStorage(TimerService.getTimers(), TimerService.getActiveSurveyId());
    sendResponse({ success: true });
    
    // 重置计时器后立即广播更新
    broadcastToAllTabs({
      action: 'timerUpdate',
      surveyId: TimerService.getActiveSurveyId(),
      elapsedTime: 0
    });
  } 
  else if (message.action === 'updateSurveyId') {
    const surveyId = message.surveyId;
    TimerService.setActiveSurveyId(surveyId);
    TimerService.getTimer(surveyId);
    
    StorageService.saveToStorage(TimerService.getTimers(), TimerService.getActiveSurveyId());
    const elapsedTime = TimerService.calculateElapsedTime(surveyId);
    
    // 向所有标签页发送更新
    broadcastToAllTabs({
      action: 'timerUpdate',
      surveyId: TimerService.getActiveSurveyId(),
      elapsedTime: elapsedTime
    });
    
    sendResponse({ 
      surveyId: TimerService.getActiveSurveyId(), 
      elapsedTime: elapsedTime 
    });
  }
  
  return true; // 异步响应
});