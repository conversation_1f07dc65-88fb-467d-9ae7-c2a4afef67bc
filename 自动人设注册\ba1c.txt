(function() {
    'use strict';

    // ================== 防检测核心模块 ==================
    class AntiDetectionSystem {
        constructor() {
            this.lastPosition = { x: null, y: null };
            this.sequenceFactor = 1.0;
        }

        // 动态延迟生成器
        delay(type) {
            const profiles = {
                click: { min: 800, max: 1500 },
                input: { min: 250, max: 450 },
                navigation: { min: 3000, max: 5000 }
            };
            const { min, max } = profiles[type];
            let delay = this._baseDelay(min, max) * this.sequenceFactor;
            this.sequenceFactor = Math.max(0.7, this.sequenceFactor * 0.95);
            return delay;
        }

        _baseDelay(min, max) {
            const base = Math.floor(Math.random() * (max - min + 1)) + min;
            const noise = Math.round([...Array(12)].reduce(a => a + Math.random(), -6) * 50);
            return Math.max(100, base + noise);
        }

        // 人类鼠标移动模拟
        async moveMouse(target) {
            const targetRect = target.getBoundingClientRect();
            const targetX = targetRect.left + targetRect.width/2;
            const targetY = targetRect.top + targetRect.height/2;

            const controlPoints = [
                { x: this.lastPosition.x || window.innerWidth/2, y: this.lastPosition.y || window.innerHeight/2 },
                { x: targetX + Math.random()*40-20, y: targetY + Math.random()*40-20 },
                { x: targetX, y: targetY }
            ];

            await this._animateMovement(controlPoints);
            this.lastPosition = { x: targetX, y: targetY };
        }

        async _animateMovement(points) {
            return new Promise(resolve => {
                let step = 0;
                const animate = () => {
                    if (step >= 50) return resolve();

                    const t = step / 50;
                    const x = Math.pow(1-t, 2)*points[0].x +
                              2*(1-t)*t*points[1].x +
                              Math.pow(t, 2)*points[2].x;
                    const y = Math.pow(1-t, 2)*points[0].y +
                              2*(1-t)*t*points[1].y +
                              Math.pow(t, 2)*points[2].y;

                    document.dispatchEvent(new MouseEvent('mousemove', {
                        clientX: x,
                        clientY: y,
                        bubbles: true
                    }));

                    setTimeout(animate, 10 + Math.pow(t, 2)*40);
                    step++;
                };
                animate();
            });
        }

        // 高级点击模拟
        async performClick(element) {
            const rect = element.getBoundingClientRect();
            const variants = [
                { // 标准点击
                    events: ['mousedown', 'mouseup', 'click'],
                    x: rect.left + rect.width/2 + Math.random()*6-3,
                    y: rect.top + rect.height/2 + Math.random()*6-3
                },
                { // 长按点击
                    events: ['mousedown', 'mouseup', 'click'],
                    x: rect.left + rect.width/2 + Math.random()*8-4,
                    y: rect.top + rect.height/2 + Math.random()*8-4,
                    hold: 200 + Math.random()*200
                }
            ];

            const variant = variants[Math.floor(Math.random()*variants.length)];
            await this.moveMouse(element);

            for (const [index, eventType] of variant.events.entries()) {
                element.dispatchEvent(new MouseEvent(eventType, {
                    bubbles: true,
                    clientX: variant.x,
                    clientY: variant.y
                }));

                if (variant.hold && eventType === 'mousedown') {
                    await new Promise(r => setTimeout(r, variant.hold));
                }
                await new Promise(r => setTimeout(r, this.delay('click')/variant.events.length));
            }
        }
    }

    // ================== 注册功能模块 ==================
    class RegistrationFlow {
        constructor() {
            this.antiDetect = new AntiDetectionSystem();
            this.debugMode = false;
        }

        async execute() {
            try {
                const data = await this._getRegistrationData();
                await this._fillForm(data);
                await this._runGlobalValidation(); // 新增最终提交检查
            } catch (error) {
                this._safeLog('Registration error:', error);
            }
        }

        async _getRegistrationData() {
            if (typeof chrome !== 'undefined' && chrome.storage?.local) {
                return new Promise(resolve => {
                    chrome.storage.local.get(['registrationData'], result => {
                        resolve(result.registrationData || this._getFallbackData());
                    });
                });
            }
            return this._getFallbackData();
        }

        _getFallbackData() {
            return {
                randomName: 'John Doe',
                birthdayWithGender: '1990-01-01 男',
                email: '<EMAIL>',
                password: 'Test123!'
            };
        }

        async _fillForm(data) {
            await this._simulateHumanBehavior();
            await this._processFormSteps(data);
        }

        async _simulateHumanBehavior() {
            if (Math.random() > 0.5) {
                await this.antiDetect.moveMouse(document.body);
            }
            if (Math.random() > 0.7) {
                await this._clickRandomLocation();
            }
        }

        async _clickRandomLocation() {
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            document.dispatchEvent(new MouseEvent('click', { clientX: x, clientY: y }));
            await new Promise(r => setTimeout(r, this.antiDetect.delay('click')));
        }

        async _processFormSteps(data) {
            const steps = [
                async () => await this._fillEmail(data.email),
                async () => await this._fillPersonalInfo(data),
                async () => await this._setPassword(data.password)
            ];

            for (const step of steps) {
                await step();
                await new Promise(r => setTimeout(r, this.antiDetect.delay('navigation')));
            }
        }

        async _fillEmail(email) {
            const input = document.querySelector('#username');
            if (!input) return;

            await this.antiDetect.moveMouse(input);
            await this.antiDetect.performClick(input);
            await this._typeWithHumanRhythm(input, email);
            await this._submitForm();
        }

        async _fillPersonalInfo(data) {
            const [firstName, lastName] = data.randomName.split(' ');
            const [birthDate] = data.birthdayWithGender.split(' ');
            const [year, month, day] = birthDate.split('-');

            await this._fillName(firstName, lastName);
            await this._fillBirthDate(year, month, day);
            await this._submitForm();
        }

        async _fillName(firstName, lastName) {
            const firstNameInput = document.querySelector('input[name="firstName"]');
            const lastNameInput = document.querySelector('input[name="lastName"]');

            await this._typeWithHumanRhythm(firstNameInput, firstName);
            await this._typeWithHumanRhythm(lastNameInput, lastName);
        }

        async _fillBirthDate(year, month, day) {
            const inputs = {
                year: document.querySelector('input[name="year"]'),
                month: document.querySelector('input[name="month"]'),
                day: document.querySelector('input[name="day"]')
            };

            for (const [type, element] of Object.entries(inputs)) {
                if (!element) continue;
                await this.antiDetect.performClick(element);
                await this._typeWithHumanRhythm(element, { year, month, day }[type]);
            }
        }

        async _setPassword(password) {
            const passwordInput = document.querySelector('input[name="password"]');
            const termsCheckbox = document.querySelector('input[type="checkbox"]');

            await this._typeWithHumanRhythm(passwordInput, password);
            if (termsCheckbox && !termsCheckbox.checked) {
                await this.antiDetect.performClick(termsCheckbox);
                await new Promise(r => setTimeout(r, this.antiDetect.delay('click')));
            }
        }


     async _runGlobalValidation() {
        // 触发表单的提交事件
        const form = document.querySelector('form');
        if (form) {
            form.dispatchEvent(new Event('submit', {
                bubbles: true,
                cancelable: true
            }));

            // 特殊处理Angular表单
            if ('ng' in window) {
                const $form = angular.element(form);
                const $ctrl = $form.controller('form');
                if ($ctrl) {
                    $ctrl.$setSubmitted();
                    $ctrl.$validate();
                }
            }
        }

        // 等待异步验证（最多3秒）
        const start = Date.now();
        while (Date.now() - start < 3000) {
            if (!document.querySelector('.loading-indicator')) break;
            await new Promise(r => setTimeout(r, 100));
        }
    }

    async _typeWithHumanRhythm(element, text) {
        // 先触发focus事件
        element.dispatchEvent(new Event('focus', { bubbles: true }));
        await new Promise(r => setTimeout(r, this.antiDetect.delay('input')));

        for (const char of text) {
            element.value += char;
            // 触发完整事件链
            const events = [
                'keydown', 'keypress', 'input',
                'keyup', 'compositionupdate'
            ];
            events.forEach(event => {
                element.dispatchEvent(new Event(event, {
                    bubbles: true,
                    composed: true  // 重要！用于Shadow DOM穿透
                }));
            });

            // 随机触发额外事件（模拟真实输入抖动）
            if(Math.random() > 0.8) {
                element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
            }

            await new Promise(r => setTimeout(r, this.antiDetect.delay('input')));
        }

        // 增强失焦事件序列
        const blurEvents = ['change', 'blur', 'focusout'];
        blurEvents.forEach(event => {
            element.dispatchEvent(new Event(event, {
                bubbles: true,
                composed: true
            }));
        });

        // 特殊处理React表单
        if (this._isReactForm(element)) {
            this._triggerReactValidation(element);
        }

        // 强制更新CSS验证状态
        await this._updateValidationState(element);
    }

            // React表单检测
    _isReactForm(element) {
        return '__reactProps' in element ||
               element.closest('[data-reactroot]');
    }

    // 触发React内部事件
    _triggerReactValidation(element) {
        const reactKey = Object.keys(element).find(k => k.startsWith('__reactEventHandlers'));
        if (reactKey) {
            const props = element[reactKey];
            if (props.onChange) {
                props.onChange({ target: element });
            }
            if (props.onBlur) {
                props.onBlur({ target: element });
            }
        }
    }

     async _updateValidationState(element) {
        // 添加验证类（根据常见框架模式）
        const addValidationClass = () => {
            element.classList.remove('invalid');
            element.classList.add('valid', 'dirty', 'touched');
            element.setAttribute('data-valid', 'true');
        };

        // 分阶段触发
        await new Promise(r => requestAnimationFrame(r));
        addValidationClass();
        await new Promise(r => setTimeout(r, 50));
        addValidationClass();
    }


        async _submitForm() {
            const submitButton = document.querySelector('button[type="submit"]:not([disabled])');
            if (submitButton) {
                await this.antiDetect.performClick(submitButton);
            }
        }


        _safeLog(...args) {
            if (this.debugMode && Math.random() > 0.7) {
                console.log('[DEBUG]', ...args);
            }
        }
    }

    // ================== 主执行逻辑 ==================
    const isRegistrationPage = () =>
        /\/registration(?:\/.*|\?.*)?$/.test(window.location.href);

    const init = async () => {
        if (!isRegistrationPage()) return;

        // 防控制台检测
        window.console = new Proxy(console, {
            get: (target, prop) => ['log','error','warn'].includes(prop)
                ? (...args) => Math.random() > 0.3 && target[prop](...args)
                : target[prop]
        });

        // 启动注册流程
        await new RegistrationFlow().execute();
    };

    if (document.readyState === 'complete') {
        init();
    } else {
        window.addEventListener('load', init);
    }

    // 噪声事件生成器
    setInterval(() => {
        if (Math.random() > 0.8) {
            document.dispatchEvent(new MouseEvent('mousemove', {
                clientX: Math.random() * window.innerWidth,
                clientY: Math.random() * window.innerHeight,
                bubbles: true
            }));
        }
    }, 5000);
})();
