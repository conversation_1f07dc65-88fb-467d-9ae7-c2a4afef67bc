(()=>{var g=chrome;var d="https://api.nopecha.com",r="https://www.nopecha.com",h="https://developers.nopecha.com",T={doc:{url:h,automation:{url:`${h}/guides/extension_advanced/#automation-build`}},api:{url:d,recognition:{url:`${d}/recognition`},status:{url:`${d}/status`}},www:{url:r,annoucement:{url:`${r}/json/announcement.json`},demo:{url:`${r}/captcha`,recaptcha:{url:`${r}/captcha/recaptcha`},funcaptcha:{url:`${r}/captcha/funcaptcha`},awscaptcha:{url:`${r}/captcha/awscaptcha`},textcaptcha:{url:`${r}/captcha/textcaptcha`},turnstile:{url:`${r}/captcha/turnstile`},perimeterx:{url:`${r}/captcha/perimeterx`},geetest:{url:`${r}/captcha/geetest`},lemincaptcha:{url:`${r}/captcha/lemincaptcha`}},manage:{url:`${r}/manage`},pricing:{url:`${r}/pricing`},setup:{url:`${r}/setup`}},discord:{url:`${r}/discord`},github:{url:`${r}/github`,release:{url:`${r}/github/release`}}};function f(e){let t=("60a8b3778b5b01f87ccc8129cd88bf0f6ec61feb879c88908365771cfcadc232"+e).split("").map(o=>o.charCodeAt(0));return y(t)}var b=new Uint32Array(256);for(let e=256;e--;){let t=e;for(let o=8;o--;)t=t&1?3988292384^t>>>1:t>>>1;b[e]=t}function y(e){let t=-1;for(let o of e)t=t>>>8^b[t&255^o];return(t^-1)>>>0}async function x(e,t){let o=""+[+new Date,performance.now(),Math.random()],[s,n]=await new Promise(c=>{g.runtime.sendMessage([o,e,...t],c)});if(s===f(o))return n}function p(e){if(document.readyState!=="loading")setTimeout(e,0);else{let t;t=()=>{removeEventListener("DOMContentLoaded",t),e()},addEventListener("DOMContentLoaded",t)}}[...document.body.children].forEach(e=>e.remove());function a(e,t,o={}){let s=document.createElement(e);return o&&Object.entries(o).forEach(([n,c])=>s[n]=c),t.appendChild(s),s}function $(){a("h1",document.body,{innerText:"Invalid URL."}),a("h2",document.body,{innerText:"Please set the URL hash and reload the page."}),a("p",document.body,{innerText:"Example: https://nopecha.com/setup#TESTKEY123"})}function w(e){return/^(true|false)$/.test(e)?e==="true":/^\d+$/.test(e)?+e:e}function _(){let e="NopeCHA Settings Import",t=document.querySelector("title");document.title!==e&&t&&(t.innerText=e);let o=document.location.hash.substring(1);if(!o)return $();let s=o.split("|"),n=Object.fromEntries(s.map(i=>i.includes("=")?i.split("="):["key",i]).map(([i,l])=>[i,w(l)]));if("disabled_hosts"in n){let i=""+n.disabled_hosts;i===""?n.disabled_hosts=[]:decodeURIComponent(i).startsWith("[")?n.disabled_hosts=JSON.parse(decodeURIComponent(i)):n.disabled_hosts=i.split(",")}"key"in n&&n.key.includes(",")&&(n.keys=n.key.split(","),delete n.key),console.log(n),a("h2",document.body,{innerText:"Imported following settings:"});let c=a("table",document.body),u=a("tr",c);a("th",u,{innerText:"Name"}),a("th",u,{innerText:"Value"}),Object.entries(n).forEach(([i,l])=>{let m=a("tr",c);a("td",m,{innerText:i}),a("td",m,{innerText:JSON.stringify(l)})}),a("h2",document.body,{innerText:"Import URL:"}),a("a",document.body,{innerText:location.href,href:location.href,style:"word-wrap: break-word"}),x("settings::update",[n])}p(_);})();
